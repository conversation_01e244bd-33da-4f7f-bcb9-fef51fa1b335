{"name": "ORM测试", "path": "/api/v1/project/ht1", "method": "GET", "description": "ORM测试", "group_id": 6, "datasource_id": 4, "table_name": "ZDY_VW_GET_ZYHT_LIST", "table_type": "view", "is_enabled": true, "is_public": false, "query_fields": ["id", "name", "status"], "required_fields": ["name"], "response_fields": ["id", "name", "status", "created_at"], "orm_model_config": {"model_name": "ZdyVwGetZyhtList", "table_name": "ZDY_VW_GET_ZYHT_LIST", "description": "ZDY_VW_GET_ZYHT_LIST 数据模型", "http_methods": ["GET"], "datasource_info": {"datasource_id": 4, "db_type": "mssql", "table_type": "view"}, "sqlalchemy_model": {"class_name": "ZdyVwGetZyhtList", "table_name": "ZDY_VW_GET_ZYHT_LIST", "fields": [{"name": "F_CODE", "original_name": "F_CODE", "python_name": "f_code", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "F_CREATOR_NAME", "original_name": "F_CREATOR_NAME", "python_name": "f_creator_name", "type": "n<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "F_CREATE_TIME", "original_name": "F_CREATE_TIME", "python_name": "f_create_time", "type": "datetime", "sqlalchemy_type": "DateTime", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": null, "scale": null}, {"name": "F_NAME", "original_name": "F_NAME", "python_name": "f_name", "type": "n<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "F_ZJE", "original_name": "F_ZJE", "python_name": "f_zje", "type": "decimal", "sqlalchemy_type": "Numeric(20, 2)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 20, "scale": 2}, {"name": "F_JCBJE", "original_name": "F_JCBJE", "python_name": "f_jcbje", "type": "decimal", "sqlalchemy_type": "Numeric(20, 2)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 20, "scale": 2}, {"name": "F_SE", "original_name": "F_SE", "python_name": "f_se", "type": "decimal", "sqlalchemy_type": "Numeric(20, 2)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 20, "scale": 2}, {"name": "YF_Name", "original_name": "YF_Name", "python_name": "y_f__name", "type": "n<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "F_TYPE", "original_name": "F_TYPE", "python_name": "f_type", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(8)", "nullable": false, "primary_key": false, "default": null, "comment": "", "length": 8, "precision": null, "scale": null}, {"name": "F_HTXS", "original_name": "F_HTXS", "python_name": "f_htxs", "type": "n<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(50)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 50, "precision": null, "scale": null}, {"name": "F_STATE", "original_name": "F_STATE", "python_name": "f_state", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(8)", "nullable": false, "primary_key": false, "default": null, "comment": "", "length": 8, "precision": null, "scale": null}, {"name": "F_ID", "original_name": "F_ID", "python_name": "f_id", "type": "decimal", "sqlalchemy_type": "Numeric(19)", "nullable": false, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 19, "scale": 0}, {"name": "HZHB_ID", "original_name": "HZHB_ID", "python_name": "hzhb_id", "type": "decimal", "sqlalchemy_type": "Numeric(19)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 19, "scale": 0}, {"name": "F_DEPT_ID", "original_name": "F_DEPT_ID", "python_name": "f_dept_id", "type": "decimal", "sqlalchemy_type": "Numeric(19)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 19, "scale": 0}, {"name": "F_ModuleCode", "original_name": "F_ModuleCode", "python_name": "f__module_code", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "HZHB_ModuleCode", "original_name": "HZHB_ModuleCode", "python_name": "h_z_h_b__module_code", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(255)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": 255, "precision": null, "scale": null}, {"name": "F_CGXM_ID", "original_name": "F_CGXM_ID", "python_name": "f_cgxm_id", "type": "decimal", "sqlalchemy_type": "Numeric(19)", "nullable": true, "primary_key": false, "default": null, "comment": "", "length": null, "precision": 19, "scale": 0}]}, "query_mapping": {"fuzzy_search_fields": ["F_CODE", "F_CREATOR_NAME", "F_NAME", "YF_Name", "F_TYPE"], "exact_match_fields": ["F_ZJE", "F_JCBJE", "F_SE"], "range_query_fields": ["F_CREATE_TIME", "F_ZJE", "F_JCBJE"], "default_sort": "F_CODE", "default_order": "desc", "allowed_sort_fields": ["F_CREATE_TIME", "F_CODE", "F_CREATOR_NAME", "F_NAME"]}, "response_config": {"exclude_fields": [], "date_format": "%Y-%m-%d %H:%M:%S", "include_total_count": true, "null_to_empty": false}}, "orm_model_name": "TestModel", "orm_relationships": {"belongs_to": [], "has_many": [], "has_one": []}, "parameter_config": {"pagination": {"enabled": true, "default_size": 10}, "sorting": {"enabled": true, "default_field": "id"}, "filtering": {"enabled": true, "fields": ["name", "status"]}}, "visual_config": {"form_layout": "vertical", "field_groups": [{"name": "基本信息", "fields": ["name", "status"]}, {"name": "时间信息", "fields": ["created_at", "updated_at"]}]}, "validation_rules": {"name": {"required": true, "min_length": 1, "max_length": 100}, "status": {"required": true, "enum": ["active", "inactive"]}}, "cache_duration": 300, "rate_limit": 100, "id": 5, "group_name": "项目系统", "datasource_name": "项目管理系统", "tags": [], "tag_names": [], "last_test_at": null, "test_status": null, "created_at": "2025-07-21T12:46:48", "updated_at": "2025-07-25T07:32:30", "created_by": null}