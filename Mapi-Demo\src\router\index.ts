import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

// 路由组件
const ComponentManagement = () => import('../views/ComponentManagement.vue')
const CanvasManagement = () => import('../views/CanvasManagement.vue')
const ReportManagement = () => import('../views/ReportManagement.vue')

// 定义路由
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/components'
  },
  {
    path: '/components',
    name: 'ComponentManagement',
    component: ComponentManagement,
    meta: {
      title: '组件管理'
    }
  },
  {
    path: '/canvas',
    name: 'CanvasManagement',
    component: CanvasManagement,
    meta: {
      title: '画布管理'
    }
  },
  {
    path: '/reports',
    name: 'ReportManagement',
    component: ReportManagement,
    meta: {
      title: '报表管理'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `报表BI系统 - ${to.meta.title}`
  } else {
    document.title = '报表BI系统'
  }
  next()
})

export default router