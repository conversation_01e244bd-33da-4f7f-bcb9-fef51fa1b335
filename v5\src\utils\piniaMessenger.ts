/**
 * 全局抽屉直接访问助手
 * 直接访问父框架的Pinia store，无需postMessage
 */

/**
 * 全局抽屉通信助手
 */
interface DrawerMessenger {
  showDrawer: (config: {
    title: string;
    component: string;
    props?: Record<string, any>;
    size?: string;
    isSecond?: boolean;
  }) => void;
  hideDrawer: (isSecond?: boolean) => void;
}

export const useGlobalDrawerMessenger = (): DrawerMessenger => {
  /**
   * 显示抽屉
   */
  const showDrawer = (config: {
    title: string;
    component: string;
    props?: Record<string, any>;
    size?: string;
    isSecond?: boolean;  // 是否为第二层抽屉
  }) => {
    try {
      // 直接访问父框架暴露的store和组件
      const parentStore = (window.parent as any)?.globalDrawerStore;
      const parentComponents = (window.parent as any)?.globalDrawerComponents;

      if (parentStore && parentComponents) {
        const componentInstance = parentComponents[config.component];

        if (componentInstance) {
          if (config.isSecond) {
            parentStore.showSecondDrawer({
              title: config.title,
              component: componentInstance,
              props: config.props,
              size: config.size
            });
            console.log('Second drawer shown via direct store access');
          } else {
            parentStore.showDrawer({
              title: config.title,
              component: componentInstance,
              props: config.props,
              size: config.size
            });
            console.log('Drawer shown via direct store access');
          }
        } else {
          console.error('Component not found:', config.component);
        }
      } else {
        console.error('Parent store or components not available');
      }
    } catch (error) {
      console.error('Error accessing parent store:', error);
    }
  };

  /**
   * 隐藏抽屉
   */
  const hideDrawer = (isSecond?: boolean) => {
    try {
      const parentStore = (window.parent as any)?.globalDrawerStore;

      if (parentStore) {
        // 获取当前抽屉标题，用于事件通知
        const currentTitle = isSecond ? parentStore.secondTitle : parentStore.title;

        if (isSecond) {
          parentStore.closeSecondDrawer();
        } else {
          parentStore.closeDrawer();
        }

        // 触发自定义事件，通知页面抽屉已关闭
        const event = new CustomEvent('drawerClosed', {
          detail: { title: currentTitle, isSecond }
        });

        // 在当前窗口和父窗口都触发事件
        window.dispatchEvent(event);
        if (window.parent && window.parent !== window) {
          window.parent.dispatchEvent(event);
        }

        // 备用方案：根据抽屉标题调用对应的刷新方法
        if (currentTitle) {
          try {
            let globalRefresh: Function | undefined;

            // 数据源已使用新的 PageRefresh 机制，禁用旧机制避免重复刷新
            // if (currentTitle.includes('数据源')) {
            //   globalRefresh = (window as any).refreshDataSourceList || (window.parent as any).refreshDataSourceList;
            // }
            // 接口分组已使用新的 PageRefresh 机制，禁用旧机制避免重复刷新
            // else if (currentTitle.includes('分组') || currentTitle.includes('接口分组')) {
            //   globalRefresh = (window as any).refreshInterfaceGroupList || (window.parent as any).refreshInterfaceGroupList;
            // }
            // 接口标签已使用新的 PageRefresh 机制，禁用旧机制避免重复刷新
            // if (currentTitle.includes('标签') || currentTitle.includes('接口标签')) {
            //   globalRefresh = (window as any).refreshInterfaceTagList || (window.parent as any).refreshInterfaceTagList;
            // } else if (currentTitle.includes('接口配置') || currentTitle.includes('接口')) {
            //   globalRefresh = (window as any).refreshInterfaceConfigList || (window.parent as any).refreshInterfaceConfigList;
            // }

            if (globalRefresh && typeof globalRefresh === 'function') {
              globalRefresh();
            }
          } catch (error) {
            // 静默处理错误
          }
        }

      } else {
        console.error('Parent store not available');
      }
    } catch (error) {
      console.error('Error accessing parent store:', error);
    }
  };

  return {
    showDrawer,
    hideDrawer
  };
};


