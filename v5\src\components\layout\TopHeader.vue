<!-- TopHeader 组件用于展示页面顶部的导航栏，包含侧边栏切换按钮、面包屑插槽、开发环境标识以及用户操作下拉菜单 -->
<template>
  <el-header>
    <div class="top-nav-container">
      <div class="left-group">
        <el-icon class="sidebar-toggle" :class="{ collapsed: sidebarStore.isCollapse }" @click="sidebarStore.toggleCollapse">
          <Fold />
        </el-icon>
        <slot name="breadcrumb"></slot>
      </div>
      <div class="user-actions">
        <el-tag type="success" size="small">开发环境</el-tag>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-icon><User /></el-icon> Admin
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item><el-icon><User /></el-icon>个人中心</el-dropdown-item>
              <el-dropdown-item><el-icon><Setting /></el-icon>系统设置</el-dropdown-item>
              <el-dropdown-item divided><el-icon><SwitchButton /></el-icon>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { User, Setting, SwitchButton, Fold } from '@element-plus/icons-vue';
import { useSidebarMenuStore } from '@/stores/sidebarMenuStore';

const sidebarStore = useSidebarMenuStore();
</script>

<style lang="scss" scoped>
/* 引入框架样式变量和混合宏 */
@use '@/assets/styles/framework.scss' as *;

/* TopHeader.vue 特有样式 */
.el-header {
  height: var(--header-height, 60px) !important;
  background: var(--header-light);
  padding: 0;
  border-bottom: 1px solid var(--border-color, #e4e7ed);
}

.top-nav-container {
  height: 100%;
  @include framework-flex-between;
  padding: 0 20px;
}

.left-group {
  @include framework-flex-center;
}

.sidebar-toggle {
  @include framework-icon-size-large;
  cursor: pointer;
  margin-right: 6px;
  margin-left: -3px; // 左移3px
  @include framework-text-primary;
  @include framework-transition-base;
}

.sidebar-toggle.collapsed {
  transform: rotate(180deg);
}

.user-actions {
  @include framework-flex-center;
  gap: 15px;
}
</style>