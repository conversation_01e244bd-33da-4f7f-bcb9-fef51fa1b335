"""
接口配置业务逻辑层
处理接口配置相关的业务逻辑
"""

import math
import json
from typing import Optional
from sqlalchemy.orm import Session
from app.config.interface.repositories.interface_config_repository import InterfaceConfigRepository
from app.config.interface.repositories.interface_group_repository import InterfaceGroupRepository
from app.config.interface.repositories.interface_tag_repository import InterfaceTagRepository
from app.config.datasource.repositories.data_source_repository import DataSourceRepository
from app.config.interface.schemas.interface_config_schema import (
    InterfaceConfigCreate,
    InterfaceConfigUpdate,
    InterfaceConfigResponse,
    InterfaceConfigListResponse
)
from app.shared.core.exception_handler import BusinessException, TechnicalException
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil


class InterfaceConfigService:
    """接口配置业务逻辑类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repository = InterfaceConfigRepository(db)
        self.group_repository = InterfaceGroupRepository(db)
        self.tag_repository = InterfaceTagRepository(db)
        self.datasource_repository = DataSourceRepository(db)
        LogUtil.debug("接口配置服务初始化", service="InterfaceConfigService")
    
    def get_interface_configs(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None,
        group_id: Optional[int] = None,
        method: Optional[str] = None,
        is_enabled: Optional[bool] = None
    ) -> InterfaceConfigListResponse:
        """
        获取接口配置列表
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词
            group_id: 分组ID过滤
            method: HTTP方法过滤
            is_enabled: 启用状态过滤
            
        Returns:
            接口配置列表响应
        """
        try:
            LogUtil.debug("开始获取接口配置列表", 
                         operation="get_interface_configs",
                         page=page, size=size, search=search,
                         group_id=group_id, method=method, is_enabled=is_enabled)
            
            # 参数验证
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )
            
            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )
            
            # 获取数据
            items, total = self.repository.get_list(page, size, search, group_id, method, is_enabled)
            
            # 转换为响应格式
            configs = []
            for item in items:
                config_response = self._convert_to_response(item)
                configs.append(config_response)
            
            # 计算总页数
            pages = math.ceil(total / size) if total > 0 else 0
            
            LogUtil.info("接口配置列表获取成功", 
                        operation="get_interface_configs",
                        total_count=total,
                        returned_count=len(configs),
                        page=page, size=size)
            
            return InterfaceConfigListResponse(
                items=configs,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_configs",
                    "error": str(e),
                    "params": {
                        "page": page, "size": size, "search": search,
                        "group_id": group_id, "method": method, "is_enabled": is_enabled
                    }
                }
            )
    
    def get_interface_config(self, config_id: int) -> InterfaceConfigResponse:
        """
        获取单个接口配置
        
        Args:
            config_id: 接口配置ID
            
        Returns:
            接口配置响应
        """
        try:
            LogUtil.debug("开始获取接口配置详情",
                         operation="get_interface_config",
                         config_id=config_id)
            
            config = self.repository.get_by_id(config_id)
            if not config:
                raise BusinessException(
                    user_message="接口配置不存在",
                    user_detail={"config_id": config_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 转换为响应格式
            config_response = self._convert_to_response(config)
            
            LogUtil.info("接口配置详情获取成功", 
                        operation="get_interface_config",
                        config_id=config_id,
                        name=config.name)
            
            return config_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_config",
                    "error": str(e),
                    "config_id": config_id
                }
            )
    
    def create_interface_config(self, config_data: InterfaceConfigCreate) -> InterfaceConfigResponse:
        """
        创建接口配置
        
        Args:
            config_data: 接口配置创建数据
            
        Returns:
            创建的接口配置响应
        """
        try:
            LogUtil.debug("开始创建接口配置", 
                         operation="create_interface_config",
                         name=config_data.name,
                         path=config_data.path,
                         method=config_data.method)
            
            # 验证关联数据是否存在
            self._validate_references(config_data.group_id, config_data.datasource_id, config_data.tags)
            
            # 检查路径+方法组合是否已存在
            if self.repository.check_path_method_exists(config_data.path, config_data.method.value):
                raise BusinessException(
                    user_message="接口路径和方法组合已存在",
                    user_detail={
                        "path": config_data.path,
                        "method": config_data.method.value,
                        "suggestion": "请使用不同的路径或方法"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 创建接口配置
            config = self.repository.create(config_data)
            
            # 转换为响应格式
            config_response = self._convert_to_response(config)
            
            LogUtil.info("接口配置创建成功", 
                        operation="create_interface_config",
                        config_id=config.id,
                        name=config.name,
                        path=config.path)
            
            return config_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "create_interface_config",
                    "error": str(e),
                    "config_data": config_data.dict()
                }
            )
    
    def update_interface_config(
        self, 
        config_id: int, 
        config_data: InterfaceConfigUpdate
    ) -> InterfaceConfigResponse:
        """
        更新接口配置
        
        Args:
            config_id: 接口配置ID
            config_data: 更新数据
            
        Returns:
            更新后的接口配置响应
        """
        try:
            LogUtil.debug("开始更新接口配置", 
                         operation="update_interface_config",
                         config_id=config_id)
            
            # 检查接口配置是否存在
            existing_config = self.repository.get_by_id(config_id)
            if not existing_config:
                raise BusinessException(
                    user_message="接口配置不存在",
                    user_detail={"config_id": config_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 验证关联数据是否存在
            self._validate_references(config_data.group_id, config_data.datasource_id, config_data.tags)
            
            # 检查路径+方法组合冲突
            if (config_data.path and config_data.method and 
                self.repository.check_path_method_exists(config_data.path, config_data.method.value, config_id)):
                raise BusinessException(
                    user_message="接口路径和方法组合已存在",
                    user_detail={
                        "path": config_data.path,
                        "method": config_data.method.value,
                        "suggestion": "请使用不同的路径或方法"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 更新接口配置
            updated_config = self.repository.update(config_id, config_data)
            
            # 转换为响应格式
            config_response = self._convert_to_response(updated_config)
            
            LogUtil.info("接口配置更新成功", 
                        operation="update_interface_config",
                        config_id=config_id,
                        name=updated_config.name)
            
            return config_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "update_interface_config",
                    "error": str(e),
                    "config_id": config_id,
                    "config_data": config_data.dict()
                }
            )
    
    def delete_interface_config(self, config_id: int) -> dict:
        """
        删除接口配置
        
        Args:
            config_id: 接口配置ID
            
        Returns:
            删除结果
        """
        try:
            LogUtil.debug("开始删除接口配置", 
                         operation="delete_interface_config",
                         config_id=config_id)
            
            # 检查接口配置是否存在
            existing_config = self.repository.get_by_id(config_id)
            if not existing_config:
                raise BusinessException(
                    user_message="接口配置不存在",
                    user_detail={"config_id": config_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 删除接口配置
            success = self.repository.delete(config_id)
            
            if success:
                LogUtil.info("接口配置删除成功", 
                            operation="delete_interface_config",
                            config_id=config_id,
                            name=existing_config.name)
                
                return {"message": "接口配置删除成功"}
            else:
                raise TechnicalException(
                    error_type=ErrorType.操作失败,
                    developer_detail={
                        "operation": "delete_interface_config",
                        "config_id": config_id,
                        "error": "删除操作返回失败"
                    }
                )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "delete_interface_config",
                    "error": str(e),
                    "config_id": config_id
                }
            )
    
    def _validate_references(self, group_id: Optional[int], datasource_id: Optional[int], tags: Optional[list[int]]):
        """验证关联数据是否存在"""
        if group_id:
            group = self.group_repository.get_by_id(group_id)
            if not group:
                raise BusinessException(
                    user_message="指定的接口分组不存在",
                    user_detail={"group_id": group_id},
                    error_type=ErrorType.资源未找到
                )
        
        if datasource_id:
            datasource = self.datasource_repository.get_by_id(datasource_id)
            if not datasource:
                raise BusinessException(
                    user_message="指定的数据源不存在",
                    user_detail={"datasource_id": datasource_id},
                    error_type=ErrorType.资源未找到
                )
        
        if tags:
            existing_tags = self.tag_repository.get_by_ids(tags)
            existing_tag_ids = [tag.id for tag in existing_tags]
            invalid_tag_ids = [tag_id for tag_id in tags if tag_id not in existing_tag_ids]
            if invalid_tag_ids:
                raise BusinessException(
                    user_message="指定的标签不存在",
                    user_detail={"invalid_tag_ids": invalid_tag_ids},
                    error_type=ErrorType.资源未找到
                )
    
    def _convert_to_response(self, config) -> InterfaceConfigResponse:
        """转换为响应格式"""
        # 获取关联的标签
        tags = self.repository.get_config_tags(config.id)
        tag_ids = [tag.id for tag in tags]
        tag_names = [tag.name for tag in tags]

        # 解析JSON字段
        query_fields = json.loads(config.query_fields) if config.query_fields else None
        required_fields = json.loads(config.required_fields) if config.required_fields else None
        response_fields = json.loads(config.response_fields) if config.response_fields else None

        # 解析新增的JSON字段
        orm_model_config = json.loads(config.orm_model_config) if config.orm_model_config else None
        orm_relationships = json.loads(config.orm_relationships) if config.orm_relationships else None
        parameter_config = json.loads(config.parameter_config) if config.parameter_config else None
        visual_config = json.loads(config.visual_config) if config.visual_config else None
        validation_rules = json.loads(config.validation_rules) if config.validation_rules else None

        # 手动创建响应对象，避免from_orm的类型验证问题
        config_response = InterfaceConfigResponse(
            id=config.id,
            name=config.name,
            path=config.path,
            method=config.method,
            description=config.description,
            group_id=config.group_id,
            datasource_id=config.datasource_id,
            table_name=config.table_name,
            table_type=config.table_type,  # 添加缺失的table_type字段
            is_enabled=config.is_enabled,
            is_public=config.is_public,
            query_fields=query_fields,
            required_fields=required_fields,
            response_fields=response_fields,
            # 关联数据名称
            group_name=config.group.name if config.group else None,
            datasource_name=config.datasource.name if config.datasource else None,
            # 新增字段 - 使用驼峰格式字段名
            ormModelConfig=orm_model_config,
            ormModelName=config.orm_model_name,
            ormRelationships=orm_relationships,
            parameterConfig=parameter_config,
            visualConfig=visual_config,
            validationRules=validation_rules,
            cache_duration=config.cache_duration,
            rate_limit=config.rate_limit,
            last_test_at=config.last_test_at,
            test_status=config.test_status,
            created_at=config.created_at,
            updated_at=config.updated_at,
            created_by=config.created_by,
            tags=tag_ids,
            tag_names=tag_names
        )

        return config_response
