# 新建业务页面标准流程

## 📋 创建步骤

### 1. 准备工作
- [ ] 确定模块名称（如：UserManagement）
- [ ] 确定路由路径（如：/user/UserManagement）
- [ ] 确定图标名称（如：User）

### 2. 创建类型定义
- [ ] 复制 `templates/TypeTemplate.ts` 到 `src/types/{module-name}.ts`
- [ ] 替换所有 `{ModuleName}` 为实际模块名
- [ ] 根据需求调整字段和类型

### 3. 创建Mock数据
- [ ] 复制 `templates/MockTemplate.ts` 到 `src/mock/{module-name}.mock.ts`
- [ ] 替换所有 `{ModuleName}` 为实际模块名
- [ ] 根据需求调整Mock数据

### 4. 创建Service层
- [ ] 复制 `templates/ServiceTemplate.ts` 到 `src/services/{module-name}.service.ts`
- [ ] 替换所有 `{ModuleName}` 为实际模块名
- [ ] 根据需求调整API方法

### 5. 创建主页面
- [ ] 复制 `templates/BusinessPageTemplate.vue` 到 `src/views/{module-name}/{ModuleName}.vue`
- [ ] 替换所有 `{ModuleName}` 为实际模块名
- [ ] 替换所有 `{IconName}` 为实际图标名
- [ ] 根据需求调整表格列和操作按钮

### 6. 创建表单组件
- [ ] 复制 `templates/FormTemplate.vue` 到 `src/views/{module-name}/{ModuleName}Form.vue`
- [ ] 替换所有 `{ModuleName}` 为实际模块名
- [ ] 根据需求调整表单字段和验证规则
- [ ] 添加到 `MainIndex.vue` 的组件映射中

### 7. 通用工具使用
- [ ] 在服务文件中导入通用工具：`import { convertToCamelCase, convertToSnakeCase, PasswordUtils } from '@/utils/common-utils'`
- [ ] API响应数据转换：`return convertToCamelCase(data)`
- [ ] API请求数据转换：`body: JSON.stringify(convertToSnakeCase(data))`
- [ ] 密码处理：`const processedData = await PasswordUtils.processPasswordField(data)`

### 7. 创建其他抽屉组件（可选）
- [ ] 复制 `templates/DrawerTemplate.vue` 到 `src/views/{module-name}/{ModuleName}XXX.vue`
- [ ] 替换所有 `{ComponentName}` 为实际组件名
- [ ] 根据需求调整内容区域
- [ ] 添加到 `MainIndex.vue` 的组件映射中

### 8. 添加路由
- [ ] 在 `src/router/index.ts` 中添加路由配置
- [ ] 在菜单配置中添加菜单项

## ✅ 必须检查的规则

### 编码规范 ⚠️ 强制执行
- [ ] **禁止使用 `function` 关键字**：所有函数必须使用箭头函数语法
- [ ] 正确示例：`export const myFunction = (param: string): string => { ... }`
- [ ] 错误示例：`export function myFunction(param: string): string { ... }`
- [ ] 运行 `npm run lint:check` 检查代码规范

### 样式文件规则 ⚠️ 重要
- [ ] **禁止修改或导入 `framework.scss`**：这是框架级别的样式文件
- [ ] 组件样式应该在组件内部单独导入：`@import '@/assets/styles/component-name.scss'`
- [ ] 页面样式使用：`@use '@/assets/styles/page-common.scss' as *;`

### 模板语法规则 ⚠️ 重要
- [ ] Vue模板中使用模板字符串时，内部不能使用双引号
- [ ] 使用中文引号「」或单引号避免语法冲突
- [ ] 示例：`:description="\`没有找到包含「\${searchQuery}」的数据\`"`

### 页面结构
- [ ] 使用 `<div class="container">` 作为根容器
- [ ] 导入 `@use '@/assets/styles/page-common.scss' as *;`
- [ ] 页面头部使用 `page-header > page-title + header-actions` 结构

### 表格样式
- [ ] `style="width: 100%; min-width: 1000px;"`
- [ ] `:row-style="{ height: '60px' }"`
- [ ] `:cell-style="{ padding: '12px 0' }"`

### 公共组件
- [ ] 使用 `SearchComponent` 搜索组件
- [ ] 使用 `PaginationComponent` 分页组件

### 抽屉使用
- [ ] 导入 `useGlobalDrawerMessenger`
- [ ] 使用 `drawerMessenger.showDrawer()` 打开抽屉
- [ ] **绝对不能使用** `globalDrawerStore.openDrawer()`

### Service层
- [ ] 接口定义 + Mock实现 + API实现
- [ ] 环境变量控制Mock/API切换
- [ ] 完整的CRUD方法

### Mock数据
- [ ] 至少5条测试数据
- [ ] 包含不同状态的数据
- [ ] 支持搜索、筛选、分页场景

## 🚫 常见错误

### 抽屉错误
❌ `globalDrawerStore.openDrawer()`
✅ `drawerMessenger.showDrawer()`

### 容器错误
❌ `<div class="page-container">`
✅ `<div class="container">`

### 样式错误
❌ 使用 `@import '@/assets/styles/page-common.scss';`
✅ `@use '@/assets/styles/page-common.scss' as *;`

### Service错误
❌ 直接调用API，没有Mock支持
✅ 接口 + Mock + API + 环境变量切换

## 📝 替换清单

创建新页面时，需要替换以下占位符：

- `{ModuleName}` → 实际模块名（如：UserManagement）
- `{moduleName}` → 小写模块名（如：userManagement）
- `{module-name}` → 短横线模块名（如：user-management）
- `{IconName}` → 图标名称（如：User）

## 🎯 完成检查

创建完成后，确保：
- [ ] 页面可以正常访问
- [ ] 数据可以正常加载
- [ ] 搜索功能正常
- [ ] 分页功能正常
- [ ] 抽屉可以正常打开
- [ ] 所有CRUD操作正常
- [ ] 样式与其他页面一致
