"""
配置层 - 数据源管理功能测试
"""

from sqlalchemy.orm import Session
from app.shared.database import SessionLocal
from app.config.repositories.data_source_repository import DataSourceRepository
from app.config.services.data_source_service import DataSourceService
from app.config.schemas.data_source_schema import DataSourceCreate, DatabaseType, DataSourceStatus

def test_data_source_repository():
    """测试数据源Repository层"""
    print("🔍 测试数据源Repository层...")
    
    try:
        db = SessionLocal()
        repo = DataSourceRepository(db)
        
        # 测试获取列表
        items, total = repo.get_list(page=1, size=10)
        print(f"✅ Repository获取列表成功: {total} 条记录")
        
        # 测试根据ID获取
        if total > 0:
            first_item = repo.get_by_id(items[0].id)
            if first_item:
                print(f"✅ Repository根据ID获取成功: {first_item.name}")
            else:
                print("❌ Repository根据ID获取失败")
                return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据源Repository测试失败: {e}")
        return False

def test_data_source_service():
    """测试数据源Service层"""
    print("🔍 测试数据源Service层...")
    
    try:
        db = SessionLocal()
        service = DataSourceService(db)
        
        # 测试获取列表
        result = service.get_data_sources(page=1, size=10)
        print(f"✅ Service获取列表成功: {result.total} 条记录")
        
        # 测试获取单个数据源
        if result.total > 0:
            first_id = result.items[0].id
            single_result = service.get_data_source(first_id)
            print(f"✅ Service获取单个数据源成功: {single_result.name}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据源Service测试失败: {e}")
        return False

def test_data_source_crud():
    """测试数据源CRUD操作"""
    print("🔍 测试数据源CRUD操作...")
    
    try:
        db = SessionLocal()
        service = DataSourceService(db)
        
        # 创建测试数据源
        test_data = DataSourceCreate(
            name="测试数据源_" + str(int(time.time())),
            description="这是一个测试数据源",
            db_type=DatabaseType.SQLITE,
            host="localhost",
            port=0,
            database="test.db",
            username="test",
            password="test123",
            max_connections=5,
            connection_timeout=30,
            status=DataSourceStatus.ACTIVE,
            created_by="test_user"
        )
        
        # 创建
        created = service.create_data_source(test_data)
        print(f"✅ 创建数据源成功: ID={created.id}")
        
        # 读取
        retrieved = service.get_data_source(created.id)
        print(f"✅ 读取数据源成功: {retrieved.name}")
        
        # 删除
        delete_result = service.delete_data_source(created.id)
        print(f"✅ 删除数据源成功: {delete_result['message']}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据源CRUD测试失败: {e}")
        return False

def run_data_source_tests():
    """运行所有数据源测试"""
    print("🚀 开始配置层数据源测试...\n")
    
    tests = [
        ("数据源Repository", test_data_source_repository),
        ("数据源Service", test_data_source_service),
        ("数据源CRUD", test_data_source_crud)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    return results

# 导入time模块
import time
