import { defineStore } from 'pinia';
import { ref, markRaw } from 'vue';
import type { Component } from 'vue';

// 按钮配置接口
interface ButtonConfig {
  text: string;
  handler: () => void;
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
  loading?: boolean;
  loadingText?: string;
  disabled?: boolean;
}

interface DrawerConfig {
  title: string;
  component: Component | null;
  props?: Record<string, any>;
  size?: string;
  leftButtons?: ButtonConfig[];
  rightButtons?: ButtonConfig[];
}

export const useGlobalDrawerStore = defineStore('globalDrawer', () => {
  // 状态 - 支持多层抽屉
  const visible = ref(false);
  const title = ref('');
  const component = ref<Component | null>(null);
  const props = ref<Record<string, any>>({});
  const size = ref('35%');
  const editingId = ref<number | null>(null);
  const leftButtons = ref<ButtonConfig[]>([]);
  const rightButtons = ref<ButtonConfig[]>([]);

  // 第二层抽屉状态
  const secondVisible = ref(false);
  const secondTitle = ref('');
  const secondComponent = ref<Component | null>(null);
  const secondProps = ref<Record<string, any>>({});
  const secondSize = ref('45%');
  const secondLeftButtons = ref<ButtonConfig[]>([]);
  const secondRightButtons = ref<ButtonConfig[]>([]);

  // 动作
  const showDrawer = (config: DrawerConfig) => {
    visible.value = true;
    title.value = config.title;
    // 使用 markRaw 避免组件被响应式包装
    component.value = config.component ? markRaw(config.component) : null;
    props.value = config.props || {};
    size.value = config.size || '35%';
    leftButtons.value = config.leftButtons || [];
    rightButtons.value = config.rightButtons || [];
  };

  const showSecondDrawer = (config: DrawerConfig) => {
    secondVisible.value = true;
    secondTitle.value = config.title;
    secondComponent.value = config.component ? markRaw(config.component) : null;
    secondProps.value = config.props || {};
    secondSize.value = config.size || '45%';
    secondLeftButtons.value = config.leftButtons || [];
    secondRightButtons.value = config.rightButtons || [];
  };

  const closeDrawer = () => {
    visible.value = false;
    // 延迟清理组件，避免动画过程中组件消失
    setTimeout(() => {
      component.value = null;
      props.value = {};
      title.value = '';
      leftButtons.value = [];
      rightButtons.value = [];
    }, 300);
  };

  const closeSecondDrawer = () => {
    secondVisible.value = false;
    setTimeout(() => {
      secondComponent.value = null;
      secondProps.value = {};
      secondTitle.value = '';
      secondLeftButtons.value = [];
      secondRightButtons.value = [];
    }, 300);
  };

  const updateProps = (newProps: Record<string, any>) => {
    props.value = { ...props.value, ...newProps };
  };

  return {
    // 第一层抽屉状态
    visible,
    title,
    component,
    props,
    size,
    editingId,
    leftButtons,
    rightButtons,
    // 第二层抽屉状态
    secondVisible,
    secondTitle,
    secondComponent,
    secondProps,
    secondSize,
    secondLeftButtons,
    secondRightButtons,
    // 动作
    showDrawer,
    showSecondDrawer,
    closeDrawer,
    closeSecondDrawer,
    updateProps
  };
});
