<!-- 
  DrawerHeader - 抽屉标题栏组件
  功能: 显示抽屉标题并提供关闭按钮，根据当前抽屉层级自动适配标题内容
  特点:
    1. 自动切换第一层/第二层抽屉标题
    2. 自定义关闭按钮样式和交互
    3. 响应式标题布局
  使用方式: <DrawerHeader /> (无需传递props，通过globalDrawerStore管理标题状态)
-->
<template>
  <div class="custom-drawer-header">
    <button class="custom-close-btn" @click="handleClose">×</button>
    <h4 class="custom-drawer-title">
      {{ currentTitle }}
    </h4>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';

// 使用Pinia获取抽屉状态
const globalDrawerStore = useGlobalDrawerStore();
const drawerMessenger = useGlobalDrawerMessenger();

// 根据当前抽屉层级显示正确的标题
const currentTitle = computed(() => {
  // 如果第二层抽屉可见，显示第二层标题，否则显示第一层标题
  return globalDrawerStore.secondVisible
    ? globalDrawerStore.secondTitle
    : globalDrawerStore.title;
});

// 关闭抽屉
const handleClose = () => {
  // 如果第二层抽屉可见，关闭第二层，否则关闭第一层
  if (globalDrawerStore.secondVisible) {
    drawerMessenger.hideDrawer(true); // 关闭第二层抽屉
  } else {
    drawerMessenger.hideDrawer(); // 关闭第一层抽屉
  }
};
</script>

<style lang="scss" scoped>
/* 自定义抽屉头部样式 */
.custom-drawer-header {
  padding: 0 20px 10px 0px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 40px; /* 缩小头部高度 */
}

.custom-drawer-title {
  font-weight: bold;
  color: #303133;
  font-size: 16px;
  text-align: left;
  margin: 0;
  margin-left: 50px; /* 为左侧关闭按钮留出空间 */
  flex: 1;
  line-height: 1.2;
}

.custom-close-btn {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #909399;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0 0 10px 0;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #606266;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  &:active {
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
