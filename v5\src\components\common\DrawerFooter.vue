<!-- 
  DrawerFooter - 智能抽屉底部按钮组件
  功能: 自动从globalDrawerStore获取按钮配置，根据当前抽屉层级显示左右两侧按钮组，支持加载状态管理
  特点:
    1. 自动适配第一层/第二层抽屉的按钮配置
    2. 统一的底部固定定位样式
    3. 支持左右分区的按钮布局
    4. 内置加载状态显示逻辑
  使用方式: <DrawerFooter /> (无需传递props，通过globalDrawerStore管理按钮状态)
-->
<template>
  <div v-if="hasButtons" class="drawer-footer-fixed">
    <!-- 2024-12-27: 移除分割线div，使用border-top替代 -->
    <div class="drawer-footer">
      <!-- 左侧按钮组 -->
      <div v-if="currentLeftButtons?.length" class="drawer-footer-left">
        <el-button
          v-for="btn in currentLeftButtons"
          :key="btn.text"
          :type="btn.type || 'default'"
          :loading="btn.loading"
          :disabled="btn.disabled"
          @click="btn.handler"
        >
          {{ btn.loading ? (btn.loadingText || '处理中...') : btn.text }}
        </el-button>
      </div>

      <!-- 右侧按钮组 -->
      <div class="drawer-footer-right">
        <el-button
          v-for="btn in currentRightButtons"
          :key="btn.text"
          :type="btn.type || 'default'"
          :loading="btn.loading"
          :disabled="btn.disabled"
          @click="btn.handler"
        >
          {{ btn.loading ? (btn.loadingText || '处理中...') : btn.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 智能抽屉底部按钮组件
 *
 * 功能特点：
 * 1. 自动从globalDrawerStore获取按钮配置
 * 2. 根据当前抽屉层级显示正确的按钮
 * 3. 统一的底部按钮布局和样式
 * 4. 支持左右两侧按钮配置
 * 5. 统一的加载状态管理
 *
 * 使用方式：
 * <DrawerFooter /> <!-- 无需传递任何props，自动从store获取 -->
 */

import { computed } from 'vue';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';

// 使用Pinia获取抽屉状态
const globalDrawerStore = useGlobalDrawerStore();

// 根据当前抽屉层级获取正确的按钮配置
const currentLeftButtons = computed(() => {
  return globalDrawerStore.secondVisible
    ? globalDrawerStore.secondLeftButtons
    : globalDrawerStore.leftButtons;
});

const currentRightButtons = computed(() => {
  return globalDrawerStore.secondVisible
    ? globalDrawerStore.secondRightButtons
    : globalDrawerStore.rightButtons;
});

// 检查是否有按钮需要显示
const hasButtons = computed(() => {
  return currentLeftButtons.value.length > 0 || currentRightButtons.value.length > 0;
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/*
 * 2024-12-27: DrawerFooter组件样式
 * 注意：使用全局样式确保能正确应用到抽屉组件
 * 导入page-common.scss以应用自定义按钮样式（#3FC8DD颜色）
 */

/* 确保抽屉容器有正确的相对定位 */
.el-drawer__body {
  position: relative !important;
}

/* 2024-12-27: DrawerFooter样式已移至page-common.scss，使用公共样式 */
/* 只保留组件特有的布局逻辑 */

/* 左右按钮组布局 - 2024-12-27: 适配通长底部样式 */
.drawer-footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drawer-footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
}

/* 当没有左侧按钮时，右侧按钮应该靠右对齐 */
.drawer-footer:not(:has(.drawer-footer-left)) {
  justify-content: flex-end;
}

/* 主要按钮样式 - 使用用户偏好的 #3FC8DD 颜色 */
:deep(.el-button--primary) {
  color: #fff !important;
  background-color: #3FC8DD !important;
  border-color: #3FC8DD !important;
}

:deep(.el-button--primary:hover) {
  background-color: #35b3c7 !important;
  border-color: #35b3c7 !important;
}

:deep(.el-button--primary:active) {
  background-color: #2d9fb5 !important;
  border-color: #2d9fb5 !important;
}
</style>
