"""
接口标签数据模式定义
"""

from typing import Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
import re


class InterfaceTagBase(BaseModel):
    """接口标签基础模式"""
    name: str = Field(..., min_length=1, max_length=50, description="标签名称")
    color: str = Field(..., description="标签颜色(十六进制)")
    description: Optional[str] = Field(None, max_length=200, description="标签描述")
    is_enabled: bool = Field(True, description="是否启用")
    
    @validator('color')
    def validate_color(cls, v):
        """验证颜色格式"""
        if not re.match(r'^#[0-9A-Fa-f]{6}$', v):
            raise ValueError('颜色必须是有效的十六进制格式，如：#FF0000')
        return v


class InterfaceTagCreate(InterfaceTagBase):
    """接口标签创建模式"""
    created_by: Optional[str] = Field(None, max_length=50, description="创建人")


class InterfaceTagUpdate(BaseModel):
    """接口标签更新模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="标签名称")
    color: Optional[str] = Field(None, description="标签颜色(十六进制)")
    description: Optional[str] = Field(None, max_length=200, description="标签描述")
    is_enabled: Optional[bool] = Field(None, description="是否启用")
    
    @validator('color')
    def validate_color(cls, v):
        """验证颜色格式"""
        if v is not None and not re.match(r'^#[0-9A-Fa-f]{6}$', v):
            raise ValueError('颜色必须是有效的十六进制格式，如：#FF0000')
        return v


class InterfaceTagResponse(InterfaceTagBase):
    """接口标签响应模式"""
    id: int
    interface_count: Optional[int] = Field(None, description="使用该标签的接口数量")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    
    class Config:
        from_attributes = True


class InterfaceTagListResponse(BaseModel):
    """接口标签列表响应模式"""
    items: list[InterfaceTagResponse]
    total: int
    page: int
    size: int
    pages: int
