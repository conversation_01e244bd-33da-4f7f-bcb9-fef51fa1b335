/**
 * 接口分组服务 - 已禁用Mock，强制使用API
 *
 * 注意：接口分组服务已被接管，不再使用Mock数据
 * 即使环境变量配置错误，也会强制使用真实API服务
 */
import type {
  InterfaceGroup,
  InterfaceGroupRequest,
  InterfaceGroupListResponse,
  InterfaceGroupQuery
} from '@/types/interface-group';
// Mock导入已注释 - 接口分组服务已切换到API模式
// import {
//   mockGetInterfaceGroups,
//   mockCreateInterfaceGroup,
//   mockUpdateInterfaceGroup,
//   mockDeleteInterfaceGroup
// } from '@/mock/interface-group.mock';
import { apiClient } from '@/utils/http-client';

/**
 * 接口分组服务接口
 */
interface IInterfaceGroupService {
  getInterfaceGroups(query?: InterfaceGroupQuery): Promise<InterfaceGroupListResponse>;
  getInterfaceGroupById(id: number): Promise<InterfaceGroup | undefined>;
  createInterfaceGroup(data: InterfaceGroupRequest): Promise<InterfaceGroup>;
  updateInterfaceGroup(id: number, data: InterfaceGroupRequest): Promise<InterfaceGroup>;
  deleteInterfaceGroup(id: number): Promise<boolean>;
  checkPathPrefixExists(pathPrefix: string, excludeId?: number): Promise<boolean>;
}

/*
// Mock接口分组服务实现 - 已禁用，接口分组服务已切换到API模式
class MockInterfaceGroupService implements IInterfaceGroupService {
  async getInterfaceGroups(query: InterfaceGroupQuery = {}): Promise<InterfaceGroupListResponse> {
    const { page = 1, page_size = 10, search } = query;
    return mockGetInterfaceGroups(page, page_size, search);
  }

  async getInterfaceGroupById(id: number): Promise<InterfaceGroup | undefined> {
    const response = await mockGetInterfaceGroups(1, 1000); // 获取所有数据
    return response.items.find(group => group.id === id);
  }

  async createInterfaceGroup(data: InterfaceGroupRequest): Promise<InterfaceGroup> {
    return mockCreateInterfaceGroup(data);
  }

  async updateInterfaceGroup(id: number, data: InterfaceGroupRequest): Promise<InterfaceGroup> {
    return mockUpdateInterfaceGroup(id, data);
  }

  async deleteInterfaceGroup(id: number): Promise<boolean> {
    return mockDeleteInterfaceGroup(id);
  }

  async checkPathPrefixExists(pathPrefix: string, excludeId?: number): Promise<boolean> {
    const response = await mockGetInterfaceGroups(1, 1000); // 获取所有数据
    return response.data.some(group =>
      group.path_prefix === pathPrefix && group.id !== excludeId
    );
  }
}
*/

/**
 * API接口分组服务实现 - 使用统一HTTP客户端
 */
class ApiInterfaceGroupService implements IInterfaceGroupService {

  async getInterfaceGroups(query: InterfaceGroupQuery = {}): Promise<InterfaceGroupListResponse> {
    const params: Record<string, any> = {};
    if (query.page) params.page = query.page;
    if (query.pageSize) params.size = query.pageSize;
    if (query.search) params.search = query.search;

    return apiClient.get<InterfaceGroupListResponse>('/interface/groups', { params });
  }

  async getInterfaceGroupById(id: number): Promise<InterfaceGroup | undefined> {
    try {
      return await apiClient.get<InterfaceGroup>(`/interface/groups/${id}`);
    } catch (error) {
      // 如果是404错误，返回undefined
      if (error instanceof Error && error.message.includes('404')) {
        return undefined;
      }
      throw error;
    }
  }

  async createInterfaceGroup(data: InterfaceGroupRequest): Promise<InterfaceGroup> {
    return apiClient.post<InterfaceGroup>('/interface/groups', data);
  }

  async updateInterfaceGroup(id: number, data: InterfaceGroupRequest): Promise<InterfaceGroup> {
    return apiClient.put<InterfaceGroup>(`/interface/groups/${id}`, data);
  }

  async deleteInterfaceGroup(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`/interface/groups/${id}`);
      return true;
    } catch (error) {
      return false;
    }
  }
  
  async checkPathPrefixExists(pathPrefix: string, excludeId?: number): Promise<boolean> {
    const params = new URLSearchParams();
    params.append('path_prefix', pathPrefix);
    if (excludeId) params.append('exclude_id', excludeId.toString());
    
    const response = await fetch(`${this.baseUrl}/check-path-prefix?${params}`);
    if (!response.ok) throw new Error('检查路径前缀失败');
    const result = await response.json();
    return result.exists;
  }
}

// 接口分组服务已被接管，强制使用API服务，不再使用Mock
// const useMockForInterfaceGroup = import.meta.env.VITE_USE_MOCK === 'true';
const interfaceGroupService: IInterfaceGroupService = new ApiInterfaceGroupService();
// 注释掉Mock服务选择逻辑，确保始终使用真实API
// const interfaceGroupService: IInterfaceGroupService = useMockForInterfaceGroup
//   ? new MockInterfaceGroupService()
//   : new ApiInterfaceGroupService();

export default interfaceGroupService;
