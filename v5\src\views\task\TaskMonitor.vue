<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Monitor /></el-icon>
      任务监控
    </h2>
    
    <div class="content-container">
      <!-- 任务统计概览 -->
      <div class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        任务统计概览
      </div>
      
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon running">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStats.running }}</div>
                <div class="stat-label">运行中任务</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon waiting">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStats.waiting }}</div>
                <div class="stat-label">等待执行</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon success">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStats.success }}</div>
                <div class="stat-label">今日成功</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon failed">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStats.failed }}</div>
                <div class="stat-label">今日失败</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 实时任务状态 -->
      <div class="section-title">
        <el-icon><View /></el-icon>
        实时任务状态
      </div>
      
      <div class="monitor-card">
        <div class="monitor-header">
          <span>任务执行监控</span>
          <el-button @click="refreshMonitor" size="small" icon="Refresh">刷新</el-button>
        </div>
        
        <el-table :data="runningTasks" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="任务名称" width="200"></el-table-column>
          <el-table-column prop="type" label="任务类型" width="120">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)" size="small">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="执行状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="执行进度" width="150">
            <template #default="scope">
              <el-progress :percentage="scope.row.progress" :status="scope.row.progress === 100 ? 'success' : ''"></el-progress>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180"></el-table-column>
          <el-table-column prop="duration" label="已执行时长" width="120"></el-table-column>
          <el-table-column prop="message" label="当前状态" min-width="200"></el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" type="danger" @click="stopTask(scope.row)" v-if="scope.row.status === '执行中'">
                停止
              </el-button>
              <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 任务执行趋势 -->
      <div class="section-title">
        <el-icon><TrendCharts /></el-icon>
        任务执行趋势
      </div>
      
      <div class="chart-card">
        <div class="chart-header">
          <span>最近7天任务执行情况</span>
          <el-radio-group v-model="chartType" size="small">
            <el-radio-button label="success">成功率</el-radio-button>
            <el-radio-button label="count">执行次数</el-radio-button>
            <el-radio-button label="duration">平均耗时</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="chart-container" ref="chartContainer" style="height: 300px;"></div>
      </div>
      
      <!-- 任务队列状态 -->
      <div class="section-title">
        <el-icon><List /></el-icon>
        任务队列状态
      </div>
      
      <div class="queue-card">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="queue-section">
              <h4>等待队列 ({{ waitingQueue.length }})</h4>
              <div class="queue-list">
                <div v-for="task in waitingQueue" :key="task.id" class="queue-item">
                  <div class="queue-info">
                    <span class="task-name">{{ task.name }}</span>
                    <span class="task-time">预计执行: {{ task.scheduledTime }}</span>
                  </div>
                  <el-tag size="small" type="info">{{ task.priority }}</el-tag>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="queue-section">
              <h4>最近完成 ({{ recentCompleted.length }})</h4>
              <div class="queue-list">
                <div v-for="task in recentCompleted" :key="task.id" class="queue-item">
                  <div class="queue-info">
                    <span class="task-name">{{ task.name }}</span>
                    <span class="task-time">完成时间: {{ task.completedTime }}</span>
                  </div>
                  <el-tag size="small" :type="task.result === 'success' ? 'success' : 'danger'">
                    {{ task.result === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Monitor, DataAnalysis, VideoPlay, Clock, CircleCheck, CircleClose, View, TrendCharts, List, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 加载状态
const loading = ref(false)

// 图表类型
const chartType = ref('success')

// 任务统计
const taskStats = ref({
  running: 3,
  waiting: 8,
  success: 156,
  failed: 2
})

// 运行中的任务
const runningTasks = ref([
  {
    id: 1,
    name: '数据同步任务',
    type: '数据同步',
    status: '执行中',
    progress: 65,
    startTime: '2023-12-15 14:30:00',
    duration: '5分32秒',
    message: '正在同步用户数据表...'
  },
  {
    id: 2,
    name: '日志清理任务',
    type: '系统清理',
    status: '执行中',
    progress: 90,
    startTime: '2023-12-15 14:25:00',
    duration: '10分15秒',
    message: '正在清理访问日志文件...'
  },
  {
    id: 3,
    name: '报表生成任务',
    type: '报表生成',
    status: '准备中',
    progress: 0,
    startTime: '2023-12-15 14:35:00',
    duration: '0秒',
    message: '等待资源分配...'
  }
])

// 等待队列
const waitingQueue = ref([
  { id: 4, name: '数据备份任务', scheduledTime: '15:00:00', priority: '高' },
  { id: 5, name: '系统检查任务', scheduledTime: '15:30:00', priority: '中' },
  { id: 6, name: '缓存清理任务', scheduledTime: '16:00:00', priority: '低' }
])

// 最近完成
const recentCompleted = ref([
  { id: 7, name: '邮件发送任务', completedTime: '14:20:00', result: 'success' },
  { id: 8, name: '数据校验任务', completedTime: '14:15:00', result: 'success' },
  { id: 9, name: '文件上传任务', completedTime: '14:10:00', result: 'failed' }
])

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    '数据同步': 'primary',
    '系统清理': 'warning',
    '报表生成': 'success',
    '数据备份': 'info'
  }
  return colors[type] || ''
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    '执行中': 'primary',
    '准备中': 'warning',
    '已完成': 'success',
    '已失败': 'danger'
  }
  return colors[status] || 'info'
}

// 刷新监控
const refreshMonitor = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('监控数据已刷新')
  }, 1000)
}

// 停止任务
const stopTask = (task: any) => {
  ElMessage.warning(`任务 "${task.name}" 停止功能开发中...`)
}

// 查看详情
const viewDetail = (task: any) => {
  ElMessage.info(`查看任务 "${task.name}" 详情功能开发中...`)
}

// 初始化
onMounted(() => {
  // 这里可以初始化图表
})
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary-color);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary-color);
  padding-left: 12px;
}

.stats-overview {
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #ebeef5;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.running { background: #409eff; }
.stat-icon.waiting { background: #e6a23c; }
.stat-icon.success { background: #67c23a; }
.stat-icon.failed { background: #f56c6c; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.monitor-card, .chart-card, .queue-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.monitor-header, .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-weight: 500;
}

.queue-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.queue-list {
  max-height: 200px;
  overflow-y: auto;
}

.queue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #fafafa;
}

.queue-info {
  flex: 1;
}

.task-name {
  display: block;
  font-weight: 500;
  color: #333;
}

.task-time {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}
</style>
