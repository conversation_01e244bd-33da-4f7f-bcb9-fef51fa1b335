module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/eslint-config-typescript'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module'
  },
  rules: {
    // 强制使用箭头函数，禁止使用 function 关键字
    'func-style': ['error', 'expression', { 
      'allowArrowFunctions': true 
    }],
    
    // 优先使用箭头函数作为回调
    'prefer-arrow-callback': ['error', {
      'allowNamedFunctions': false,
      'allowUnboundThis': true
    }],
    
    // 其他代码风格规则
    'no-var': 'error',
    'prefer-const': 'error',
    'prefer-template': 'error',
    
    // Vue 相关规则
    'vue/multi-word-component-names': 'off',
    
    // TypeScript 相关规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'warn'
  },
  overrides: [
    {
      files: ['*.vue'],
      parser: 'vue-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser'
      }
    }
  ]
};
