"""
接口标签数据访问层
负责数据库的CRUD操作
"""

from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.config.interface.models.interface_tag_model import InterfaceTagModel
from app.config.interface.schemas.interface_tag_schema import InterfaceTagCreate, InterfaceTagUpdate
from app.shared.core.log_util import LogUtil
import random


class InterfaceTagRepository:
    """接口标签数据访问类"""
    
    # 预定义颜色列表
    DEFAULT_COLORS = [
        '#3FC8DD', '#67C23A', '#E6A23C', '#F56C6C', '#9C27B0',
        '#409EFF', '#FF9800', '#4CAF50', '#2196F3', '#9E9E9E',
        '#795548', '#607D8B', '#FF5722', '#8BC34A', '#00BCD4'
    ]
    
    def __init__(self, db: Session):
        self.db = db
        LogUtil.debug("接口标签Repository初始化", repository="InterfaceTagRepository")
    
    def get_by_id(self, tag_id: int) -> Optional[InterfaceTagModel]:
        """根据ID获取接口标签"""
        LogUtil.debug("根据ID获取接口标签", tag_id=tag_id)
        return self.db.query(InterfaceTagModel).filter(InterfaceTagModel.id == tag_id).first()
    
    def get_by_name(self, name: str) -> Optional[InterfaceTagModel]:
        """根据名称获取接口标签"""
        LogUtil.debug("根据名称获取接口标签", name=name)
        return self.db.query(InterfaceTagModel).filter(InterfaceTagModel.name == name).first()
    
    def get_by_ids(self, tag_ids: List[int]) -> List[InterfaceTagModel]:
        """根据ID列表获取接口标签"""
        LogUtil.debug("根据ID列表获取接口标签", tag_ids=tag_ids)
        return self.db.query(InterfaceTagModel).filter(InterfaceTagModel.id.in_(tag_ids)).all()
    
    def get_list(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None
    ) -> Tuple[List[InterfaceTagModel], int]:
        """
        获取接口标签列表（分页）
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词（名称、描述）
            
        Returns:
            (接口标签列表, 总数量)
        """
        LogUtil.debug("获取接口标签列表", page=page, size=size, search=search)
        
        query = self.db.query(InterfaceTagModel)
        
        # 搜索过滤
        if search:
            search_filter = or_(
                InterfaceTagModel.name.ilike(f"%{search}%"),
                InterfaceTagModel.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        items = query.order_by(InterfaceTagModel.created_at.desc()).offset((page - 1) * size).limit(size).all()
        
        LogUtil.info("接口标签列表查询完成", total=total, returned_count=len(items))
        return items, total
    
    def get_all_enabled(self) -> List[InterfaceTagModel]:
        """获取所有启用的接口标签"""
        LogUtil.debug("获取所有启用的接口标签")
        return self.db.query(InterfaceTagModel).filter(InterfaceTagModel.is_enabled == True).all()
    
    def create(self, tag_data: InterfaceTagCreate) -> InterfaceTagModel:
        """
        创建接口标签
        
        Args:
            tag_data: 接口标签创建数据
            
        Returns:
            创建的接口标签模型
        """
        LogUtil.debug("创建接口标签", name=tag_data.name, color=tag_data.color)
        
        # 如果没有指定颜色，自动分配一个
        color = tag_data.color
        if not color:
            color = self._get_random_color()
            LogUtil.debug("自动分配颜色", color=color)
        
        # 创建接口标签模型
        db_tag = InterfaceTagModel(
            name=tag_data.name,
            color=color,
            description=tag_data.description,
            is_enabled=tag_data.is_enabled,
            created_by=tag_data.created_by
        )
        
        self.db.add(db_tag)
        self.db.commit()
        self.db.refresh(db_tag)
        
        LogUtil.info("接口标签创建成功", tag_id=db_tag.id, name=db_tag.name, color=db_tag.color)
        return db_tag
    
    def update(self, tag_id: int, tag_data: InterfaceTagUpdate) -> Optional[InterfaceTagModel]:
        """
        更新接口标签
        
        Args:
            tag_id: 接口标签ID
            tag_data: 更新数据
            
        Returns:
            更新后的接口标签模型
        """
        LogUtil.debug("更新接口标签", tag_id=tag_id)
        
        db_tag = self.get_by_id(tag_id)
        if not db_tag:
            LogUtil.warning("接口标签不存在", tag_id=tag_id)
            return None
        
        # 更新字段
        update_data = tag_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_tag, field, value)
        
        self.db.commit()
        self.db.refresh(db_tag)
        
        LogUtil.info("接口标签更新成功", tag_id=db_tag.id, name=db_tag.name)
        return db_tag
    
    def delete(self, tag_id: int) -> bool:
        """
        删除接口标签
        
        Args:
            tag_id: 接口标签ID
            
        Returns:
            是否删除成功
        """
        LogUtil.debug("删除接口标签", tag_id=tag_id)
        
        db_tag = self.get_by_id(tag_id)
        if not db_tag:
            LogUtil.warning("接口标签不存在", tag_id=tag_id)
            return False
        
        self.db.delete(db_tag)
        self.db.commit()
        
        LogUtil.info("接口标签删除成功", tag_id=tag_id, name=db_tag.name)
        return True
    
    def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查名称是否已存在
        
        Args:
            name: 接口标签名称
            exclude_id: 排除的ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = self.db.query(InterfaceTagModel).filter(InterfaceTagModel.name == name)
        
        if exclude_id:
            query = query.filter(InterfaceTagModel.id != exclude_id)
        
        exists = query.first() is not None
        LogUtil.debug("检查接口标签名称是否存在", name=name, exclude_id=exclude_id, exists=exists)
        return exists
    
    def _get_random_color(self) -> str:
        """获取随机颜色"""
        # 获取已使用的颜色
        used_colors = [tag.color for tag in self.db.query(InterfaceTagModel.color).all()]
        
        # 找到未使用的颜色
        available_colors = [color for color in self.DEFAULT_COLORS if color not in used_colors]
        
        # 如果还有可用颜色，随机选择一个
        if available_colors:
            return random.choice(available_colors)
        
        # 如果所有预定义颜色都用完了，随机生成一个
        return f"#{random.randint(0, 0xFFFFFF):06X}"
