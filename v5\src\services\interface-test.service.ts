import type { 
  InterfaceTestRecord, 
  InterfaceTestRequest, 
  InterfaceTestResponse,
  InterfaceTestListResponse,
  InterfaceTestQuery,
  TestCase,
  TestCaseRequest,
  BatchTestRequest,
  BatchTestResponse,
  TestStatistics,
  InterfaceStatusStats
} from '@/types/interface-test';
import { 
  mockGetInterfaceTestRecords,
  mockExecuteInterfaceTest,
  mockBatchTestInterfaces,
  mockGetTestStatistics,
  mockGetInterfaceStatusStats
} from '@/mock/interface-test.mock';

/**
 * 接口测试服务接口
 */
interface IInterfaceTestService {
  getTestRecords(query?: InterfaceTestQuery): Promise<InterfaceTestListResponse>;
  getTestRecordById(id: number): Promise<InterfaceTestRecord | undefined>;
  executeTest(request: InterfaceTestRequest): Promise<InterfaceTestResponse>;
  batchTest(request: BatchTestRequest): Promise<BatchTestResponse>;
  getTestStatistics(): Promise<TestStatistics>;
  getInterfaceStatusStats(): Promise<InterfaceStatusStats>;
  deleteTestRecord(id: number): Promise<boolean>;
  clearTestRecords(interfaceId?: number): Promise<boolean>;
}

/**
 * Mock接口测试服务实现
 */
class MockInterfaceTestService implements IInterfaceTestService {
  async getTestRecords(query: InterfaceTestQuery = {}): Promise<InterfaceTestListResponse> {
    const { page = 1, page_size = 10 } = query;
    return mockGetInterfaceTestRecords(page, page_size, query);
  }
  
  async getTestRecordById(id: number): Promise<InterfaceTestRecord | undefined> {
    const response = await mockGetInterfaceTestRecords(1, 1000); // 获取所有数据
    return response.data.find(record => record.id === id);
  }
  
  async executeTest(request: InterfaceTestRequest): Promise<InterfaceTestResponse> {
    return mockExecuteInterfaceTest(request);
  }
  
  async batchTest(request: BatchTestRequest): Promise<BatchTestResponse> {
    return mockBatchTestInterfaces(request);
  }
  
  async getTestStatistics(): Promise<TestStatistics> {
    return mockGetTestStatistics();
  }
  
  async getInterfaceStatusStats(): Promise<InterfaceStatusStats> {
    return mockGetInterfaceStatusStats();
  }
  
  async deleteTestRecord(id: number): Promise<boolean> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    // Mock实现中直接返回成功
    return true;
  }
  
  async clearTestRecords(interfaceId?: number): Promise<boolean> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    // Mock实现中直接返回成功
    return true;
  }
}

/**
 * API接口测试服务实现
 */
class ApiInterfaceTestService implements IInterfaceTestService {
  private baseUrl = '/api/interface-tests';
  
  async getTestRecords(query: InterfaceTestQuery = {}): Promise<InterfaceTestListResponse> {
    const params = new URLSearchParams();
    if (query.page) params.append('page', query.page.toString());
    if (query.page_size) params.append('page_size', query.page_size.toString());
    if (query.interface_id) params.append('interface_id', query.interface_id.toString());
    if (query.search) params.append('search', query.search);
    if (query.success !== undefined) params.append('success', query.success.toString());
    if (query.start_date) params.append('start_date', query.start_date);
    if (query.end_date) params.append('end_date', query.end_date);
    if (query.tester) params.append('tester', query.tester);
    
    const response = await fetch(`${this.baseUrl}?${params}`);
    if (!response.ok) throw new Error('获取测试记录列表失败');
    return response.json();
  }
  
  async getTestRecordById(id: number): Promise<InterfaceTestRecord | undefined> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    if (response.status === 404) return undefined;
    if (!response.ok) throw new Error(`获取测试记录ID ${id} 失败`);
    return response.json();
  }
  
  async executeTest(request: InterfaceTestRequest): Promise<InterfaceTestResponse> {
    const response = await fetch(`${this.baseUrl}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    if (!response.ok) throw new Error('执行接口测试失败');
    return response.json();
  }
  
  async batchTest(request: BatchTestRequest): Promise<BatchTestResponse> {
    const response = await fetch(`${this.baseUrl}/batch`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    if (!response.ok) throw new Error('批量测试失败');
    return response.json();
  }
  
  async getTestStatistics(): Promise<TestStatistics> {
    const response = await fetch(`${this.baseUrl}/statistics`);
    if (!response.ok) throw new Error('获取测试统计失败');
    return response.json();
  }
  
  async getInterfaceStatusStats(): Promise<InterfaceStatusStats> {
    const response = await fetch(`/api/interfaces/status-stats`);
    if (!response.ok) throw new Error('获取接口状态统计失败');
    return response.json();
  }
  
  async deleteTestRecord(id: number): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('删除测试记录失败');
    return true;
  }
  
  async clearTestRecords(interfaceId?: number): Promise<boolean> {
    const url = interfaceId 
      ? `${this.baseUrl}/clear?interface_id=${interfaceId}`
      : `${this.baseUrl}/clear`;
    
    const response = await fetch(url, {
      method: 'DELETE'
    });
    if (!response.ok) throw new Error('清空测试记录失败');
    return true;
  }
}

// 根据环境变量选择服务实现
// 临时修复：接口测试服务暂时使用Mock模式
const useMockForInterfaceTest = true; // 暂时使用Mock
const interfaceTestService: IInterfaceTestService = useMockForInterfaceTest
  ? new MockInterfaceTestService()
  : new ApiInterfaceTestService();

export default interfaceTestService;
