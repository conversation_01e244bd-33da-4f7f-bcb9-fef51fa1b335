# 通用页面样式使用指南

## 概述

`page-common.scss` 包含了从数据源列表页面提取的通用样式，可以在其他页面中复用。

## 基本页面结构

```vue
<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><YourIcon /></el-icon>
        <span style="margin-left: 10px;">页面标题</span>
      </div>
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索..."
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button @click="loadData">刷新</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="管理" name="management">
        <!-- 表格内容 -->
        <el-table :data="tableData" style="width: 100%">
          <!-- 表格列 -->
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
/* 页面特有样式写在这里 */
</style>
```

## 可用的通用样式类

### 容器类
- `.container` - 主容器，包含边距、背景、圆角、阴影、滚动条
- `.page-header` - 页面头部，包含标题和操作按钮
- `.page-title` - 页面标题样式
- `.header-actions` - 头部操作按钮区域

### 组件类
- `.search-input` - 搜索框样式
- `.page-tabs` - 页签样式（30px高度）
- `.pagination-container` - 分页容器样式

### 表格样式
- 表格边框：无左右边框，只有上下边框
- 表头：加粗字体，浅灰背景
- 悬停效果：浅蓝色背景

### 按钮样式
- 小按钮：Element Plus标准样式
- 主要按钮：青蓝色 (#3FC8DD) - 统一的编辑按钮颜色
- 成功按钮：绿色
- 危险按钮：红色

### 对话框样式
- Element Plus标准样式
- 标题加粗
- 合适的内边距

### 滚动条样式
- 宽度：4px
- 颜色：青灰色 `#9db7bd`
- 悬停颜色：`#7a9ca3`

## 自定义样式

在页面的 `<style scoped>` 中添加页面特有的样式：

```scss
<style scoped>
/* 页面特有样式 */
.custom-class {
  /* 自定义样式 */
}

/* 覆盖通用样式（如需要） */
.page-title .el-icon {
  color: #your-color; /* 自定义图标颜色 */
}
</style>
```

## 响应式设计

通用样式包含了基本的响应式设计：
- 移动端：减小边距和内边距
- 头部操作区域：在小屏幕上垂直排列
- 搜索框：在小屏幕上占满宽度

## 注意事项

1. **不要修改通用样式文件**：页面特有的样式应该写在各自的页面中
2. **保持类名一致**：使用文档中提到的类名以获得正确的样式
3. **图标颜色**：页面标题图标默认使用主题色 `#3FC8DD`，可在页面中覆盖
4. **滚动条**：所有页面使用统一的滚动条样式
5. **页签高度**：统一使用30px高度的页签

## 示例页面

参考以下页面的实现：
- `src/views/datasource/DataSourceList.vue` - 数据源列表页面
- `src/views/client/ClientManagement.vue` - 客户端管理页面
