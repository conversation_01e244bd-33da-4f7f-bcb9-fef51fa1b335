import type { 
  InterfaceGroup, 
  InterfaceGroupRequest, 
  InterfaceGroupListResponse 
} from '@/types/interface-group';

/**
 * 接口分组管理 MOCK 数据
 * 用于：InterfaceGroup.vue 接口分组管理页面
 */

/**
 * 模拟接口分组列表数据
 */
export const mockInterfaceGroups: InterfaceGroup[] = [
  {
    id: 1,
    name: '项目管理接口',
    path_prefix: 'project',
    description: '项目管理相关接口，包括项目信息、任务管理、进度跟踪等',
    is_enabled: true,
    interface_count: 5,
    created_at: '2025-07-01 09:30:00',
    updated_at: '2025-07-10 14:20:00'
  },
  {
    id: 2,
    name: '财务管理接口',
    path_prefix: 'finance',
    description: '财务管理相关接口，包括账务处理、报表生成、预算管理等',
    is_enabled: true,
    interface_count: 8,
    created_at: '2025-07-02 10:15:00',
    updated_at: '2025-07-11 16:45:00'
  },
  {
    id: 3,
    name: '印章管理接口',
    path_prefix: 'seal',
    description: '印章管理相关接口，包括印章申请、审批流程、使用记录等',
    is_enabled: false,
    interface_count: 3,
    created_at: '2025-07-03 11:20:00',
    updated_at: '2025-07-09 09:30:00'
  },
  {
    id: 4,
    name: '办公自动化接口',
    path_prefix: 'office',
    description: '办公自动化相关接口，包括文档管理、流程审批、通知公告等',
    is_enabled: true,
    interface_count: 12,
    created_at: '2025-07-04 14:30:00',
    updated_at: '2025-07-12 11:15:00'
  },
  {
    id: 5,
    name: '企业门户接口',
    path_prefix: 'portal',
    description: '企业门户相关接口，包括首页展示、新闻发布、用户中心等',
    is_enabled: false,
    interface_count: 6,
    created_at: '2025-07-05 16:00:00',
    updated_at: '2025-07-08 10:00:00'
  },
  {
    id: 6,
    name: '人力资源接口',
    path_prefix: 'hr',
    description: '人力资源管理相关接口，包括员工信息、考勤管理、薪资核算等',
    is_enabled: true,
    interface_count: 9,
    created_at: '2025-07-06 10:45:00',
    updated_at: '2025-07-13 15:30:00'
  },
  {
    id: 7,
    name: '合同管理接口',
    path_prefix: 'contract',
    description: '合同管理相关接口，包括合同创建、审批、执行、归档等',
    is_enabled: true,
    interface_count: 7,
    created_at: '2025-07-07 11:00:00',
    updated_at: '2025-07-14 09:20:00'
  },
  {
    id: 8,
    name: '客户关系接口',
    path_prefix: 'crm',
    description: '客户关系管理相关接口，包括客户信息、销售机会、服务记录等',
    is_enabled: true,
    interface_count: 11,
    created_at: '2025-07-08 14:15:00',
    updated_at: '2025-07-15 16:30:00'
  },
  {
    id: 7,
    name: '合同系统',
    path_prefix: 'contract',
    description: '合同管理相关接口，包括合同签订、履约管理、到期提醒等',
    is_enabled: true,
    interface_count: 7,
    created_at: '2025-07-07 13:20:00',
    updated_at: '2025-07-14 09:45:00'
  }
];

/**
 * 模拟获取接口分组列表
 */
export const mockGetInterfaceGroups = async (
  page: number = 1,
  pageSize: number = 10,
  search?: string
): Promise<InterfaceGroupListResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  let filteredGroups = [...mockInterfaceGroups];
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    filteredGroups = filteredGroups.filter(group => 
      group.name.toLowerCase().includes(searchLower) ||
      group.path_prefix.toLowerCase().includes(searchLower) ||
      (group.description && group.description.toLowerCase().includes(searchLower))
    );
  }
  
  // 分页处理
  const total = filteredGroups.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filteredGroups.slice(start, end);
  
  return {
    items: data,
    total,
    page,
    size: pageSize,
    pages: Math.ceil(total / pageSize)
  };
};

/**
 * 模拟创建接口分组
 */
export const mockCreateInterfaceGroup = async (
  groupData: InterfaceGroupRequest
): Promise<InterfaceGroup> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 检查路径前缀是否重复
  const existingGroup = mockInterfaceGroups.find(
    group => group.path_prefix === groupData.path_prefix
  );
  if (existingGroup) {
    throw new Error(`路径前缀 "${groupData.path_prefix}" 已存在`);
  }
  
  // 检查分组名称是否重复
  const existingName = mockInterfaceGroups.find(
    group => group.name === groupData.name
  );
  if (existingName) {
    throw new Error(`分组名称 "${groupData.name}" 已存在`);
  }
  
  const newId = Math.max(...mockInterfaceGroups.map(g => g.id), 0) + 1;
  const now = new Date().toLocaleString();
  
  const newGroup: InterfaceGroup = {
    id: newId,
    name: groupData.name,
    path_prefix: groupData.path_prefix,
    description: groupData.description,
    is_enabled: groupData.is_enabled !== undefined ? groupData.is_enabled : true,
    interface_count: 0,
    created_at: now,
    updated_at: now
  };
  
  mockInterfaceGroups.push(newGroup);
  return { ...newGroup };
};

/**
 * 模拟更新接口分组
 */
export const mockUpdateInterfaceGroup = async (
  id: number,
  groupData: InterfaceGroupRequest
): Promise<InterfaceGroup> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const index = mockInterfaceGroups.findIndex(group => group.id === id);
  if (index === -1) {
    throw new Error(`分组ID ${id} 不存在`);
  }
  
  // 检查路径前缀是否重复（排除自己）
  const existingGroup = mockInterfaceGroups.find(
    group => group.path_prefix === groupData.path_prefix && group.id !== id
  );
  if (existingGroup) {
    throw new Error(`路径前缀 "${groupData.path_prefix}" 已存在`);
  }
  
  // 检查分组名称是否重复（排除自己）
  const existingName = mockInterfaceGroups.find(
    group => group.name === groupData.name && group.id !== id
  );
  if (existingName) {
    throw new Error(`分组名称 "${groupData.name}" 已存在`);
  }
  
  const updatedGroup: InterfaceGroup = {
    ...mockInterfaceGroups[index],
    name: groupData.name,
    path_prefix: groupData.path_prefix,
    description: groupData.description,
    is_enabled: groupData.is_enabled !== undefined ? groupData.is_enabled : mockInterfaceGroups[index].is_enabled,
    updated_at: new Date().toLocaleString()
  };
  
  mockInterfaceGroups[index] = updatedGroup;
  return { ...updatedGroup };
};

/**
 * 模拟删除接口分组
 */
export const mockDeleteInterfaceGroup = async (id: number): Promise<boolean> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const group = mockInterfaceGroups.find(g => g.id === id);
  if (!group) {
    throw new Error(`分组ID ${id} 不存在`);
  }
  
  // 检查是否有关联的接口
  if (group.interface_count && group.interface_count > 0) {
    throw new Error(`分组 "${group.name}" 下还有 ${group.interface_count} 个接口，请先删除相关接口`);
  }
  
  const index = mockInterfaceGroups.findIndex(g => g.id === id);
  mockInterfaceGroups.splice(index, 1);
  
  return true;
};
