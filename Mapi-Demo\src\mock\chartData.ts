// 图表模拟数据
export interface ChartData {
  labels: string[]
  datasets: {
    label?: string
    data: number[]
    backgroundColor?: string[] | string
    borderColor?: string[] | string
    borderWidth?: number
    tension?: number
    fill?: boolean
  }[]
}

export interface ChartConfig {
  type: string
  data: ChartData
  options: any
}

// 合同饼图数据
export const contractPieChartData: ChartConfig = {
  type: 'pie',
  data: {
    labels: ['服务合同', '销售合同', '采购合同', '其他合同'],
    datasets: [{
      data: [35, 25, 30, 10],
      backgroundColor: [
        '#165DFF',
        '#00B42A',
        '#FF7D00',
        '#86909C'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          boxWidth: 6,
          font: {
            size: 10
          }
        }
      }
    }
  }
}

// 付款饼图数据
export const paymentPieChartData: ChartConfig = {
  type: 'doughnut',
  data: {
    labels: ['在线支付', '银行转账', '现金支付', '其他方式'],
    datasets: [{
      data: [45, 30, 15, 10],
      backgroundColor: [
        '#165DFF',
        '#00B42A',
        '#FF7D00',
        '#86909C'
      ],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          boxWidth: 6,
          font: {
            size: 10
          }
        }
      }
    },
    cutout: '70%'
  }
}

// 结算柱状图数据
export const settlementBarChartData: ChartConfig = {
  type: 'bar',
  data: {
    labels: ['财务部', '销售部', '市场部', '技术部', '人力资源部'],
    datasets: [{
      label: '结算金额(万元)',
      data: [120, 85, 60, 45, 30],
      backgroundColor: ['#165DFF']
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 10
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10
          }
        }
      }
    }
  }
}

// 销售趋势线图数据
export const salesLineChartData: ChartConfig = {
  type: 'line',
  data: {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    datasets: [{
      label: '销售额(万元)',
      data: [65, 59, 80, 81, 56, 55, 72, 78, 85, 90, 92, 100],
      borderColor: '#165DFF',
      backgroundColor: ['rgba(22, 93, 255, 0.1)'],
      tension: 0.4,
      fill: true
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 10
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10
          }
        }
      }
    }
  }
}

// 组件数据
export interface ComponentItem {
  id: number
  name: string
  description: string
  type: string
  createdAt: string
  updatedAt: string
}

export const componentsData: ComponentItem[] = [
  {
    id: 1,
    name: '合同饼图',
    description: '展示不同类型合同的占比情况',
    type: 'pie',
    createdAt: '2025-07-01',
    updatedAt: '2025-07-20'
  },
  {
    id: 2,
    name: '付款饼图',
    description: '展示不同付款方式的占比情况',
    type: 'pie',
    createdAt: '2025-07-02',
    updatedAt: '2025-07-19'
  },
  {
    id: 3,
    name: '结算柱状图',
    description: '展示各部门月度结算金额对比',
    type: 'bar',
    createdAt: '2025-07-03',
    updatedAt: '2025-07-18'
  },
  {
    id: 4,
    name: '数据地图',
    description: '展示各地区业务分布情况',
    type: 'map',
    createdAt: '2025-07-04',
    updatedAt: '2025-07-17'
  },
  {
    id: 5,
    name: '销售趋势图',
    description: '展示近12个月销售趋势变化',
    type: 'line',
    createdAt: '2025-07-05',
    updatedAt: '2025-07-16'
  },
  {
    id: 6,
    name: 'KPI仪表盘',
    description: '关键绩效指标实时监控',
    type: 'dashboard',
    createdAt: '2025-07-06',
    updatedAt: '2025-07-15'
  }
]

// 画布数据
export interface CanvasItem {
  id: number
  name: string
  description: string
  components: number[] // 组件ID数组
  owner: string
  createdAt: string
  updatedAt: string
  componentCount: number
}

export const canvasesData: CanvasItem[] = [
  {
    id: 1,
    name: '合同驾驶舱',
    description: '合同管理相关数据可视化',
    components: [1, 2, 3, 5],
    owner: '系统管理员',
    createdAt: '2025-07-01',
    updatedAt: '2025-07-20',
    componentCount: 4
  },
  {
    id: 2,
    name: '资金驾驶舱',
    description: '资金管理相关数据可视化',
    components: [4, 6],
    owner: '财务经理',
    createdAt: '2025-07-02',
    updatedAt: '2025-07-18',
    componentCount: 2
  },
  {
    id: 3,
    name: '销售分析画布',
    description: '销售数据多维度分析',
    components: [5, 3],
    owner: '销售主管',
    createdAt: '2025-07-03',
    updatedAt: '2025-07-15',
    componentCount: 2
  },
  {
    id: 4,
    name: '客户分析画布',
    description: '客户数据全方位分析',
    components: [1, 2, 3, 4],
    owner: '市场经理',
    createdAt: '2025-07-04',
    updatedAt: '2025-07-10',
    componentCount: 4
  }
]

// 报表数据
export interface ReportItem {
  id: number
  name: string
  canvasId: number
  canvasName: string
  type: string
  period: string
  owner: string
  createdAt: string
  updatedAt: string
}

export const reportsData: ReportItem[] = [
  {
    id: 1,
    name: '月度合同分析报表',
    canvasId: 1,
    canvasName: '合同驾驶舱',
    type: '月报',
    period: '2025年6月',
    owner: '系统管理员',
    createdAt: '2025-07-05 14:30',
    updatedAt: '2025-07-05 14:30'
  },
  {
    id: 2,
    name: '季度资金分析报表',
    canvasId: 2,
    canvasName: '资金驾驶舱',
    type: '季报',
    period: '2025年第二季度',
    owner: '财务经理',
    createdAt: '2025-07-10 10:15',
    updatedAt: '2025-07-10 10:15'
  },
  {
    id: 3,
    name: '区域销售分析报表',
    canvasId: 3,
    canvasName: '销售分析画布',
    type: '半年报',
    period: '2025年上半年',
    owner: '销售主管',
    createdAt: '2025-07-15 09:45',
    updatedAt: '2025-07-15 09:45'
  },
  {
    id: 4,
    name: '客户分析报表',
    canvasId: 4,
    canvasName: '客户分析画布',
    type: '半年报',
    period: '2025年上半年',
    owner: '市场经理',
    createdAt: '2025-07-20 16:20',
    updatedAt: '2025-07-20 16:20'
  }
]