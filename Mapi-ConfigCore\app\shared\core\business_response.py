"""
业务逻辑响应处理器
处理业务逻辑判断后的响应和日志记录
"""

from typing import Dict, Any, List
from .base_response import BaseResponseHandler, ErrorType


class BusinessResponse(BaseResponseHandler):
    """业务逻辑响应处理器"""
    
    @classmethod
    def handle_resource_conflict(
        cls,
        resource_type: str,
        resource_id: int,
        conflict_reason: str,
        related_items: List[str] = None,
        suggestion: str = None
    ) -> Dict[str, Any]:
        """
        处理资源冲突情况
        
        Args:
            resource_type: 资源类型（如：数据源、接口分组等）
            resource_id: 资源ID
            conflict_reason: 冲突原因
            related_items: 相关项目列表
            suggestion: 建议操作
        
        Returns:
            标准错误响应格式
        """
        detail = {
            f"{resource_type.lower()}_id": resource_id,
            "conflict_reason": conflict_reason
        }
        
        if related_items:
            detail["related_items"] = related_items
            detail["count"] = len(related_items)
        
        if suggestion:
            detail["suggestion"] = suggestion
        
        # 记录业务日志
        cls.log_business_error(
            ErrorType.资源冲突,
            f"无法删除{resource_type}，{conflict_reason}",
            detail
        )
        
        # 返回错误响应
        return cls.create_error_response(
            success=False,
            message=f"无法删除{resource_type}，{conflict_reason}",
            error_code="RESOURCE_CONFLICT",
            detail=detail
        )
    
    @classmethod
    def handle_resource_not_found(
        cls,
        resource_type: str,
        resource_id: int
    ) -> Dict[str, Any]:
        """
        处理资源未找到情况
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
        
        Returns:
            标准错误响应格式
        """
        detail = {f"{resource_type.lower()}_id": resource_id}
        
        # 记录业务日志
        cls.log_business_error(
            ErrorType.资源未找到,
            f"{resource_type}不存在",
            detail
        )
        
        # 返回错误响应
        return cls.create_error_response(
            success=False,
            message=f"{resource_type}不存在",
            error_code="RESOURCE_NOT_FOUND",
            detail=detail
        )
    
    @classmethod
    def handle_validation_error(
        cls,
        message: str,
        field_errors: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        处理验证错误情况
        
        Args:
            message: 错误消息
            field_errors: 字段错误详情
        
        Returns:
            标准错误响应格式
        """
        detail = {}
        if field_errors:
            detail["field_errors"] = field_errors
        
        # 记录业务日志
        cls.log_business_error(
            ErrorType.验证错误,
            message,
            detail
        )
        
        # 返回错误响应
        return cls.create_error_response(
            success=False,
            message=message,
            error_code="VALIDATION_ERROR",
            detail=detail
        )
    
    @classmethod
    def handle_success_operation(
        cls,
        operation: str,
        resource_type: str = None,
        resource_id: int = None,
        data: Any = None
    ) -> Dict[str, Any]:
        """
        处理成功操作
        
        Args:
            operation: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            data: 返回数据
        
        Returns:
            标准成功响应格式
        """
        message = f"{operation}成功"
        
        # 记录业务日志
        log_detail = {}
        if resource_type and resource_id:
            log_detail[f"{resource_type.lower()}_id"] = resource_id
        
        cls.log_business_info(f"{operation}成功", log_detail)
        
        # 返回成功响应
        return cls.create_success_response(message=message, data=data)
