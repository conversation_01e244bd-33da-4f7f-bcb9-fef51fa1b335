// 客户端管理相关类型定义

// 客户端基本信息
export interface Client {
  id: number;
  clientId: string;                    // 客户端ID（16位随机字母）
  clientName: string;                  // 客户端名称
  clientSecret: string;                // 安全密钥（32位随机字符串）
  description?: string;                // 描述
  tokenExpiresIn: number;              // Token有效期（秒）
  status: 'enabled' | 'disabled';      // 状态
  createdAt: string;                   // 创建时间
  updatedAt: string;                   // 更新时间
  // 权限统计信息
  permissionCount?: number;            // 已授权接口数量
  groupCount?: number;                 // 已授权分组数量
  permissionGroups?: string[];         // 已授权分组名称列表
  permissionUpdatedAt?: string | null; // 权限最后更新时间
}

// 客户端表单数据
export interface ClientFormData {
  clientName: string;
  description?: string;
  tokenExpiresIn: number;
  status: 'enabled' | 'disabled';
  clientId?: string;                   // 编辑时存在
  clientSecret?: string;               // 编辑时存在
  createdAt?: string;                  // 编辑时存在
  updatedAt?: string;                  // 编辑时存在
}

// 客户端权限
export interface ClientPermission {
  id: number;
  clientId: string;                    // 客户端ID
  resourceType: 'group' | 'interface'; // 资源类型
  resourceId: string;                  // 资源ID（分组ID或接口ID）
  createdAt: string;                   // 创建时间
}

// 客户端权限表单数据
export interface ClientPermissionFormData {
  clientId: string;
  permissions: {
    resourceType: 'group' | 'interface';
    resourceId: string;
  }[];
}

// 接口分组信息（用于权限配置）
export interface InterfaceGroup {
  id: string;
  name: string;
  description?: string;
  status: 'enabled' | 'disabled';
}

// 接口信息（用于权限配置）
export interface InterfaceInfo {
  id: string;
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description?: string;
  accessType: 'public' | 'private';    // 访问类型
  status: 'enabled' | 'disabled';
  groupId: string;                     // 所属分组ID
  type: 'interface';                   // 类型标识
}

// 权限配置树节点（用于表格树显示）
export interface PermissionTreeNode {
  id: string;
  name: string;
  type: 'group' | 'interface';
  path?: string;                       // 接口路径
  method?: string;                     // HTTP方法
  description?: string;                // 描述
  accessType?: 'public' | 'private';   // 访问类型
  status: 'enabled' | 'disabled';
  groupId?: string;                    // 所属分组ID（接口节点）
  children?: PermissionTreeNode[];     // 子节点（分组节点）
  hasChildren?: boolean;               // 是否有子节点
}

// 客户端列表查询参数
export interface ClientQuery {
  page?: number;
  pageSize?: number;
  clientName?: string;                 // 客户端名称搜索
  status?: 'enabled' | 'disabled';     // 状态筛选
}

// 客户端列表响应
export interface ClientListResponse {
  data: Client[];
  total: number;
  page: number;
  pageSize: number;
}

// Token有效期选项
export interface TokenExpiresOption {
  label: string;
  value: number;                       // 秒数，0表示永不过期
}

// Token有效期配置
export const TOKEN_EXPIRES_OPTIONS: TokenExpiresOption[] = [
  { label: '1天', value: 86400 },
  { label: '7天', value: 604800 },
  { label: '30天', value: 2592000 },
  { label: '90天', value: 7776000 },
  { label: '永不过期', value: 0 }
];

// HTTP方法标签类型映射
export const HTTP_METHOD_TAG_TYPES: Record<string, string> = {
  'GET': 'success',
  'POST': 'primary',
  'PUT': 'warning',
  'DELETE': 'danger',
  'PATCH': 'info'
};

// 客户端ID生成配置
export const CLIENT_ID_CONFIG = {
  length: 16,                          // 客户端ID长度
  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
};

// 客户端密钥生成配置
export const CLIENT_SECRET_CONFIG = {
  length: 32,                          // 密钥长度
  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
};

// 客户端状态选项
export const CLIENT_STATUS_OPTIONS = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' }
];

// 访问类型选项
export const ACCESS_TYPE_OPTIONS = [
  { label: '公有', value: 'public' },
  { label: '私有', value: 'private' }
];
