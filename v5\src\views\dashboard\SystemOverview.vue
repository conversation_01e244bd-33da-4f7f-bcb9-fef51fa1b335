<template>
  <div class="container">
    <!-- 系统状态总览 -->
    <div class="system-status-overview">
      <div class="status-indicator">
        <div class="status-circle" :class="systemStatus.level">
          <el-icon class="status-icon">
            <component :is="systemStatus.icon" />
          </el-icon>
        </div>
        <div class="status-info">
          <h2 class="status-title">{{ systemStatus.title }}</h2>
          <p class="status-description">{{ systemStatus.description }}</p>
          <p class="status-uptime">系统运行时间: {{ systemStatus.uptime }}</p>
        </div>
      </div>
      <div class="health-score">
        <div class="score-number">{{ systemStatus.healthScore }}</div>
        <div class="score-label">健康评分</div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <div
        v-for="metric in coreMetrics"
        :key="metric.id"
        class="metric-card"
        :class="metric.status"
      >
        <div class="metric-header">
          <el-icon class="metric-icon">
            <component :is="metric.icon" />
          </el-icon>
          <span class="metric-name">{{ metric.name }}</span>
        </div>
        <div class="metric-value">
          <span class="value-number">{{ metric.value }}</span>
          <span class="value-unit">{{ metric.unit }}</span>
        </div>
        <div class="metric-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: metric.percentage + '%' }"
              :class="metric.status"
            ></div>
          </div>
          <span class="progress-text">{{ metric.percentage }}%</span>
        </div>
        <div class="metric-trend" :class="metric.trend">
          <el-icon>
            <component :is="metric.trendIcon" />
          </el-icon>
          <span>{{ metric.trendText }}</span>
        </div>
      </div>
    </div>

    <!-- 系统活动 -->
    <div class="activity-section">
      <h3 class="section-title">
        <el-icon><Clock /></el-icon>
        最近活动
      </h3>
      <div class="activity-content">
        <div class="alerts-panel" v-if="recentAlerts.length > 0">
          <h4 class="panel-title">
            <el-icon class="warning-icon"><Warning /></el-icon>
            活跃告警
          </h4>
          <div class="alert-list">
            <div
              v-for="alert in recentAlerts"
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <div class="alert-content">
                <span class="alert-message">{{ alert.message }}</span>
                <span class="alert-time">{{ alert.time }}</span>
              </div>
              <div class="alert-status" :class="alert.level">
                {{ alert.levelText }}
              </div>
            </div>
          </div>
        </div>

        <div class="events-panel">
          <h4 class="panel-title">
            <el-icon><List /></el-icon>
            系统事件
          </h4>
          <div class="event-list">
            <div
              v-for="event in recentEvents"
              :key="event.id"
              class="event-item"
            >
              <div class="event-dot" :class="event.type"></div>
              <div class="event-content">
                <span class="event-message">{{ event.message }}</span>
                <span class="event-time">{{ event.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { markRaw,  ref, onMounted } from 'vue'
import {
  Connection,
  Timer,
  User,
  DataLine,
  Cpu,
  Memo,
  CircleCheck,
  ArrowUp,
  ArrowDown,
  Minus,
  Clock,
  Warning,
  List
} from '@element-plus/icons-vue'

// 系统状态总览
const systemStatus = ref({
  level: 'healthy', // healthy, warning, error
  icon: markRaw(CircleCheck),
  title: '系统运行正常',
  description: '所有核心服务运行稳定，无严重告警',
  uptime: '15天 8小时 32分钟',
  healthScore: 96
})

// 核心指标数据
const coreMetrics = ref([
  {
    id: 'api-success',
    name: 'API成功率',
    value: '99.8',
    unit: '%',
    percentage: 99.8,
    status: 'excellent',
    trend: 'up',
    trendIcon: markRaw(ArrowUp),
    trendText: '较昨日 +0.2%',
    icon: markRaw(Connection)
  },
  {
    id: 'response-time',
    name: '响应时间',
    value: '142',
    unit: 'ms',
    percentage: 85,
    status: 'good',
    trend: 'down',
    trendIcon: markRaw(ArrowDown),
    trendText: '较昨日 -8ms',
    icon: markRaw(Timer)
  },
  {
    id: 'online-users',
    name: '在线用户',
    value: '1,247',
    unit: '人',
    percentage: 62,
    status: 'good',
    trend: 'up',
    trendIcon: ArrowUp,
    trendText: '较昨日 +156',
    icon: markRaw(User)
  },
  {
    id: 'database',
    name: '数据库连接',
    value: '35',
    unit: '/100',
    percentage: 35,
    status: 'excellent',
    trend: 'stable',
    trendIcon: markRaw(Minus),
    trendText: '连接稳定',
    icon: markRaw(DataLine)
  },
  {
    id: 'cpu-usage',
    name: 'CPU使用率',
    value: '42',
    unit: '%',
    percentage: 42,
    status: 'excellent',
    trend: 'stable',
    trendIcon: Minus,
    trendText: '负载正常',
    icon: markRaw(Cpu)
  },
  {
    id: 'memory-usage',
    name: '内存使用率',
    value: '68',
    unit: '%',
    percentage: 68,
    status: 'warning',
    trend: 'up',
    trendIcon: ArrowUp,
    trendText: '较昨日 +5%',
    icon: markRaw(Memo)
  }
])

// 最近告警
const recentAlerts = ref([
  {
    id: 1,
    level: 'warning',
    levelText: '警告',
    message: '内存使用率持续偏高，建议关注',
    time: '2分钟前'
  },
  {
    id: 2,
    level: 'info',
    levelText: '信息',
    message: '定时任务执行完成',
    time: '15分钟前'
  }
])

// 最近事件
const recentEvents = ref([
  {
    id: 1,
    type: 'success',
    message: '系统备份任务执行成功',
    time: '30分钟前'
  },
  {
    id: 2,
    type: 'info',
    message: '新用户注册: user_12847',
    time: '45分钟前'
  },
  {
    id: 3,
    type: 'info',
    message: 'API接口调用峰值: 1,250 req/min',
    time: '1小时前'
  },
  {
    id: 4,
    type: 'success',
    message: '数据库优化任务完成',
    time: '2小时前'
  }
])

// 模拟数据更新
const updateMetrics = () => {
  // 模拟实时数据更新
  coreMetrics.value.forEach(metric => {
    if (metric.id === 'online-users') {
      const change = Math.floor(Math.random() * 10) - 5
      const newValue = parseInt(metric.value.replace(',', '')) + change
      metric.value = newValue.toLocaleString()
    }
  })
}

// 组件挂载后设置定时更新
onMounted(() => {
  // 每30秒更新一次数据
  setInterval(updateMetrics, 30000)
})
</script>

<style scoped>
/* 现代化系统总览页面样式 */

/* 使用标准容器样式 */
.container {
  max-width: calc(100% - 20px);
  margin: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  padding: 20px;
  max-height: calc(100vh - 20px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 内层滚动条样式 */
.container::-webkit-scrollbar {
  width: 4px;
}

.container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.container::-webkit-scrollbar-thumb {
  background: #9db7bd;
  border-radius: 2px;
  transition: background 0.3s ease;
}

.container::-webkit-scrollbar-thumb:hover {
  background: #7a9ca3;
}

/* Firefox滚动条样式 */
.container {
  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
}

/* 系统状态总览 */
.system-status-overview {
  background: #f8fafc;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e2e8f0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.status-circle.healthy {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.status-circle.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.status-circle.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.status-icon {
  font-size: 28px;
}

.status-info h2 {
  margin: 0 0 6px 0;
  font-size: 20px;
  color: #1f2937;
  font-weight: 600;
}

.status-description {
  margin: 0 0 6px 0;
  color: #6b7280;
  font-size: 14px;
}

.status-uptime {
  margin: 0;
  color: #9ca3af;
  font-size: 12px;
}

.health-score {
  text-align: center;
}

.score-number {
  font-size: 36px;
  font-weight: 700;
  color: #3FC8DD;
  line-height: 1;
}

.score-label {
  color: #6b7280;
  font-size: 12px;
  margin-top: 4px;
}

/* 核心指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.metric-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(63, 200, 221, 0.15);
  border-color: #3FC8DD;
}

.metric-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.metric-icon {
  font-size: 20px;
  color: #3FC8DD;
  margin-right: 8px;
}

.metric-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.metric-value {
  margin-bottom: 12px;
}

.value-number {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.value-unit {
  font-size: 14px;
  color: #6b7280;
  margin-left: 2px;
}

.metric-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-fill.excellent {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-fill.good {
  background: linear-gradient(90deg, #3FC8DD, #0891b2);
}

.progress-fill.warning {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 35px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.metric-trend.up {
  color: #10b981;
}

.metric-trend.down {
  color: #ef4444;
}

.metric-trend.stable {
  color: #6b7280;
}

/* 活动区域 */
.activity-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #1f2937;
  font-weight: 600;
}

.activity-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.warning-icon {
  color: #f59e0b;
}

/* 告警列表 */
.alert-list {
  margin-bottom: 8px;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #fef2f2;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
  margin-bottom: 8px;
}

.alert-item.warning {
  background: #fef3c7;
  border-left-color: #f59e0b;
}

.alert-item.info {
  background: #dbeafe;
  border-left-color: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-message {
  display: block;
  color: #374151;
  font-weight: 500;
  margin-bottom: 2px;
  font-size: 13px;
}

.alert-time {
  color: #6b7280;
  font-size: 11px;
}

.alert-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.alert-status.warning {
  background: #fbbf24;
  color: white;
}

.alert-status.info {
  background: #3b82f6;
  color: white;
}

/* 事件列表 */
.event-list {
  margin-bottom: 8px;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.event-item:last-child {
  border-bottom: none;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.event-dot.success {
  background: #10b981;
}

.event-dot.info {
  background: #3b82f6;
}

.event-dot.warning {
  background: #f59e0b;
}

.event-content {
  flex: 1;
}

.event-message {
  display: block;
  color: #374151;
  font-size: 13px;
  margin-bottom: 1px;
}

.event-time {
  color: #9ca3af;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .activity-content {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .system-status-overview {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .status-indicator {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
