<template>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <el-icon><Lock /></el-icon>
        <span>权限管理</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchParams.keyword"
          placeholder="搜索客户端名称或ID"
          @search="handleSearch"
        />
        <el-button
          :icon="Refresh"
          @click="loadData"
          class="refresh-btn"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; min-width: 1000px;"
      :row-style="{ height: '60px' }"
      :cell-style="{ padding: '12px 0' }"
    >
      <!-- 空状态 -->
      <template #empty>
        <el-empty
          description="暂无权限数据"
          :image-size="120"
        >
          <template #description>
            <p>还没有任何客户端权限配置</p>
            <p>请先在客户端管理中创建客户端</p>
          </template>
          <el-button type="primary" @click="$router.push('/client/management')">
            <el-icon><User /></el-icon>
            前往客户端管理
          </el-button>
        </el-empty>
      </template>
      <el-table-column prop="clientName" label="客户端名称" width="180">
        <template #default="{ row }">
          <div class="client-name-cell">
            <el-icon class="client-icon"><Monitor /></el-icon>
            <el-button
              type="text"
              class="client-name-btn"
              @click="handleViewPermissions(row)"
            >
              {{ row.clientName }}
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="clientId" label="客户端ID" width="180">
        <template #default="{ row }">
          <span class="client-id">{{ row.clientId }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="权限统计" min-width="200">
        <template #default="{ row }">
          <div class="permission-stats">
            <div class="stats-row">
              <el-tag type="primary" size="small">
                {{ row.permissionCount || 0 }} 个接口
              </el-tag>
              <el-tag type="success" size="small">
                {{ row.groupCount || 0 }} 个分组
              </el-tag>
            </div>
            <div class="stats-groups" v-if="row.permissionGroups && row.permissionGroups.length">
              <el-tag 
                v-for="group in row.permissionGroups.slice(0, 3)" 
                :key="group"
                size="small"
                class="group-tag"
              >
                {{ group }}
              </el-tag>
              <span v-if="row.permissionGroups.length > 3" class="more-groups">
                +{{ row.permissionGroups.length - 3 }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'enabled' ? 'success' : 'danger'">
            {{ row.status === 'enabled' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="updatedAt" label="权限更新时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.permissionUpdatedAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleViewPermissions(row)">
            查看权限
          </el-button>
          <el-button type="primary" size="small" @click="handlePermissionSetting(row)">
            权限设置
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <PaginationComponent
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      @change="loadData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock, Monitor, Refresh, User } from '@element-plus/icons-vue'
import SearchComponent from '@/components/common/SearchComponent.vue'
import PaginationComponent from '@/components/common/PaginationComponent.vue'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import clientService from '@/services/client.service'

// 全局状态
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 搜索参数
const searchParams = reactive({
  keyword: ''
})

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchParams.keyword
    }
    
    const result = await clientService.getClients(params)
    tableData.value = result.data
    pagination.total = result.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 查看客户端权限
const handleViewPermissions = (row) => {
  drawerMessenger.showDrawer({
    title: `客户端权限 - ${row.clientName}`,
    component: 'ClientPermissionViewForm',
    size: '40%',
    props: {
      clientData: { ...row }
    }
  })
}

// 权限设置
const handlePermissionSetting = (row) => {
  drawerMessenger.showDrawer({
    title: `权限设置 - ${row.clientName}`,
    component: 'ClientPermissionSettingForm',
    size: '40%',
    props: {
      clientData: { ...row }
    }
  })
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 头部操作区域样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;

  .refresh-btn {
    height: 32px;
  }
}

/* 客户端名称样式 */
.client-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .client-icon {
    color: #409eff;
    font-size: 16px;
  }

  .client-name-btn {
    font-weight: 500;
    color: #303133 !important;

    &:hover {
      color: #409eff !important;
      text-decoration: underline;
    }
  }
}

.client-id {
  font-family: monospace;
  font-size: 13px;
  color: #606266;
}

/* 权限统计样式 */
.permission-stats {
  .stats-row {
    display: flex;
    gap: 8px;
    margin-bottom: 6px;
  }
  
  .stats-groups {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
    
    .group-tag {
      font-size: 11px;
      background: #f0f9ff;
      color: #0369a1;
      border-color: #bae6fd;
    }
    
    .more-groups {
      font-size: 11px;
      color: #909399;
    }
  }
}
</style>
