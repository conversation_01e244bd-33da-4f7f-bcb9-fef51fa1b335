#!/usr/bin/env python3
"""
ODBC驱动程序检查测试
检查系统中可用的数据库驱动程序
"""

def test_database_drivers():
    """测试数据库驱动程序可用性"""
    print("🔍 检查数据库驱动程序...")
    print("=" * 50)
    
    drivers_status = {}
    
    # 测试pymssql (SQL Server - 纯Python驱动)
    try:
        import pymssql
        drivers_status['pymssql'] = {
            'available': True,
            'version': getattr(pymssql, '__version__', 'unknown'),
            'description': 'SQL Server (纯Python驱动)'
        }
        print("✅ pymssql (SQL Server) - 可用")
    except ImportError:
        drivers_status['pymssql'] = {
            'available': False,
            'description': 'SQL Server (纯Python驱动)',
            'install_cmd': 'poetry add pymssql'
        }
        print("❌ pymssql (SQL Server) - 未安装")
    
    # 测试pymysql (MySQL)
    try:
        import pymysql
        drivers_status['pymysql'] = {
            'available': True,
            'version': pymysql.__version__,
            'description': 'MySQL (纯Python驱动)'
        }
        print("✅ pymysql (MySQL) - 可用")
    except ImportError:
        drivers_status['pymysql'] = {
            'available': False,
            'description': 'MySQL (纯Python驱动)',
            'install_cmd': 'poetry add pymysql'
        }
        print("❌ pymysql (MySQL) - 未安装")
    
    # 测试psycopg2 (PostgreSQL)
    try:
        import psycopg2
        drivers_status['psycopg2'] = {
            'available': True,
            'version': psycopg2.__version__,
            'description': 'PostgreSQL'
        }
        print("✅ psycopg2 (PostgreSQL) - 可用")
    except ImportError:
        drivers_status['psycopg2'] = {
            'available': False,
            'description': 'PostgreSQL',
            'install_cmd': 'poetry add psycopg2-binary'
        }
        print("❌ psycopg2 (PostgreSQL) - 未安装")
    
    # 测试cx_Oracle (Oracle)
    try:
        import cx_Oracle
        drivers_status['cx_Oracle'] = {
            'available': True,
            'version': cx_Oracle.__version__,
            'description': 'Oracle'
        }
        print("✅ cx_Oracle (Oracle) - 可用")
    except ImportError:
        drivers_status['cx_Oracle'] = {
            'available': False,
            'description': 'Oracle',
            'install_cmd': 'poetry add cx-Oracle'
        }
        print("❌ cx_Oracle (Oracle) - 未安装")
    
    # 测试sqlite3 (内置)
    try:
        import sqlite3
        drivers_status['sqlite3'] = {
            'available': True,
            'version': sqlite3.sqlite_version,
            'description': 'SQLite (Python内置)'
        }
        print("✅ sqlite3 (SQLite) - 可用")
    except ImportError:
        drivers_status['sqlite3'] = {
            'available': False,
            'description': 'SQLite (Python内置)'
        }
        print("❌ sqlite3 (SQLite) - 不可用")
    
    print()
    print("📊 驱动程序统计:")
    available_count = sum(1 for status in drivers_status.values() if status['available'])
    total_count = len(drivers_status)
    print(f"可用: {available_count}/{total_count}")
    
    # 显示安装建议
    missing_drivers = [name for name, status in drivers_status.items() 
                      if not status['available'] and 'install_cmd' in status]
    
    if missing_drivers:
        print()
        print("📥 安装建议:")
        for driver_name in missing_drivers:
            status = drivers_status[driver_name]
            print(f"  {status['install_cmd']}  # {status['description']}")
    
    return drivers_status

def test_pyodbc_fallback():
    """测试pyodbc作为备用驱动（如果可用）"""
    print("\n🔍 检查ODBC驱动程序（备用方案）...")
    print("=" * 50)
    
    try:
        import pyodbc
        print("✅ pyodbc 可用")
        
        drivers = pyodbc.drivers()
        if drivers:
            print(f"找到 {len(drivers)} 个ODBC驱动程序:")
            
            sql_server_drivers = [d for d in drivers if 'SQL Server' in d]
            if sql_server_drivers:
                print("🔵 SQL Server ODBC驱动:")
                for driver in sql_server_drivers:
                    print(f"  ✓ {driver}")
            else:
                print("❌ 未找到SQL Server ODBC驱动")
        else:
            print("❌ 未找到任何ODBC驱动程序")
            
        return True
        
    except ImportError:
        print("ℹ️  pyodbc 未安装（这是正常的，我们使用纯Python驱动）")
        return False

def test_connection_methods():
    """测试不同的连接方法优先级"""
    print("\n🔍 测试连接方法优先级...")
    print("=" * 50)
    
    # SQL Server连接方法优先级
    print("SQL Server连接方法:")
    print("1. pymssql (推荐 - 纯Python)")
    print("2. pyodbc (备用 - 需要ODBC驱动)")
    
    # 其他数据库
    print("\n其他数据库连接方法:")
    print("- MySQL: pymysql (纯Python)")
    print("- PostgreSQL: psycopg2 (纯Python)")
    print("- Oracle: cx_Oracle (纯Python)")
    print("- SQLite: sqlite3 (Python内置)")
    
    return True

def run_driver_tests():
    """运行所有驱动程序测试"""
    print("🚀 数据库驱动程序测试")
    print("=" * 50)
    
    tests = [
        ("数据库驱动程序", test_database_drivers),
        ("ODBC备用驱动", test_pyodbc_fallback),
        ("连接方法说明", test_connection_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    return results

if __name__ == "__main__":
    run_driver_tests()
