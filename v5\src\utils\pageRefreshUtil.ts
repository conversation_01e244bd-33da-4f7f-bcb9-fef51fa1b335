/**
 * 页面刷新工具类
 * 提供统一的页面刷新机制，支持智能分页策略
 */
import { ElMessage } from 'element-plus';

interface RefreshOptions {
  /** 页面标识 */
  pageKey: string;
  /** 成功提示消息 */
  successMessage?: string;
  /** 是否显示成功提示 */
  showMessage?: boolean;
  /** 操作类型：add=新增(跳转第1页), edit=修改(保持当前页), delete=删除(保持当前页) */
  operation?: 'add' | 'edit' | 'delete';
}

class PageRefreshUtil {
  private static instance: PageRefreshUtil;
  
  private constructor() {}
  
  static getInstance(): PageRefreshUtil {
    if (!PageRefreshUtil.instance) {
      PageRefreshUtil.instance = new PageRefreshUtil();
    }
    return PageRefreshUtil.instance;
  }

  /**
   * 注册页面刷新方法
   * @param pageKey 页面标识
   * @param refreshMethod 刷新方法（保持当前页）
   * @param refreshToFirstPageMethod 刷新到第一页的方法（可选）
   */
  registerRefresh(
    pageKey: string,
    refreshMethod: () => void | Promise<void>,
    refreshToFirstPageMethod?: () => void | Promise<void>
  ): void {
    // 暴露到全局，供其他组件调用
    (window as any)[`refresh${this.capitalize(pageKey)}`] = refreshMethod;

    // 如果提供了跳转第一页的方法，也暴露到全局
    if (refreshToFirstPageMethod) {
      (window as any)[`refresh${this.capitalize(pageKey)}ToFirstPage`] = refreshToFirstPageMethod;
    }
    
    // 监听对应的刷新事件
    const eventName = `refresh${this.capitalize(pageKey)}`;
    const handleRefreshEvent = () => {
      console.log(`收到 ${eventName} 事件，开始刷新...`);
      refreshMethod();
    };
    
    window.addEventListener(eventName, handleRefreshEvent);
    
    // 存储事件处理器，用于清理
    (window as any)[`${eventName}Handler`] = handleRefreshEvent;
  }

  /**
   * 注销页面刷新方法
   * @param pageKey 页面标识
   */
  unregisterRefresh(pageKey: string): void {
    const methodName = `refresh${this.capitalize(pageKey)}`;
    const eventName = `refresh${this.capitalize(pageKey)}`;
    const handlerName = `${eventName}Handler`;
    
    // 清理全局方法
    delete (window as any)[methodName];
    
    // 清理事件监听
    const handler = (window as any)[handlerName];
    if (handler) {
      window.removeEventListener(eventName, handler);
      delete (window as any)[handlerName];
    }
  }

  /**
   * 触发页面刷新
   * @param options 刷新选项
   */
  async triggerRefresh(options: RefreshOptions): Promise<boolean> {
    const { pageKey, successMessage, showMessage = true, operation } = options;



    let refreshed = false;

    // 主要方案：iframe 方法调用
    refreshed = await this.tryIframeRefresh(pageKey, operation);

    // 备用方案：延迟重试
    if (!refreshed) {
      refreshed = await this.tryDelayedRefresh(pageKey, operation);
    }


    // 显示结果
    if (refreshed && showMessage) {
      const message = successMessage || `${pageKey} 数据刷新成功`;
      ElMessage.success(message);
    } else if (!refreshed && showMessage) {
      ElMessage.warning('页面刷新失败，请手动刷新');
    }

    return refreshed;
  }

  /**
   * 尝试通过 iframe 刷新
   */
  private async tryIframeRefresh(pageKey: string, operation?: string): Promise<boolean> {
    try {
      const iframes = document.querySelectorAll('iframe');

      for (let i = 0; i < iframes.length; i++) {
        const iframe = iframes[i];
        try {
          const iframeWindow = iframe.contentWindow;
          if (!iframeWindow) continue;

          let methodName = `refresh${this.capitalize(pageKey)}`;

          // 根据操作类型选择方法
          if (operation === 'add') {
            const firstPageMethodName = `refresh${this.capitalize(pageKey)}ToFirstPage`;
            if ((iframeWindow as any)[firstPageMethodName]) {
              await (iframeWindow as any)[firstPageMethodName]();
              return true;
            }
          }

          // 默认方法
          if ((iframeWindow as any)[methodName]) {
            await (iframeWindow as any)[methodName]();
            return true;
          }
        } catch (error) {
          // 忽略iframe访问错误
        }
      }
    } catch (error) {
      // 忽略iframe刷新错误
    }
    return false;
  }







  /**
   * 延迟重试刷新（解决时差问题）
   */
  private async tryDelayedRefresh(pageKey: string, operation?: string): Promise<boolean> {
    // 等待 500ms，让页面有时间注册方法
    await new Promise(resolve => setTimeout(resolve, 500));

    // 重试 iframe 方法
    return await this.tryIframeRefresh(pageKey, operation);
  }

  /**
   * 首字母大写
   */
  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// 导出单例实例
export const pageRefreshUtil = PageRefreshUtil.getInstance();

/**
 * 页面标识常量
 */
export const PAGE_KEYS = {
  INTERFACE_GROUP: 'interfaceGroup',
  INTERFACE_TAG: 'interfaceTag', 
  INTERFACE_CONFIG: 'interfaceConfig',
  DATA_SOURCE: 'dataSource',
} as const;

/**
 * 快捷刷新方法
 */
export const PageRefresh = {
  /** 接口分组刷新 */
  interfaceGroup: {
    /** 新增后刷新（跳转第1页） */
    afterAdd: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_GROUP,
      successMessage: '接口分组已新增',
      operation: 'add'
    }),
    /** 修改后刷新（保持当前页） */
    afterEdit: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_GROUP,
      successMessage: '接口分组已修改',
      operation: 'edit'
    }),
    /** 删除后刷新（保持当前页） */
    afterDelete: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_GROUP,
      successMessage: '接口分组已删除',
      operation: 'delete'
    }),
  },

  /** 接口标签刷新 */
  interfaceTag: {
    afterAdd: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_TAG,
      successMessage: '接口标签已新增',
      operation: 'add'
    }),
    afterEdit: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_TAG,
      successMessage: '接口标签已修改',
      operation: 'edit'
    }),
    afterDelete: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_TAG,
      successMessage: '接口标签已删除',
      operation: 'delete'
    }),
  },

  /** 接口配置刷新 */
  interfaceConfig: {
    afterAdd: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_CONFIG,
      successMessage: '接口配置已新增',
      operation: 'add'
    }),
    afterEdit: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_CONFIG,
      successMessage: '接口配置已修改',
      operation: 'edit'
    }),
    afterDelete: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.INTERFACE_CONFIG,
      successMessage: '接口配置已删除',
      operation: 'delete'
    }),
  },

  /** 数据源刷新 */
  dataSource: {
    afterAdd: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.DATA_SOURCE,
      successMessage: '数据源已新增',
      operation: 'add'
    }),
    afterEdit: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.DATA_SOURCE,
      successMessage: '数据源已修改',
      operation: 'edit'
    }),
    afterDelete: () => pageRefreshUtil.triggerRefresh({
      pageKey: PAGE_KEYS.DATA_SOURCE,
      successMessage: '数据源已删除',
      operation: 'delete'
    }),
  },
};
