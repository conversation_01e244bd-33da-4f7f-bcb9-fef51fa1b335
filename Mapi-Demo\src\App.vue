<template>
  <el-container class="main-container">
    <el-header class="main-header">
      <HeaderComponent />
    </el-header>
    
    <el-main class="main-content">
      <router-view />
    </el-main>
    
    <el-footer class="main-footer">
      <FooterComponent />
    </el-footer>
  </el-container>
</template>

<script setup lang="ts">
import HeaderComponent from './components/HeaderComponent.vue'
import FooterComponent from './components/FooterComponent.vue'
</script>

<style>
.main-container {
  min-height: 100vh;
}

.main-header {
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  z-index: 1000;
}

.main-content {
  padding: 20px;
  background-color: #f5f7fa;
  flex: 1;
}

.main-footer {
  padding: 0;
  border-top: 1px solid #e5e6eb;
}
</style>