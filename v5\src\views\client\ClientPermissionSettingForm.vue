<template>
  <div class="drawer-form-container">
    <div class="drawer-form-content custom-scrollbar">
      <!-- 工具栏 -->
      <div class="permission-toolbar">
        <div class="toolbar-left">
          <h3 class="section-title">接口权限配置</h3>
          <el-tag type="primary" class="selected-count">
            已选择: {{ selectedCount }} 个接口
          </el-tag>
        </div>
        <div class="toolbar-right">
          <el-button-group>
            <el-button size="small" @click="expandAll">
              <el-icon><Plus /></el-icon>
              全部展开
            </el-button>
            <el-button size="small" @click="collapseAll">
              <el-icon><Minus /></el-icon>
              全部收起
            </el-button>
          </el-button-group>
          <el-button-group>
            <el-button type="primary" size="small" @click="selectAll">
              <el-icon><Check /></el-icon>
              全选
            </el-button>
            <el-button size="small" @click="clearAll">
              <el-icon><Close /></el-icon>
              清空
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 搜索筛选栏 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索接口名称、路径或描述"
            clearable
            @input="debouncedSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-box">
          <el-select
            v-model="filterMethod"
            placeholder="HTTP方法"
            clearable
            @change="handleFilter"
            class="filter-select"
          >
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
            <el-option label="PATCH" value="PATCH" />
          </el-select>
          <el-select
            v-model="filterGroup"
            placeholder="接口分组"
            clearable
            @change="handleFilter"
            class="filter-select"
          >
            <el-option
              v-for="group in interfaceGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </div>
      </div>

      <!-- 权限表格 -->
      <div class="permission-table">
        <el-table
          ref="tableRef"
          :data="filteredInterfaces"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          @select="handleSelect"
          @select-all="handleSelectAll"
          style="width: 100%"
          :default-expand-all="expandedAll"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55">
            <template #default="{ row }">
              <div :style="{ paddingLeft: row.type === 'interface' ? '30px' : '10px' }">
                <el-checkbox
                  :model-value="selectedPermissions.includes(row.id)"
                  @change="(checked) => handleCheckboxChange(row, checked)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="分组/接口名称" min-width="200">
            <template #default="{ row }">
              <div class="name-cell" :class="{ 'is-group': row.type === 'group' }">
                <div class="name-content">
                  <span class="name-text">{{ row.name }}</span>
                  <div v-if="row.type === 'interface' && row.path" class="interface-path-text">
                    {{ row.path }}
                  </div>
                </div>
                <el-tag v-if="row.type === 'group'" size="small" type="info" class="group-count-tag">
                  {{ row.children ? row.children.length : 0 }} 个接口
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="描述" min-width="200">
            <template #default="{ row }">
              <span class="description-text">{{ row.description || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="HTTP方法" width="100">
            <template #default="{ row }">
              <el-tag 
                v-if="row.method" 
                :type="getMethodTagType(row.method)" 
                size="small"
              >
                {{ row.method }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus, Minus, Check, Close } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'
import clientService from '@/services/client.service'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const tableRef = ref()
const loading = ref(false)
const saving = ref(false)
const clientData = ref({})
const allInterfaces = ref([])
const selectedPermissions = ref([])
const searchKeyword = ref('')
const actualSearchKeyword = ref('') // 实际用于过滤的关键词
const filterMethod = ref('')
const filterGroup = ref('')
const expandedAll = ref(false)

// 计算属性
const interfaceGroups = computed(() => {
  return allInterfaces.value.filter(item => item.type === 'group')
})

const filteredInterfaces = computed(() => {
  let result = [...allInterfaces.value]

  // 搜索过滤 - 使用防抖后的关键词
  if (actualSearchKeyword.value) {
    const keyword = actualSearchKeyword.value.toLowerCase()
    result = result.filter(item => {
      if (item.type === 'group') {
        return item.name.toLowerCase().includes(keyword) ||
               (item.children && item.children.some(child => 
                 child.name.toLowerCase().includes(keyword) ||
                 child.path?.toLowerCase().includes(keyword) ||
                 child.description?.toLowerCase().includes(keyword)
               ))
      } else {
        return item.name.toLowerCase().includes(keyword) ||
               item.path?.toLowerCase().includes(keyword) ||
               item.description?.toLowerCase().includes(keyword)
      }
    })
  }
  
  // HTTP方法过滤
  if (filterMethod.value) {
    result = result.map(item => {
      if (item.type === 'group') {
        const filteredChildren = item.children?.filter(child => child.method === filterMethod.value) || []
        return filteredChildren.length > 0 ? { ...item, children: filteredChildren } : null
      } else {
        return item.method === filterMethod.value ? item : null
      }
    }).filter(Boolean)
  }
  
  // 分组过滤
  if (filterGroup.value) {
    result = result.filter(item => item.id === filterGroup.value)
  }
  
  return result
})

const selectedCount = computed(() => {
  return selectedPermissions.value.length
})

// 更新表格选中状态
const updateTableSelection = () => {
  if (!tableRef.value) return

  // 清空当前选择
  tableRef.value.clearSelection()

  // 根据权限数据设置选中状态
  const setSelection = (items) => {
    items.forEach(item => {
      if (item.type === 'interface') {
        // 只对接口进行选择操作
        const isSelected = selectedPermissions.value.includes(item.id)
        if (isSelected) {
          tableRef.value.toggleRowSelection(item, true)
        }
      } else if (item.type === 'group' && item.children) {
        // 对于分组，检查其所有子接口是否都被选中
        const allChildrenSelected = item.children.every(child =>
          child.type === 'interface' && selectedPermissions.value.includes(child.id)
        )
        if (allChildrenSelected && item.children.some(child => child.type === 'interface')) {
          tableRef.value.toggleRowSelection(item, true)
        }
      }

      if (item.children) {
        setSelection(item.children)
      }
    })
  }

  setSelection(allInterfaces.value)
}

// 加载权限数据
const loadPermissionData = async () => {
  try {
    loading.value = true
    
    // 加载所有接口数据
    const interfaceTreeData = await clientService.getInterfaceTree()
    allInterfaces.value = interfaceTreeData
    
    // 加载客户端已有权限
    // TODO: 根据客户端ID获取已有权限
    // 根据列表页的permissionGroups来设置已选权限
    const clientPermissionGroups = clientData.value?.permissionGroups || []
    const selectedIds = []

    // 如果客户端有"用户管理"分组权限，选中相关接口
    if (clientPermissionGroups.includes('用户管理')) {
      selectedIds.push('interface_1', 'interface_2')
    }

    // 如果客户端有"数据源管理"分组权限，选中相关接口
    if (clientPermissionGroups.includes('数据源管理')) {
      selectedIds.push('interface_4', 'interface_5')
    }

    // 如果客户端有"接口管理"分组权限，选中相关接口
    if (clientPermissionGroups.includes('接口管理')) {
      selectedIds.push('interface_6', 'interface_7')
    }

    selectedPermissions.value = selectedIds
    
    // 设置表格选中状态
    nextTick(() => {
      updateTableSelection()
    })
  } catch (error) {
    ElMessage.error('加载权限数据失败')
  } finally {
    loading.value = false
  }
}

// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.clientData) {
    clientData.value = newProps.clientData
    loadPermissionData()
  }
}, { immediate: true, deep: true })

// 防抖搜索
let searchTimer = null
const debouncedSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    actualSearchKeyword.value = searchKeyword.value
    // 搜索时自动更新表格选中状态
    nextTick(() => {
      updateTableSelection()
    })
  }, 300) // 300ms防抖延迟
}

// 搜索过滤
const handleSearch = () => {
  // 搜索时自动更新表格选中状态
  nextTick(() => {
    updateTableSelection()
  })
}

const handleFilter = () => {
  // 过滤时自动更新表格选中状态
  nextTick(() => {
    updateTableSelection()
  })
}

// 表格操作
const handleSelect = (selection, row) => {
  if (row.type === 'interface') {
    const index = selectedPermissions.value.indexOf(row.id)
    if (index > -1) {
      selectedPermissions.value.splice(index, 1)
    } else {
      selectedPermissions.value.push(row.id)
    }
  }
}

const handleSelectAll = (selection) => {
  // 处理全选/取消全选
  const currentPageInterfaces = []
  const collectInterfaces = (items) => {
    items.forEach(item => {
      if (item.type === 'interface') {
        currentPageInterfaces.push(item.id)
      }
      if (item.children) {
        collectInterfaces(item.children)
      }
    })
  }
  
  collectInterfaces(filteredInterfaces.value)
  
  if (selection.length === 0) {
    // 取消全选
    currentPageInterfaces.forEach(id => {
      const index = selectedPermissions.value.indexOf(id)
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    })
  } else {
    // 全选
    currentPageInterfaces.forEach(id => {
      if (!selectedPermissions.value.includes(id)) {
        selectedPermissions.value.push(id)
      }
    })
  }
}

// 批量操作
const expandAll = () => {
  expandedAll.value = true
  if (tableRef.value) {
    // 展开所有分组
    allInterfaces.value.forEach(item => {
      if (item.type === 'group' && item.children && item.children.length > 0) {
        tableRef.value.toggleRowExpansion(item, true)
      }
    })
  }
}

const collapseAll = () => {
  expandedAll.value = false
  if (tableRef.value) {
    // 收起所有分组
    allInterfaces.value.forEach(item => {
      if (item.type === 'group' && item.children && item.children.length > 0) {
        tableRef.value.toggleRowExpansion(item, false)
      }
    })
  }
}

const selectAll = () => {
  console.log('selectAll clicked')
  const allInterfaceIds = []
  const collectInterfaceIds = (items) => {
    items.forEach(item => {
      // 只收集接口ID，不收集分组ID
      if (item.type === 'interface') {
        allInterfaceIds.push(item.id)
      }
      if (item.children) {
        collectInterfaceIds(item.children)
      }
    })
  }

  collectInterfaceIds(allInterfaces.value)
  selectedPermissions.value = [...allInterfaceIds]
  console.log('Selected all interfaces:', allInterfaceIds)

  nextTick(() => {
    updateTableSelection()
  })
}

const clearAll = () => {
  console.log('clearAll clicked')
  selectedPermissions.value = []
  console.log('Cleared all selections')

  nextTick(() => {
    updateTableSelection()
  })
}

// 行样式类名
const getRowClassName = ({ row }) => {
  return row.type === 'interface' ? 'interface-row' : 'group-row'
}

// 处理复选框变化
const handleCheckboxChange = (row, checked) => {
  if (row.type === 'interface') {
    if (checked) {
      if (!selectedPermissions.value.includes(row.id)) {
        selectedPermissions.value.push(row.id)
      }
    } else {
      const index = selectedPermissions.value.indexOf(row.id)
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    }
  }
}

// HTTP方法标签类型
const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return typeMap[method] || 'default'
}



// 保存权限
const handleSubmit = async () => {
  try {
    saving.value = true
    
    // TODO: 调用API保存权限
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('权限设置成功')
    drawerMessenger.hideDrawer()
  } catch (error) {
    ElMessage.error('权限设置失败')
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer()
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '取消',
      handler: handleCancel
    },
    {
      text: '保存权限',
      type: 'primary',
      handler: handleSubmit,
      loading: saving.value,
      loadingText: '保存中...'
    }
  ]
}

// 监听saving状态，更新按钮状态
watch(saving, () => {
  updateDrawerButtons()
})

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.permission-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 工具栏样式 */
.permission-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin-left: -20px;
  margin-right: -20px;
  margin-bottom: 0;
  min-height: 56px;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 100%;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      line-height: 1.2;
      display: flex;
      align-items: center;
    }

    .selected-count {
      font-size: 12px;
      font-weight: 600;
      background-color: #1890ff !important;
      border-color: #1890ff !important;
      color: #ffffff !important;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
      display: flex;
      align-items: center;
      height: 24px;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    height: 100%;

    .el-button-group {
      display: flex;
      align-items: center;
    }

    .el-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

/* 搜索筛选栏样式 */
.search-filter-bar {
  display: flex;
  gap: 16px;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
  margin: 0 -20px;
  
  .search-box {
    flex: 1;
    
    .search-input {
      width: 100%;
    }
  }
  
  .filter-box {
    display: flex;
    gap: 12px;
    
    .filter-select {
      width: 120px;
    }
  }
}

.permission-table {
  flex: 1;
  margin: 0 -20px;
  padding: 0 20px;
  overflow: hidden;
  
  .el-table {
    height: 100%;
  }
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .name-content {
    flex: 1;

    .name-text {
      font-weight: 500;
      color: #303133;
      display: block;
      line-height: 1.2;
    }

    .interface-path-text {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 11px;
      color: #1890ff;
      font-weight: 600;
      background-color: #f0f8ff;
      padding: 2px 6px;
      border-radius: 3px;
      margin-top: 3px;
      line-height: 1.2;
      display: inline-block;
    }
  }

  &.is-group {
    .name-content .name-text {
      font-weight: 600;
      color: #1890ff;
    }

    .group-count-tag {
      background-color: #f0f8ff !important;
      border-color: #d6e4ff !important;
      color: #1890ff !important;
      font-size: 11px;
    }
  }
}

/* 修复表格树形结构的箭头和文字布局 */
.permission-table {
  .el-table {
    .el-table__body {
      .el-table__row {
        /* 选择列（复选框列）的缩进处理 */
        .el-table__cell {
          &:nth-child(1) {
            /* 第一列（复选框列）的单元格 */
            .cell {
              display: flex !important;
              align-items: center !important;
              justify-content: flex-start !important;

              /* 复选框的缩进 */
              .el-checkbox {
                margin-left: var(--el-table-indent, 0) !important;
              }
            }
          }

          &:nth-child(2) {
            /* 第二列（名称列）的单元格 */
            .cell {
              display: flex !important;
              align-items: center !important;
              gap: 4px !important;

              /* 展开箭头 */
              .el-table__expand-icon {
                margin-right: 4px !important;
                margin-left: 0 !important;
                display: inline-flex !important;
                align-items: center !important;
                vertical-align: middle !important;
              }

              /* 树形缩进 */
              .el-table__indent {
                display: inline-block !important;
                vertical-align: middle !important;
              }

              /* 名称内容 */
              .name-cell {
                display: inline-flex !important;
                align-items: center !important;
                vertical-align: middle !important;
                flex: 1 !important;
              }
            }
          }
        }


    }
  }
}



.description-text {
  color: #606266;
  font-size: 13px;
}
}
</style>
