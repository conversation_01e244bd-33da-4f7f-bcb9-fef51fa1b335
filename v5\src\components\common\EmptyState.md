# EmptyState 空状态组件

通用的空状态组件，用于显示无数据、无搜索结果等场景。

## 基本用法

```vue
<template>
  <EmptyState />
</template>
```

## 不同类型

### 表格空状态
```vue
<template>
  <EmptyState 
    type="table"
    title="暂无数据"
    description="当前没有数据记录，您可以添加新的数据"
    action-text="添加数据"
    @action="handleAdd"
  />
</template>
```

### 搜索空状态
```vue
<template>
  <EmptyState 
    type="search"
    title="无搜索结果"
    description="没有找到符合条件的结果，请尝试其他搜索条件"
    action-text="清除搜索"
    @action="clearSearch"
  />
</template>
```

### 文件夹空状态
```vue
<template>
  <EmptyState 
    type="folder"
    title="暂无文件"
    description="当前文件夹为空，您可以上传文件或创建新文件夹"
    action-text="上传文件"
    @action="uploadFile"
  />
</template>
```

## 在Element Plus表格中使用

```vue
<template>
  <el-table :data="tableData">
    <el-table-column prop="name" label="名称" />
    
    <!-- 空状态插槽 -->
    <template #empty>
      <EmptyState 
        type="table"
        title="暂无数据"
        description="当前没有数据记录"
        action-text="添加数据"
        @action="handleAdd"
      />
    </template>
  </el-table>
</template>
```

## 自定义操作按钮

```vue
<template>
  <EmptyState 
    type="table"
    title="暂无数据"
    description="当前没有数据记录"
    :show-action="false"
  >
    <template #actions>
      <el-button type="primary" @click="handleAdd">添加数据</el-button>
      <el-button @click="handleImport">导入数据</el-button>
    </template>
  </EmptyState>
</template>
```

## Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| type | 空状态类型 | string | default/folder/table/search | default |
| title | 标题 | string | — | 根据type自动生成 |
| description | 描述文本 | string | — | 根据type自动生成 |
| actionText | 操作按钮文本 | string | — | — |
| actionIcon | 操作按钮图标 | Component | — | — |
| showAction | 是否显示操作区域 | boolean | — | true |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| action | 点击操作按钮时触发 | — |

## Slots

| 插槽名 | 说明 |
|--------|------|
| actions | 自定义操作区域内容 |

## 样式定制

组件支持CSS变量定制：

```scss
.empty-state {
  --empty-color-primary: #666;
  --empty-color-secondary: #999;
  --empty-color-border: #d9d9d9;
  --empty-color-bg: #f0f2f5;
  --empty-color-bg-light: #fafafa;
  --empty-color-icon: #e6e6e6;
}
```

## 响应式设计

组件内置响应式断点：
- 768px以下：小尺寸显示
- 480px以下：超小尺寸显示

## 使用建议

1. **表格空状态**：用于数据表格无数据时
2. **搜索空状态**：用于搜索无结果时
3. **文件夹空状态**：用于文件列表为空时
4. **默认空状态**：用于其他通用场景

## 最佳实践

1. 根据具体场景选择合适的type
2. 提供有意义的描述文本
3. 为用户提供明确的下一步操作
4. 在搜索场景下提供清除搜索的选项
