"""
接口分组路由层
定义API路由和端点
"""

from fastapi import APIRouter, Depends, Query, Path
from typing import Optional
from app.config.interface.controllers.interface_group_controller import InterfaceGroupController
from app.config.interface.schemas.interface_group_schema import (
    InterfaceGroupCreate,
    InterfaceGroupUpdate,
    InterfaceGroupResponse,
    InterfaceGroupListResponse
)

# 创建路由器
router = APIRouter()

@router.get("/", response_model=InterfaceGroupListResponse, summary="获取接口分组列表")
async def get_interface_groups(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    controller: InterfaceGroupController = Depends()
):
    """
    获取接口分组列表（分页）

    支持的查询参数：
    - **page**: 页码，从1开始
    - **size**: 每页大小，1-100之间
    - **search**: 搜索关键词，支持名称、路径前缀、描述模糊搜索

    返回分页的接口分组列表，包含总数、页数等信息
    """
    return await controller.get_interface_groups(page, size, search)


@router.post("/", response_model=InterfaceGroupResponse, summary="创建接口分组")
async def create_interface_group(
    group_data: InterfaceGroupCreate,
    controller: InterfaceGroupController = Depends()
):
    """
    创建新的接口分组

    请求体参数：
    - **name**: 分组名称，必填，1-100字符
    - **path_prefix**: 路径前缀，必填，1-50字符，用于接口路径的统一前缀
    - **description**: 分组描述，可选，最多500字符
    - **is_enabled**: 是否启用，默认true
    - **created_by**: 创建人，可选

    返回创建的接口分组信息
    """
    return await controller.create_interface_group(group_data)


@router.get("/{group_id}", response_model=InterfaceGroupResponse, summary="获取单个接口分组")
async def get_interface_group(
    group_id: int = Path(..., description="接口分组ID"),
    controller: InterfaceGroupController = Depends()
):
    """
    根据ID获取单个接口分组详情

    路径参数：
    - **group_id**: 接口分组ID

    返回接口分组详细信息，包含关联的接口数量
    """
    return await controller.get_interface_group(group_id)


@router.put("/{group_id}", response_model=InterfaceGroupResponse, summary="更新接口分组")
async def update_interface_group(
    group_data: InterfaceGroupUpdate,
    group_id: int = Path(..., description="接口分组ID"),
    controller: InterfaceGroupController = Depends()
):
    """
    更新接口分组信息

    路径参数：
    - **group_id**: 要更新的接口分组ID

    请求体参数（所有参数都是可选的）：
    - **name**: 分组名称，1-100字符
    - **path_prefix**: 路径前缀，1-50字符
    - **description**: 分组描述，最多500字符
    - **is_enabled**: 是否启用

    返回更新后的接口分组信息
    """
    return await controller.update_interface_group(group_id, group_data)


@router.delete("/{group_id}", summary="删除接口分组")
async def delete_interface_group(
    group_id: int = Path(..., description="接口分组ID"),
    controller: InterfaceGroupController = Depends()
):
    """
    删除接口分组

    路径参数：
    - **group_id**: 要删除的接口分组ID

    注意：删除前会检查是否有关联的接口配置，如有关联则不允许删除
    """
    return await controller.delete_interface_group(group_id)
