/**
 * 接口测试相关类型定义
 * 用于：InterfaceTest.vue 接口测试页面
 */

// 接口测试记录
export interface InterfaceTestRecord {
  id: number;
  interface_id: number;            // 接口ID
  interface_name?: string;         // 接口名称（用于显示）
  interface_path?: string;         // 接口路径（用于显示）
  interface_method?: string;       // 接口方法（用于显示）
  test_name?: string;              // 测试名称
  test_params: Record<string, any>; // 测试参数
  test_headers: Record<string, string>; // 测试请求头
  response_status: number;         // 响应状态码
  response_time: number;           // 响应时间（毫秒）
  response_data: any;              // 响应数据
  response_headers: Record<string, string>; // 响应头
  success: boolean;                // 是否成功
  error_message?: string;          // 错误信息
  test_time: string;               // 测试时间
  tester?: string;                 // 测试人
}

// 接口测试请求
export interface InterfaceTestRequest {
  interface_id: number;
  test_name?: string;
  test_params?: Record<string, any>;
  test_headers?: Record<string, string>;
}

// 接口测试响应
export interface InterfaceTestResponse {
  success: boolean;
  status_code: number;
  response_time: number;
  response_data: any;
  response_headers: Record<string, string>;
  error_message?: string;
  test_time: string;
}

// 接口测试记录查询参数
export interface InterfaceTestQuery {
  page?: number;
  page_size?: number;
  interface_id?: number;           // 接口筛选
  search?: string;                 // 搜索关键词（测试名称或接口名称）
  success?: boolean;               // 成功状态筛选
  start_date?: string;             // 开始日期
  end_date?: string;               // 结束日期
  tester?: string;                 // 测试人筛选
}

// 接口测试记录列表响应
export interface InterfaceTestListResponse {
  data: InterfaceTestRecord[];
  total: number;
  page: number;
  page_size: number;
}

// 测试参数配置
export interface TestParamConfig {
  name: string;                    // 参数名
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'; // 参数类型
  value: any;                      // 参数值
  description?: string;            // 参数描述
  required: boolean;               // 是否必填
}

// 测试用例
export interface TestCase {
  id: number;
  interface_id: number;
  name: string;                    // 用例名称
  description?: string;            // 用例描述
  params: TestParamConfig[];       // 测试参数
  headers: Record<string, string>; // 请求头
  expected_status?: number;        // 期望状态码
  expected_fields?: string[];      // 期望响应字段
  is_active: boolean;              // 是否启用
  created_at: string;
  updated_at: string;
  created_by?: string;
}

// 测试用例请求
export interface TestCaseRequest {
  interface_id: number;
  name: string;
  description?: string;
  params: TestParamConfig[];
  headers: Record<string, string>;
  expected_status?: number;
  expected_fields?: string[];
  is_active?: boolean;
}

// 批量测试请求
export interface BatchTestRequest {
  interface_ids: number[];
  test_case_ids?: number[];        // 指定测试用例，为空则使用默认参数
}

// 批量测试响应
export interface BatchTestResponse {
  total: number;
  success_count: number;
  failed_count: number;
  results: InterfaceTestResponse[];
}

// 测试统计信息
export interface TestStatistics {
  total_tests: number;             // 总测试次数
  success_tests: number;          // 成功次数
  failed_tests: number;           // 失败次数
  success_rate: number;           // 成功率
  avg_response_time: number;      // 平均响应时间
  last_test_time?: string;        // 最后测试时间
  today_tests: number;            // 今日测试次数
  this_week_tests: number;        // 本周测试次数

  // 新增统计信息
  total_interfaces: number;       // 总接口数量
  total_groups: number;           // 总分组数量
  tested_interfaces: number;      // 已测试接口数
  untested_interfaces: number;    // 未测试接口数
  fastest_response_time: number;  // 最快响应时间
  slowest_response_time: number;  // 最慢响应时间
  most_tested_interface?: string; // 测试最多的接口
  recent_test_trend: 'up' | 'down' | 'stable'; // 最近测试趋势
}

// 接口状态统计
export interface InterfaceStatusStats {
  total_interfaces: number;       // 总接口数
  enabled_interfaces: number;     // 启用接口数
  tested_interfaces: number;      // 已测试接口数
  success_interfaces: number;     // 测试成功接口数
  failed_interfaces: number;      // 测试失败接口数
  untested_interfaces: number;    // 未测试接口数
}
