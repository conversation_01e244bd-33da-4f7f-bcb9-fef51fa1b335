import { defineConfig } from 'vite';
import path from 'path';
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  css: {
    preprocessorOptions: {
      scss: {
        // api: 'modern-compiler' // 移除不支持的配置
      }
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173, // 可自定义端口号
    strictPort: false, // 端口被占用时自动选择其他端口
    proxy: {
      '/api': {
        target: 'http://**************:8000',
        changeOrigin: true,
        secure: false
      }
    }
  },
  resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
       }
    }
})