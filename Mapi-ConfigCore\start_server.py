#!/usr/bin/env python3
"""
启动FastAPI开发服务器
"""

import uvicorn
from app.main import app

if __name__ == "__main__":
    print("🚀 启动Mapi-ConfigCore开发服务器...")
    print("📍 服务地址: http://127.0.0.1:8000")
    print("📖 API文档: http://127.0.0.1:8000/docs")
    print("📚 ReDoc文档: http://127.0.0.1:8000/redoc")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 50)
    
    uvicorn.run(
        "app.main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
