---
type: "agent_requested"
description: "空组件使用规范和标准"
---

# 空组件使用规范

## 概述

所有数据列表页面必须添加空组件，为用户提供友好的空状态提示和操作引导。

## 实现标准

### 1. 基本结构

```vue
<el-table :data="tableData">
  <!-- 空状态 -->
  <template #empty>
    <el-empty
      description="暂无数据"
      :image-size="120"
    >
      <template #description>
        <p>描述性文字</p>
        <p>引导性提示</p>
      </template>
      <el-button type="primary" @click="handleAction">
        <el-icon><Plus /></el-icon>
        操作按钮
      </el-button>
    </el-empty>
  </template>
</el-table>
```

### 2. 必需元素

- **描述性文字**：说明当前状态（如"暂无数据"）
- **引导性提示**：告诉用户下一步操作
- **操作按钮**：提供快速操作入口
- **图标**：使用Element Plus图标增强视觉效果

### 3. 图标导入

确保导入所需图标：

```typescript
import { Plus, User, Refresh } from '@element-plus/icons-vue'
```

## 已实现页面

### 主要管理页面

1. **接口配置管理** (`InterfaceConfig.vue`)
   - 描述：暂无接口配置数据
   - 操作：新增接口

2. **客户端管理** (`ClientManagement.vue`)
   - 描述：暂无客户端数据
   - 操作：新增客户端

3. **权限管理** (`PermissionManagement.vue`)
   - 描述：暂无权限数据
   - 操作：前往客户端管理

4. **日志管理** (`LogManagement.vue`)
   - 描述：暂无日志数据
   - 操作：重置查询条件

5. **任务管理** (`TaskManagement.vue`)
   - 描述：暂无定时任务
   - 操作：创建任务

6. **操作日志** (`OperationLogs.vue`)
   - 描述：暂无系统日志
   - 操作：无（仅提示）

### 使用EmptyState组件的页面

1. **数据源列表** (`DataSourceList.vue`)
2. **接口分组** (`InterfaceGroup.vue`)
3. **接口标签** (`InterfaceTag.vue`)
4. **接口测试** (`InterfaceTest.vue`)

## 设计原则

### 1. 一致性
- 所有空组件使用统一的视觉风格
- 描述文字简洁明了
- 操作按钮位置和样式统一

### 2. 引导性
- 明确告诉用户当前状态
- 提供明确的下一步操作指引
- 操作按钮直接关联到相关功能

### 3. 友好性
- 避免使用技术术语
- 使用积极正面的语言
- 提供有用的操作建议

## 实施检查清单

- [ ] 是否使用了 `<template #empty>` 插槽
- [ ] 是否包含描述性文字
- [ ] 是否包含引导性提示
- [ ] 是否提供操作按钮（如适用）
- [ ] 是否导入了所需图标
- [ ] 文字是否简洁明了
- [ ] 操作是否与页面功能相关

## 注意事项

1. **不是所有页面都需要操作按钮**：如操作日志页面只需要提示信息
2. **操作按钮要有意义**：确保按钮操作与页面功能直接相关
3. **保持简洁**：避免过多的文字描述
4. **考虑用户场景**：根据不同页面的使用场景调整提示内容

## 更新记录

- **2024-12-28**：制定空组件使用规范
- **2024-12-28**：完成主要管理页面的空组件添加
