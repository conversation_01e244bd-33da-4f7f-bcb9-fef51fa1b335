#!/usr/bin/env python3
"""
调试接口配置的关联信息
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def debug_interface_config():
    """调试接口配置"""
    print("🔍 调试接口配置关联信息...")
    
    try:
        # 获取接口配置列表
        response = requests.get(f"{BASE_URL}/interface/configs/")
        if response.status_code == 200:
            data = response.json()
            print(f"📊 接口配置总数: {data['total']}")
            
            for config in data['items']:
                print(f"\n📋 接口配置: {config['name']} (ID={config['id']})")
                print(f"   路径: {config['path']}")
                print(f"   方法: {config['method']}")
                print(f"   分组ID: {config.get('group_id', 'N/A')}")
                print(f"   分组名称: {config.get('group_name', 'N/A')}")
                print(f"   数据源ID: {config.get('datasource_id', 'N/A')}")
                print(f"   数据源名称: {config.get('datasource_name', 'N/A')}")
                print(f"   标签IDs: {config.get('tags', [])}")
                print(f"   标签名称: {config.get('tag_names', [])}")
                print(f"   查询字段: {config.get('query_fields', [])}")
                print(f"   必填字段: {config.get('required_fields', [])}")
                print(f"   响应字段: {config.get('response_fields', [])}")

                # 获取单个配置详情
                response2 = requests.get(f"{BASE_URL}/interface/configs/{config['id']}")
                if response2.status_code == 200:
                    detail = response2.json()
                    print(f"   详情-分组名称: {detail.get('group_name', 'N/A')}")
                    print(f"   详情-数据源名称: {detail.get('datasource_name', 'N/A')}")
                    print(f"   详情-标签名称: {detail.get('tag_names', [])}")
        else:
            print(f"❌ 获取接口配置失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")

if __name__ == "__main__":
    debug_interface_config()
