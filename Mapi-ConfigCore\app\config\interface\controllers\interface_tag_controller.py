"""
接口标签控制器层
处理HTTP请求和响应
"""

from fastapi import Depends
from sqlalchemy.orm import Session
from typing import Optional
from app.config.interface.services.interface_tag_service import InterfaceTagService
from app.config.interface.schemas.interface_tag_schema import (
    InterfaceTagCreate,
    InterfaceTagUpdate,
    InterfaceTagResponse,
    InterfaceTagListResponse
)
from app.shared.database import get_database
from app.shared.core.log_util import LogUtil


class InterfaceTagController:
    """接口标签控制器类"""
    
    def __init__(self, db: Session = Depends(get_database)):
        self.db = db
        self.service = InterfaceTagService(db)
        LogUtil.debug("接口标签控制器初始化", controller="InterfaceTagController")
    
    async def get_interface_tags(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None
    ) -> InterfaceTagListResponse:
        """
        获取接口标签列表
        
        Args:
            page: 页码，默认1
            size: 每页大小，默认10
            search: 搜索关键词，可选
            
        Returns:
            接口标签列表响应
        """
        LogUtil.debug("处理获取接口标签列表请求", 
                     controller="InterfaceTagController",
                     action="get_interface_tags",
                     page=page, size=size, search=search)
        
        return self.service.get_interface_tags(page, size, search)
    
    async def get_interface_tag(self, tag_id: int) -> InterfaceTagResponse:
        """
        获取单个接口标签
        
        Args:
            tag_id: 接口标签ID
            
        Returns:
            接口标签响应
        """
        LogUtil.debug("处理获取接口标签详情请求", 
                     controller="InterfaceTagController",
                     action="get_interface_tag",
                     tag_id=tag_id)
        
        return self.service.get_interface_tag(tag_id)
    
    async def create_interface_tag(self, tag_data: InterfaceTagCreate) -> InterfaceTagResponse:
        """
        创建接口标签
        
        Args:
            tag_data: 接口标签创建数据
            
        Returns:
            创建的接口标签响应
        """
        LogUtil.debug("处理创建接口标签请求", 
                     controller="InterfaceTagController",
                     action="create_interface_tag",
                     name=tag_data.name,
                     color=tag_data.color)
        
        return self.service.create_interface_tag(tag_data)
    
    async def update_interface_tag(
        self, 
        tag_id: int, 
        tag_data: InterfaceTagUpdate
    ) -> InterfaceTagResponse:
        """
        更新接口标签
        
        Args:
            tag_id: 接口标签ID
            tag_data: 更新数据
            
        Returns:
            更新后的接口标签响应
        """
        LogUtil.debug("处理更新接口标签请求", 
                     controller="InterfaceTagController",
                     action="update_interface_tag",
                     tag_id=tag_id)
        
        return self.service.update_interface_tag(tag_id, tag_data)
    
    async def delete_interface_tag(self, tag_id: int) -> dict:
        """
        删除接口标签
        
        Args:
            tag_id: 接口标签ID
            
        Returns:
            删除结果
        """
        LogUtil.debug("处理删除接口标签请求", 
                     controller="InterfaceTagController",
                     action="delete_interface_tag",
                     tag_id=tag_id)
        
        return self.service.delete_interface_tag(tag_id)
    
    async def get_all_enabled_tags(self) -> list[InterfaceTagResponse]:
        """
        获取所有启用的接口标签（用于下拉选择）
        
        Returns:
            启用的接口标签列表
        """
        LogUtil.debug("处理获取所有启用标签请求", 
                     controller="InterfaceTagController",
                     action="get_all_enabled_tags")
        
        return self.service.get_all_enabled_tags()
