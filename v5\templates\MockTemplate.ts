// Mock数据标准模板
// 使用说明：
// 1. 复制此模板创建新mock文件
// 2. 替换所有 {ModuleName} 为实际模块名
// 3. 替换所有 {moduleName} 为实际模块名（小写）
// 4. 根据实际需求调整数据结构和字段

import type { {ModuleName} } from '../types/{module-name}';

/**
 * {ModuleName}Mock数据
 */
export const mock{ModuleName}s: {ModuleName}[] = [
  {
    id: 1,
    name: '示例{ModuleName}1',
    description: '这是第一个示例{ModuleName}的描述信息',
    status: 'enabled',
    createdAt: '2024-12-20 10:00:00',
    updatedAt: '2024-12-27 15:30:00'
  },
  {
    id: 2,
    name: '示例{ModuleName}2',
    description: '这是第二个示例{ModuleName}的描述信息',
    status: 'enabled',
    createdAt: '2024-12-25 15:30:00',
    updatedAt: '2024-12-26 09:15:00'
  },
  {
    id: 3,
    name: '示例{ModuleName}3',
    description: '这是第三个示例{ModuleName}的描述信息',
    status: 'disabled',
    createdAt: '2024-12-15 08:20:00',
    updatedAt: '2024-12-20 14:45:00'
  },
  {
    id: 4,
    name: '示例{ModuleName}4',
    description: '这是第四个示例{ModuleName}的描述信息',
    status: 'enabled',
    createdAt: '2024-12-10 16:45:00',
    updatedAt: '2024-12-22 11:20:00'
  },
  {
    id: 5,
    name: '示例{ModuleName}5',
    description: '这是第五个示例{ModuleName}的描述信息',
    status: 'enabled',
    createdAt: '2024-12-01 12:00:00',
    updatedAt: '2024-12-15 10:30:00'
  }
];
