import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { MenuData, PageConfig } from '@/types/navigation';

// 侧边栏菜单状态管理Store
export const useSidebarMenuStore = defineStore('sidebarMenu', () => {
  // 侧边栏折叠状态
  const isCollapse = ref(false);
  // 控制MAPI文字显示状态
  const showText = ref(true);
  // 当前选中菜单项
  const currentMenu = ref<string>('');

  // 菜单数据
  const menuData = ref<MenuData | null>(null);

  // 切换侧边栏折叠状态
  const toggleCollapse = () => {
    if (isCollapse.value) {
      // 展开：先展开侧边栏，延迟显示文字
      isCollapse.value = false;
      setTimeout(() => {
        showText.value = true;
      }, 150); // 150ms延迟，更快显示文字
    } else {
      // 折叠：立即隐藏文字，然后折叠
      showText.value = false;
      isCollapse.value = true;
    }
  };

  // 设置侧边栏当前选中菜单项
  const setSelectedMenu = (menuKey: string) => {
    currentMenu.value = menuKey;
  };

  // 处理菜单点击事件
  const handleMenuSelect = (menuKey: string): PageConfig | null => {
    // 设置当前选中菜单
    setSelectedMenu(menuKey);
      // 查找并返回页面信息
    return findPageConfigByMenuKey(menuKey);
  };

  // 根据菜单Key查找页面配置
  const findPageConfigByMenuKey = (menuKey: string): PageConfig | null => {
    if (!menuData.value || !menuData.value.pageConfigs) return null;

    return menuData.value.pageConfigs.find(config => config.name === menuKey) || null;
  };

  // 设置菜单数据
  const setMenuData = (data: MenuData | null) => {
    menuData.value = data;
    // 自动选择第一个二级菜单作为默认选中项
    if (data && data.topLevelMenus && data.topLevelMenus.length > 0) {
      const firstTopMenu = data.topLevelMenus[0];
      if (firstTopMenu.children && firstTopMenu.children.length > 0) {
        handleMenuSelect(firstTopMenu.children[0].name);
      }
    }
  };

  return {
    isCollapse,
    showText,
    currentMenu,
    menuData,
    toggleCollapse,
    setSelectedMenu,
    setMenuData,
    handleMenuSelect
  };
});