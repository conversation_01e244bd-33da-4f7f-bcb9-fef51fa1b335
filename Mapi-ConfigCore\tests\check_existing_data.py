#!/usr/bin/env python3
"""
检查现有数据并创建接口配置
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def get_existing_data():
    """获取现有数据"""
    print("🔍 检查现有数据...")
    
    # 获取数据源
    try:
        response = requests.get(f"{BASE_URL}/datasource/")
        if response.status_code == 200:
            datasources = response.json()['items']
            print(f"📊 现有数据源: {len(datasources)} 个")
            for ds in datasources:
                print(f"   - ID={ds['id']}, 名称={ds['name']}")
        else:
            print(f"❌ 获取数据源失败: {response.status_code}")
            datasources = []
    except Exception as e:
        print(f"❌ 获取数据源异常: {e}")
        datasources = []
    
    # 获取接口分组
    try:
        response = requests.get(f"{BASE_URL}/interface/groups/")
        if response.status_code == 200:
            groups = response.json()['items']
            print(f"📁 现有接口分组: {len(groups)} 个")
            for group in groups:
                print(f"   - ID={group['id']}, 名称={group['name']}")
        else:
            print(f"❌ 获取接口分组失败: {response.status_code}")
            groups = []
    except Exception as e:
        print(f"❌ 获取接口分组异常: {e}")
        groups = []
    
    # 获取接口标签
    try:
        response = requests.get(f"{BASE_URL}/interface/tags/")
        if response.status_code == 200:
            tags = response.json()['items']
            print(f"🏷️ 现有接口标签: {len(tags)} 个")
            for tag in tags:
                print(f"   - ID={tag['id']}, 名称={tag['name']}")
        else:
            print(f"❌ 获取接口标签失败: {response.status_code}")
            tags = []
    except Exception as e:
        print(f"❌ 获取接口标签异常: {e}")
        tags = []
    
    # 获取接口配置
    try:
        response = requests.get(f"{BASE_URL}/interface/configs/")
        if response.status_code == 200:
            configs = response.json()['items']
            print(f"⚙️ 现有接口配置: {len(configs)} 个")
            for config in configs:
                print(f"   - ID={config['id']}, 名称={config['name']}")
        else:
            print(f"❌ 获取接口配置失败: {response.status_code}")
            configs = []
    except Exception as e:
        print(f"❌ 获取接口配置异常: {e}")
        configs = []
    
    return datasources, groups, tags, configs

def create_interface_configs_with_existing_data(datasources, groups, tags):
    """使用现有数据创建接口配置"""
    print("\n⚙️ 使用现有数据创建接口配置...")
    
    if not datasources or not groups or not tags:
        print("❌ 缺少必要的数据，无法创建接口配置")
        return []
    
    # 找到办公管理系统数据源
    datasource_id = None
    for ds in datasources:
        if "办公" in ds['name'] or ds['name'] == "办公管理系统":
            datasource_id = ds['id']
            break
    
    if not datasource_id:
        datasource_id = datasources[0]['id']  # 使用第一个数据源
    
    # 找到分组ID
    user_group_id = None
    order_group_id = None
    for group in groups:
        if "用户" in group['name']:
            user_group_id = group['id']
        elif "订单" in group['name']:
            order_group_id = group['id']
    
    if not user_group_id:
        user_group_id = groups[0]['id']
    if not order_group_id:
        order_group_id = groups[1]['id'] if len(groups) > 1 else groups[0]['id']
    
    # 找到标签ID
    query_tag_id = None
    create_tag_id = None
    important_tag_id = None
    for tag in tags:
        if "查询" in tag['name']:
            query_tag_id = tag['id']
        elif "新增" in tag['name']:
            create_tag_id = tag['id']
        elif "重要" in tag['name']:
            important_tag_id = tag['id']
    
    configs = [
        {
            "name": "获取用户列表",
            "path": "/user/list",
            "method": "GET",
            "description": "分页获取用户列表",
            "group_id": user_group_id,
            "datasource_id": datasource_id,
            "table_name": "users",
            "is_enabled": True,
            "is_public": False,
            "query_fields": ["name", "email", "status"],
            "required_fields": [],
            "response_fields": ["id", "name", "email", "created_at"],
            "cache_duration": 300,
            "rate_limit": 100,
            "tags": [query_tag_id] if query_tag_id else [],
            "created_by": "system"
        },
        {
            "name": "创建用户",
            "path": "/user/create", 
            "method": "POST",
            "description": "创建新用户",
            "group_id": user_group_id,
            "datasource_id": datasource_id,
            "table_name": "users",
            "is_enabled": True,
            "is_public": False,
            "query_fields": [],
            "required_fields": ["name", "email"],
            "response_fields": ["id", "name", "email", "created_at"],
            "cache_duration": 0,
            "rate_limit": 50,
            "tags": [create_tag_id, important_tag_id] if create_tag_id and important_tag_id else [],
            "created_by": "system"
        },
        {
            "name": "获取订单列表",
            "path": "/order/list",
            "method": "GET", 
            "description": "分页获取订单列表",
            "group_id": order_group_id,
            "datasource_id": datasource_id,
            "table_name": "orders",
            "is_enabled": True,
            "is_public": False,
            "query_fields": ["status", "user_id", "created_at"],
            "required_fields": [],
            "response_fields": ["id", "order_no", "amount", "status", "created_at"],
            "cache_duration": 600,
            "rate_limit": 200,
            "tags": [query_tag_id] if query_tag_id else [],
            "created_by": "system"
        }
    ]
    
    config_ids = []
    for config_data in configs:
        try:
            response = requests.post(f"{BASE_URL}/interface/configs/", json=config_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 接口配置创建成功: {result['name']} (ID={result['id']})")
                config_ids.append(result['id'])
            else:
                print(f"❌ 接口配置创建失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ 接口配置创建异常: {e}")
    
    return config_ids

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 检查现有数据并创建接口配置")
    print("=" * 60)
    
    # 获取现有数据
    datasources, groups, tags, configs = get_existing_data()
    
    # 如果没有接口配置，则创建
    if not configs:
        config_ids = create_interface_configs_with_existing_data(datasources, groups, tags)
        print(f"\n✅ 创建了 {len(config_ids)} 个接口配置")
    else:
        print(f"\n✅ 已有 {len(configs)} 个接口配置，无需创建")
    
    print("\n" + "=" * 60)
    print("🎉 检查完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
