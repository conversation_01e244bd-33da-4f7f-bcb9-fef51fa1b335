<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Setting /></el-icon>
      系统设置
    </h2>
    
    <div class="content-container">
      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <div class="config-card">
            <el-form :model="basicSettings" label-width="150px">
              <el-form-item label="系统名称">
                <el-input v-model="basicSettings.systemName"></el-input>
              </el-form-item>
              
              <el-form-item label="系统版本">
                <el-input v-model="basicSettings.version" readonly></el-input>
              </el-form-item>
              
              <el-form-item label="系统描述">
                <el-input v-model="basicSettings.description" type="textarea" :rows="3"></el-input>
              </el-form-item>
              
              <el-form-item label="管理员邮箱">
                <el-input v-model="basicSettings.adminEmail"></el-input>
              </el-form-item>
              
              <el-form-item label="时区设置">
                <el-select v-model="basicSettings.timezone">
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai"></el-option>
                  <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo"></el-option>
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York"></el-option>
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="语言设置">
                <el-select v-model="basicSettings.language">
                  <el-option label="简体中文" value="zh-CN"></el-option>
                  <el-option label="English" value="en-US"></el-option>
                  <el-option label="日本語" value="ja-JP"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 接口设置 -->
        <el-tab-pane label="接口设置" name="api">
          <div class="config-card">
            <el-form :model="apiSettings" label-width="150px">
              <el-form-item label="API基础URL">
                <el-input v-model="apiSettings.baseUrl"></el-input>
              </el-form-item>
              
              <el-form-item label="API版本">
                <el-input v-model="apiSettings.version"></el-input>
              </el-form-item>
              
              <el-form-item label="请求超时时间">
                <el-input-number v-model="apiSettings.timeout" :min="1000" :max="60000" :step="1000"></el-input-number>
                <span style="margin-left: 10px;">毫秒</span>
              </el-form-item>
              
              <el-form-item label="启用CORS">
                <el-switch v-model="apiSettings.enableCors"></el-switch>
              </el-form-item>
              
              <el-form-item label="允许的域名">
                <el-input v-model="apiSettings.allowedOrigins" type="textarea" :rows="3" placeholder="每行一个域名"></el-input>
              </el-form-item>
              
              <el-form-item label="API文档地址">
                <el-input v-model="apiSettings.docsUrl"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 日志设置 -->
        <el-tab-pane label="日志设置" name="log">
          <div class="config-card">
            <el-form :model="logSettings" label-width="150px">
              <el-form-item label="启用访问日志">
                <el-switch v-model="logSettings.enableAccessLog"></el-switch>
              </el-form-item>
              
              <el-form-item label="启用错误日志">
                <el-switch v-model="logSettings.enableErrorLog"></el-switch>
              </el-form-item>
              
              <el-form-item label="日志级别">
                <el-select v-model="logSettings.level">
                  <el-option label="DEBUG" value="debug"></el-option>
                  <el-option label="INFO" value="info"></el-option>
                  <el-option label="WARN" value="warn"></el-option>
                  <el-option label="ERROR" value="error"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="日志保留天数">
                <el-input-number v-model="logSettings.retentionDays" :min="1" :max="365"></el-input-number>
                <span style="margin-left: 10px;">天</span>
              </el-form-item>
              
              <el-form-item label="日志文件大小限制">
                <el-input-number v-model="logSettings.maxFileSize" :min="1" :max="1000"></el-input-number>
                <span style="margin-left: 10px;">MB</span>
              </el-form-item>
              
              <el-form-item label="日志输出格式">
                <el-radio-group v-model="logSettings.format">
                  <el-radio value="json">JSON格式</el-radio>
                  <el-radio value="text">文本格式</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <div class="config-card">
            <el-form :model="notificationSettings" label-width="150px">
              <el-form-item label="启用邮件通知">
                <el-switch v-model="notificationSettings.enableEmail"></el-switch>
              </el-form-item>
              
              <el-form-item label="SMTP服务器">
                <el-input v-model="notificationSettings.smtpHost"></el-input>
              </el-form-item>
              
              <el-form-item label="SMTP端口">
                <el-input-number v-model="notificationSettings.smtpPort" :min="1" :max="65535"></el-input-number>
              </el-form-item>
              
              <el-form-item label="发件人邮箱">
                <el-input v-model="notificationSettings.fromEmail"></el-input>
              </el-form-item>
              
              <el-form-item label="邮箱密码">
                <el-input v-model="notificationSettings.emailPassword" type="password"></el-input>
              </el-form-item>
              
              <el-form-item label="启用短信通知">
                <el-switch v-model="notificationSettings.enableSms"></el-switch>
              </el-form-item>
              
              <el-form-item label="短信服务商">
                <el-select v-model="notificationSettings.smsProvider">
                  <el-option label="阿里云" value="aliyun"></el-option>
                  <el-option label="腾讯云" value="tencent"></el-option>
                  <el-option label="华为云" value="huawei"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 保存按钮 -->
      <div class="save-actions">
        <el-button type="primary" @click="saveSettings" size="large">保存设置</el-button>
        <el-button @click="resetSettings" size="large">重置设置</el-button>
        <el-button @click="exportSettings" size="large">导出配置</el-button>
        <el-button @click="importSettings" size="large">导入配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('basic')

// 基本设置
const basicSettings = ref({
  systemName: 'MAPI管理系统',
  version: 'v1.0.0',
  description: '统一API管理平台，提供接口配置、测试、监控等功能',
  adminEmail: '<EMAIL>',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN'
})

// 接口设置
const apiSettings = ref({
  baseUrl: 'http://localhost:3000/api',
  version: 'v1',
  timeout: 30000,
  enableCors: true,
  allowedOrigins: 'http://localhost:3000\nhttp://localhost:8080',
  docsUrl: 'http://localhost:3000/docs'
})

// 日志设置
const logSettings = ref({
  enableAccessLog: true,
  enableErrorLog: true,
  level: 'info',
  retentionDays: 30,
  maxFileSize: 100,
  format: 'json'
})

// 通知设置
const notificationSettings = ref({
  enableEmail: false,
  smtpHost: 'smtp.qq.com',
  smtpPort: 587,
  fromEmail: '',
  emailPassword: '',
  enableSms: false,
  smsProvider: 'aliyun'
})

// 保存设置
const saveSettings = () => {
  ElMessage.success('系统设置保存成功')
}

// 重置设置
const resetSettings = () => {
  ElMessage.info('设置已重置为默认值')
}

// 导出配置
const exportSettings = () => {
  ElMessage.info('配置导出功能开发中...')
}

// 导入配置
const importSettings = () => {
  ElMessage.info('配置导入功能开发中...')
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.config-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.save-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.settings-tabs {
  margin-bottom: 20px;
}
</style>
