#!/usr/bin/env python3
"""
前后端API对接检查脚本
用于快速验证API接口是否正常工作
"""

import requests
import json
import sys

# API基础URL
BASE_URL = "http://127.0.0.1:8002/api/v1"

def check_api_health():
    """检查API服务健康状态"""
    print("🔍 检查API服务健康状态...")
    
    try:
        # 检查数据源列表接口
        response = requests.get(f"{BASE_URL}/datasource/", timeout=5)
        if response.status_code == 200:
            print("   ✅ 数据源API正常")
            return True
        else:
            print(f"   ❌ 数据源API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ API服务连接失败: {e}")
        return False

def check_error_handling():
    """检查错误处理是否正确"""
    print("\n🔍 检查错误处理...")
    
    try:
        # 测试删除不存在的资源
        response = requests.delete(f"{BASE_URL}/datasource/99999")
        
        if response.status_code == 404:
            data = response.json()
            if all(key in data for key in ['success', 'message', 'error_code']):
                print("   ✅ 404错误处理正确")
                print(f"   错误信息: {data.get('message')}")
                return True
            else:
                print("   ❌ 错误响应格式不正确")
                print(f"   响应: {data}")
                return False
        else:
            print(f"   ❌ 期望404状态码，实际: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误处理检查失败: {e}")
        return False

def check_business_logic():
    """检查业务逻辑错误处理"""
    print("\n🔍 检查业务逻辑错误处理...")
    
    try:
        # 测试删除有关联的数据源
        response = requests.delete(f"{BASE_URL}/datasource/8")
        
        if response.status_code == 409:
            data = response.json()
            required_fields = ['success', 'message', 'error_code', 'detail']
            
            if all(key in data for key in required_fields):
                detail = data.get('detail', {})
                if 'suggestion' in detail and 'related_items' in detail:
                    print("   ✅ 业务错误处理正确")
                    print(f"   错误信息: {data.get('message')}")
                    print(f"   建议: {detail.get('suggestion')}")
                    print(f"   相关项目数量: {len(detail.get('related_items', []))}")
                    return True
                else:
                    print("   ❌ 业务错误详情不完整")
                    return False
            else:
                print("   ❌ 业务错误响应格式不正确")
                return False
        else:
            print(f"   ⚠️  期望409状态码，实际: {response.status_code}")
            print("   (可能该数据源没有关联关系)")
            return True
            
    except Exception as e:
        print(f"   ❌ 业务逻辑检查失败: {e}")
        return False

def check_naming_convention():
    """检查命名规范"""
    print("\n🔍 检查命名规范...")
    
    try:
        # 获取接口配置列表，检查字段命名
        response = requests.get(f"{BASE_URL}/interface/configs/")
        
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            
            if items:
                first_item = items[0]
                # 检查是否使用下划线命名
                snake_case_fields = ['datasource_id', 'group_id', 'created_at', 'updated_at']
                camel_case_fields = ['datasourceId', 'groupId', 'createdAt', 'updatedAt']
                
                has_snake_case = any(field in first_item for field in snake_case_fields)
                has_camel_case = any(field in first_item for field in camel_case_fields)
                
                if has_snake_case and not has_camel_case:
                    print("   ✅ 后端使用下划线命名规范")
                    return True
                elif has_camel_case:
                    print("   ❌ 后端使用了驼峰命名，应该使用下划线")
                    return False
                else:
                    print("   ⚠️  无法确定命名规范")
                    return True
            else:
                print("   ⚠️  没有数据可检查命名规范")
                return True
        else:
            print(f"   ❌ 获取接口配置失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 命名规范检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 前后端API对接检查")
    print("=" * 60)
    
    checks = [
        ("API服务健康", check_api_health),
        ("错误处理", check_error_handling),
        ("业务逻辑", check_business_logic),
        ("命名规范", check_naming_convention),
    ]
    
    results = []
    for name, check_func in checks:
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过，API对接正常！")
        sys.exit(0)
    else:
        print("⚠️  部分检查失败，请参考调试规范进行修复")
        print("📖 参考文档: docs/frontend_backend_integration_guide.md")
        sys.exit(1)

if __name__ == "__main__":
    main()
