import type {
  InterfaceConfig,
  InterfaceConfigRequest,
  InterfaceConfigListResponse,
  InterfaceConfigQuery,
  TableStructure} from '@/types/interface-config';
import { mockInterfaceGroups } from './interface-group.mock';
import { mockDataSources } from './datasource.mock';

/**
 * 接口配置管理 MOCK 数据
 * 用于：InterfaceConfig.vue 接口配置管理页面
 */

/**
 * 模拟接口配置列表数据
 */
export const mockInterfaceConfigs: InterfaceConfig[] = [
  {
    id: 1,
    name: '项目列表查询',
    path: '/api/project/list',
    method: 'GET',
    group_id: 1,
    group_name: '项目管理接口',
    datasource_id: 1,
    datasource_name: '管理系统数据库',
    table_name: 'projects',
    description: '获取项目列表，支持分页和条件查询',
    is_enabled: true,
    is_public: false,
    tags: [1, 2],
    tag_names: ['查询类', '更新类'],
    query_fields: ['name', 'status', 'created_at', 'manager_id'],
    required_fields: [],
    response_fields: ['id', 'name', 'status', 'description', 'created_at', 'manager_name'],
    cache_duration: 300,
    rate_limit: 100,
    created_at: '2025-07-01 09:00:00',
    updated_at: '2025-07-11 10:30:00',
    created_by: 'admin',
    last_test_at: '2025-07-11 15:20:00',
    test_status: 'success'
  },
  {
    id: 2,
    name: '项目详情查询',
    path: '/api/project/{id}',
    method: 'GET',
    group_id: 1,
    group_name: '项目管理接口',
    datasource_id: 1,
    datasource_name: '管理系统数据库',
    table_name: 'projects',
    description: '根据项目ID获取项目详细信息',
    is_enabled: true,
    is_public: false,
    tags: [1],
    tag_names: ['查询类'],
    query_fields: ['id'],
    required_fields: ['id'],
    response_fields: ['id', 'name', 'status', 'description', 'budget', 'start_date', 'end_date', 'manager_name', 'team_members'],
    cache_duration: 600,
    rate_limit: 200,
    created_at: '2025-07-01 09:15:00',
    updated_at: '2025-07-10 16:45:00',
    created_by: 'admin',
    last_test_at: '2025-07-11 14:30:00',
    test_status: 'success'
  },
  {
    id: 3,
    name: '项目创建',
    path: '/api/project',
    method: 'POST',
    group_id: 1,
    group_name: '项目管理接口',
    datasource_id: 1,
    datasource_name: '管理系统数据库',
    table_name: 'projects',
    description: '创建新项目',
    is_enabled: true,
    is_public: false,
    tags: [2],
    tag_names: ['更新类'],
    query_fields: [],
    required_fields: ['name', 'description', 'manager_id', 'start_date'],
    response_fields: ['id', 'name', 'status', 'created_at'],
    cache_duration: 0,
    rate_limit: 50,
    created_at: '2025-07-01 09:30:00',
    updated_at: '2025-07-09 11:20:00',
    created_by: 'admin',
    last_test_at: '2025-07-11 13:45:00',
    test_status: 'success'
  },
  {
    id: 4,
    name: '员工信息查询',
    path: '/api/hr/employee/list',
    method: 'GET',
    group_id: 6,
    group_name: '人力资源接口',
    datasource_id: 6,
    datasource_name: '人力资源数据库',
    table_name: 'employees',
    description: '查询员工信息列表',
    is_enabled: true,
    is_public: false,
    tags: [1, 8],
    tag_names: ['查询类', '权限验证'],
    query_fields: ['name', 'department', 'position', 'status'],
    required_fields: [],
    response_fields: ['id', 'name', 'department', 'position', 'email', 'phone', 'hire_date'],
    cache_duration: 180,
    rate_limit: 150,
    created_at: '2025-07-02 10:00:00',
    updated_at: '2025-07-11 09:15:00',
    created_by: 'hr_admin',
    last_test_at: '2025-07-11 16:10:00',
    test_status: 'success'
  },
  {
    id: 5,
    name: '财务报表数据',
    path: '/api/finance/report',
    method: 'GET',
    group_id: 2,
    group_name: '财务管理接口',
    datasource_id: 2,
    datasource_name: '财务管理数据库',
    table_name: 'financial_records',
    description: '获取财务报表数据',
    is_enabled: true,
    is_public: false,
    tags: [1, 4],
    tag_names: ['查询类', '报表统计'],
    query_fields: ['date_range', 'department', 'category'],
    required_fields: ['date_range'],
    response_fields: ['period', 'income', 'expense', 'profit', 'department_breakdown'],
    cache_duration: 1800,
    rate_limit: 30,
    created_at: '2025-07-03 11:30:00',
    updated_at: '2025-07-10 14:20:00',
    created_by: 'finance_admin',
    last_test_at: '2025-07-11 12:00:00',
    test_status: 'failed'
  },
  {
    id: 6,
    name: '合同审批提交',
    path: '/api/contract/approval',
    method: 'POST',
    group_id: 7,
    group_name: '合同管理接口',
    datasource_id: 7,
    datasource_name: '合同管理数据库',
    table_name: 'contracts',
    description: '提交合同审批申请',
    is_enabled: false,
    is_public: false,
    tags: [2, 3],
    tag_names: ['更新类', '审批流程'],
    query_fields: [],
    required_fields: ['contract_id', 'approver_id', 'comments'],
    response_fields: ['approval_id', 'status', 'submitted_at'],
    cache_duration: 0,
    rate_limit: 20,
    created_at: '2025-07-04 14:00:00',
    updated_at: '2025-07-08 16:30:00',
    created_by: 'contract_admin',
    last_test_at: '2025-07-11 12:00:00',
    test_status: 'failed'
  },
  {
    id: 7,
    name: '文档上传',
    path: '/api/office/document/upload',
    method: 'POST',
    group_id: 4,
    group_name: '办公自动化接口',
    datasource_id: 4,
    datasource_name: '办公自动化数据库',
    table_name: 'documents',
    description: '上传文档文件',
    is_enabled: true,
    is_public: true,
    tags: [5],
    tag_names: ['文件操作'],
    query_fields: [],
    required_fields: ['file', 'title', 'category'],
    response_fields: ['document_id', 'file_url', 'upload_time'],
    cache_duration: 0,
    rate_limit: 10,
    created_at: '2025-07-05 15:30:00',
    updated_at: '2025-07-11 08:45:00',
    created_by: 'office_admin',
    last_test_at: '2025-07-11 17:00:00',
    test_status: 'pending'
  },
  {
    id: 8,
    name: '印章申请提交',
    path: '/api/seal/application',
    method: 'POST',
    group_id: 3,
    group_name: '印章管理接口',
    datasource_id: 3,
    datasource_name: '印章管理数据库',
    table_name: 'seal_applications',
    description: '提交印章使用申请',
    is_enabled: false,
    is_public: false,
    tags: [2, 3],
    tag_names: ['更新类', '审批流程'],
    query_fields: [],
    required_fields: ['applicant_id', 'seal_type', 'purpose', 'documents'],
    response_fields: ['application_id', 'status', 'submitted_at'],
    cache_duration: 0,
    rate_limit: 50,
    created_at: '2025-07-06 16:00:00',
    updated_at: '2025-07-11 11:30:00',
    created_by: 'seal_admin',
    last_test_at: '2025-07-11 16:45:00',
    test_status: 'failed'
  },
  {
    id: 9,
    name: '客户信息查询',
    path: '/api/crm/customer/list',
    method: 'GET',
    group_id: 8,
    group_name: '客户关系接口',
    datasource_id: 8,
    datasource_name: '客户关系数据库',
    table_name: 'customers',
    description: '查询客户信息列表',
    is_enabled: true,
    is_public: false,
    tags: [1, 8],
    tag_names: ['查询类', '权限验证'],
    query_fields: ['name', 'company', 'industry', 'status'],
    required_fields: [],
    response_fields: ['id', 'name', 'company', 'industry', 'contact_info', 'created_at'],
    cache_duration: 600,
    rate_limit: 200,
    created_at: '2025-07-08 10:00:00',
    updated_at: '2025-07-15 14:20:00',
    created_by: 'crm_admin',
    last_test_at: '2025-07-15 16:30:00',
    test_status: 'success'
  },
  {
    id: 10,
    name: '门户新闻发布',
    path: '/api/portal/news',
    method: 'POST',
    group_id: 5,
    group_name: '企业门户接口',
    datasource_id: 5,
    datasource_name: '企业门户数据库',
    table_name: 'news_articles',
    description: '发布企业新闻',
    is_enabled: false,
    is_public: true,
    tags: [2, 7],
    tag_names: ['更新类', '内容管理'],
    query_fields: [],
    required_fields: ['title', 'content', 'author_id', 'category'],
    response_fields: ['article_id', 'title', 'published_at', 'status'],
    cache_duration: 0,
    rate_limit: 30,
    created_at: '2025-07-09 15:00:00',
    updated_at: '2025-07-14 10:30:00',
    created_by: 'portal_admin',
    last_test_at: '2025-07-14 12:00:00',
    test_status: 'pending'
  },
  {
    id: 10,
    name: '系统配置查询',
    path: '/api/system/config',
    method: 'GET',
    group_id: 6,
    group_name: '系统管理',
    datasource_id: 6,
    datasource_name: '系统数据库',
    table_name: 'system_configs',
    description: '获取系统配置信息',
    is_enabled: true,
    is_public: false,
    tags: [1, 6],
    tag_names: ['查询类', '系统配置'],
    query_fields: ['config_key', 'category'],
    required_fields: [],
    response_fields: ['config_key', 'config_value', 'description', 'updated_at'],
    cache_duration: 3600,
    rate_limit: 500,
    created_at: '2025-07-06 16:00:00',
    updated_at: '2025-07-11 11:30:00',
    created_by: 'system_admin',
    last_test_at: '2025-07-11 16:45:00',
    test_status: 'success'
  },
  {
    id: 11,
    name: '系统配置查询',
    path: '/api/system/config',
    method: 'GET',
    group_id: 6,
    group_name: '系统管理',
    datasource_id: 6,
    datasource_name: '系统数据库',
    table_name: 'system_configs',
    description: '获取系统配置信息',
    is_enabled: true,
    is_public: false,
    tags: [1, 6],
    tag_names: ['查询类', '系统配置'],
    query_fields: ['config_key', 'category'],
    required_fields: [],
    response_fields: ['config_key', 'config_value', 'description', 'updated_at'],
    cache_duration: 3600,
    rate_limit: 500,
    created_at: '2025-07-06 16:00:00',
    updated_at: '2025-07-11 11:30:00',
    created_by: 'system_admin',
    last_test_at: '2025-07-11 16:45:00',
    test_status: 'success'
  }
];

/**
 * 模拟数据表结构数据
 */
export const mockTableStructures: Record<string, TableStructure> = {
  'projects': {
    table_name: 'projects',
    fields: [
      { name: 'id', type: 'int', comment: '项目ID', is_nullable: false, is_primary: true },
      { name: 'name', type: 'varchar', comment: '项目名称', is_nullable: false, is_primary: false },
      { name: 'description', type: 'text', comment: '项目描述', is_nullable: true, is_primary: false },
      { name: 'status', type: 'varchar', comment: '项目状态', is_nullable: false, is_primary: false, default_value: 'planning' },
      { name: 'budget', type: 'decimal', comment: '项目预算', is_nullable: true, is_primary: false },
      { name: 'start_date', type: 'date', comment: '开始日期', is_nullable: true, is_primary: false },
      { name: 'end_date', type: 'date', comment: '结束日期', is_nullable: true, is_primary: false },
      { name: 'manager_id', type: 'int', comment: '项目经理ID', is_nullable: false, is_primary: false },
      { name: 'created_at', type: 'datetime', comment: '创建时间', is_nullable: false, is_primary: false },
      { name: 'updated_at', type: 'datetime', comment: '更新时间', is_nullable: false, is_primary: false }
    ]
  },
  'employees': {
    table_name: 'employees',
    fields: [
      { name: 'id', type: 'int', comment: '员工ID', is_nullable: false, is_primary: true },
      { name: 'name', type: 'varchar', comment: '员工姓名', is_nullable: false, is_primary: false },
      { name: 'email', type: 'varchar', comment: '邮箱', is_nullable: false, is_primary: false },
      { name: 'phone', type: 'varchar', comment: '电话', is_nullable: true, is_primary: false },
      { name: 'department', type: 'varchar', comment: '部门', is_nullable: false, is_primary: false },
      { name: 'position', type: 'varchar', comment: '职位', is_nullable: false, is_primary: false },
      { name: 'status', type: 'varchar', comment: '状态', is_nullable: false, is_primary: false, default_value: 'active' },
      { name: 'hire_date', type: 'date', comment: '入职日期', is_nullable: false, is_primary: false },
      { name: 'salary', type: 'decimal', comment: '薪资', is_nullable: true, is_primary: false },
      { name: 'created_at', type: 'datetime', comment: '创建时间', is_nullable: false, is_primary: false }
    ]
  }
};

/**
 * 模拟获取接口配置列表
 */
export const mockGetInterfaceConfigs = async (
  page: number = 1,
  pageSize: number = 10,
  query?: InterfaceConfigQuery
): Promise<InterfaceConfigListResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 400));

  let filteredConfigs = [...mockInterfaceConfigs];

  // 搜索过滤
  if (query?.search) {
    const searchLower = query.search.toLowerCase();
    filteredConfigs = filteredConfigs.filter(config =>
      config.name.toLowerCase().includes(searchLower) ||
      config.path.toLowerCase().includes(searchLower) ||
      (config.description && config.description.toLowerCase().includes(searchLower))
    );
  }

  // 分组过滤
  if (query?.group_id) {
    filteredConfigs = filteredConfigs.filter(config => config.group_id === query.group_id);
  }

  // 数据源过滤
  if (query?.datasource_id) {
    filteredConfigs = filteredConfigs.filter(config => config.datasource_id === query.datasource_id);
  }

  // HTTP方法过滤
  if (query?.method) {
    filteredConfigs = filteredConfigs.filter(config => config.method === query.method);
  }

  // 启用状态过滤
  if (query?.is_enabled !== undefined) {
    filteredConfigs = filteredConfigs.filter(config => config.is_enabled === query.is_enabled);
  }

  // 公开状态过滤
  if (query?.is_public !== undefined) {
    filteredConfigs = filteredConfigs.filter(config => config.is_public === query.is_public);
  }

  // 标签过滤
  if (query?.tag_ids && query.tag_ids.length > 0) {
    filteredConfigs = filteredConfigs.filter(config =>
      config.tags && config.tags.some(tagId => query.tag_ids!.includes(tagId))
    );
  }

  // 分页处理
  const total = filteredConfigs.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filteredConfigs.slice(start, end);

  return {
    items: data,
    total,
    page,
    size: pageSize,
    pages: Math.ceil(total / pageSize)
  };
};

/**
 * 模拟创建接口配置
 */
export const mockCreateInterfaceConfig = async (
  configData: InterfaceConfigRequest
): Promise<InterfaceConfig> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 600));

  // 检查路径是否重复
  const existingConfig = mockInterfaceConfigs.find(
    config => config.path === configData.path && config.method === configData.method
  );
  if (existingConfig) {
    throw new Error(`接口路径 "${configData.method} ${configData.path}" 已存在`);
  }

  const newId = Math.max(...mockInterfaceConfigs.map(c => c.id), 0) + 1;
  const now = new Date().toLocaleString();

  // 获取分组名称
  const group = mockInterfaceGroups.find(g => g.id === configData.group_id);
  const groupName = group ? group.name : '未知分组';

  // 获取数据源名称
  const datasource = mockDataSources.find(d => d.id === configData.datasource_id);
  const datasourceName = datasource ? datasource.name : '未知数据源';

  const newConfig: InterfaceConfig = {
    id: newId,
    name: configData.name,
    path: configData.path,
    method: configData.method,
    group_id: configData.group_id,
    group_name: groupName,
    datasource_id: configData.datasource_id,
    datasource_name: datasourceName,
    table_name: configData.table_name,
    description: configData.description,
    is_enabled: configData.is_enabled ?? true,
    is_public: configData.is_public ?? false,
    tags: configData.tags || [],
    tag_names: [], // 这里可以根据tags获取tag名称
    query_fields: configData.query_fields || [],
    required_fields: configData.required_fields || [],
    response_fields: configData.response_fields || [],
    cache_duration: configData.cache_duration || 0,
    rate_limit: configData.rate_limit || 100,
    created_at: now,
    updated_at: now,
    created_by: 'current_user',
    last_test_at: '2025-07-11 12:00:00',
    test_status: 'failed'
  };

  mockInterfaceConfigs.push(newConfig);
  return { ...newConfig };
};

/**
 * 模拟更新接口配置
 */
export const mockUpdateInterfaceConfig = async (
  id: number,
  configData: InterfaceConfigRequest
): Promise<InterfaceConfig> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 600));

  const index = mockInterfaceConfigs.findIndex(config => config.id === id);
  if (index === -1) {
    throw new Error(`接口配置ID ${id} 不存在`);
  }

  // 检查路径是否重复（排除自己）
  const existingConfig = mockInterfaceConfigs.find(
    config => config.path === configData.path &&
               config.method === configData.method &&
               config.id !== id
  );
  if (existingConfig) {
    throw new Error(`接口路径 "${configData.method} ${configData.path}" 已存在`);
  }

  // 获取分组名称
  const group = mockInterfaceGroups.find(g => g.id === configData.group_id);
  const groupName = group ? group.name : '未知分组';

  // 获取数据源名称
  const datasource = mockDataSources.find(d => d.id === configData.datasource_id);
  const datasourceName = datasource ? datasource.name : '未知数据源';

  const updatedConfig: InterfaceConfig = {
    ...mockInterfaceConfigs[index],
    name: configData.name,
    path: configData.path,
    method: configData.method,
    group_id: configData.group_id,
    group_name: groupName,
    datasource_id: configData.datasource_id,
    datasource_name: datasourceName,
    table_name: configData.table_name,
    description: configData.description,
    is_enabled: configData.is_enabled ?? mockInterfaceConfigs[index].is_enabled,
    is_public: configData.is_public ?? mockInterfaceConfigs[index].is_public,
    tags: configData.tags || mockInterfaceConfigs[index].tags,
    tag_names: mockInterfaceConfigs[index].tag_names, // 保持原有的tag名称
    query_fields: configData.query_fields || mockInterfaceConfigs[index].query_fields,
    required_fields: configData.required_fields || mockInterfaceConfigs[index].required_fields,
    response_fields: configData.response_fields || mockInterfaceConfigs[index].response_fields,
    cache_duration: configData.cache_duration ?? mockInterfaceConfigs[index].cache_duration,
    rate_limit: configData.rate_limit ?? mockInterfaceConfigs[index].rate_limit,
    updated_at: new Date().toLocaleString()
  };

  mockInterfaceConfigs[index] = updatedConfig;
  return { ...updatedConfig };
};

/**
 * 模拟删除接口配置
 */
export const mockDeleteInterfaceConfig = async (id: number): Promise<boolean> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  const index = mockInterfaceConfigs.findIndex(config => config.id === id);
  if (index === -1) {
    throw new Error(`接口配置ID ${id} 不存在`);
  }

  mockInterfaceConfigs.splice(index, 1);
  return true;
};

/**
 * 模拟获取数据表结构
 */
export const mockGetTableStructure = async (
  _datasourceId: number,
  tableName: string
): Promise<TableStructure> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  const structure = mockTableStructures[tableName];
  if (!structure) {
    throw new Error(`数据表 "${tableName}" 不存在或无法访问`);
  }

  return { ...structure };
};
