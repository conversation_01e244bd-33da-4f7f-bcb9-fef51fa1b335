<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Setting /></el-icon>
      任务配置
    </h2>
    
    <div class="content-container">
      <el-tabs v-model="activeTab" class="config-tabs">
        <!-- 任务模板 -->
        <el-tab-pane label="任务模板" name="templates">
          <div class="section-title">
            <el-icon><Document /></el-icon>
            任务模板管理
          </div>
          
          <div class="template-actions">
            <el-button type="primary" @click="showCreateTemplateDialog">
              <el-icon><Plus /></el-icon>
              创建模板
            </el-button>
            <el-button @click="importTemplate">
              <el-icon><Upload /></el-icon>
              导入模板
            </el-button>
          </div>
          
          <div class="template-grid">
            <div v-for="template in taskTemplates" :key="template.id" class="template-card">
              <div class="template-header">
                <div class="template-icon">
                  <el-icon><component :is="template.icon" /></el-icon>
                </div>
                <div class="template-info">
                  <h4>{{ template.name }}</h4>
                  <p>{{ template.description }}</p>
                </div>
              </div>
              <div class="template-meta">
                <el-tag size="small" :type="template.category === '系统' ? 'primary' : 'success'">
                  {{ template.category }}
                </el-tag>
                <span class="usage-count">使用次数: {{ template.usageCount }}</span>
              </div>
              <div class="template-actions">
                <el-button size="small" @click="useTemplate(template)">使用</el-button>
                <el-button type="primary" size="small" @click="editTemplate(template)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteTemplate(template)">删除</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 执行器配置 -->
        <el-tab-pane label="执行器配置" name="executors">
          <div class="section-title">
            <el-icon><Cpu /></el-icon>
            任务执行器
          </div>
          
          <div class="executor-config">
            <el-form :model="executorConfig" label-width="150px">
              <el-form-item label="最大并发任务数">
                <el-input-number v-model="executorConfig.maxConcurrent" :min="1" :max="50"></el-input-number>
                <span style="margin-left: 10px; color: #666;">个</span>
              </el-form-item>
              
              <el-form-item label="任务超时时间">
                <el-input-number v-model="executorConfig.timeout" :min="60" :max="3600"></el-input-number>
                <span style="margin-left: 10px; color: #666;">秒</span>
              </el-form-item>
              
              <el-form-item label="失败重试次数">
                <el-input-number v-model="executorConfig.retryCount" :min="0" :max="10"></el-input-number>
                <span style="margin-left: 10px; color: #666;">次</span>
              </el-form-item>
              
              <el-form-item label="重试间隔">
                <el-input-number v-model="executorConfig.retryInterval" :min="1" :max="300"></el-input-number>
                <span style="margin-left: 10px; color: #666;">秒</span>
              </el-form-item>
              
              <el-form-item label="任务优先级策略">
                <el-radio-group v-model="executorConfig.priorityStrategy">
                  <el-radio label="fifo">先进先出</el-radio>
                  <el-radio label="priority">优先级优先</el-radio>
                  <el-radio label="shortest">最短任务优先</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="启用任务监控">
                <el-switch v-model="executorConfig.enableMonitoring"></el-switch>
              </el-form-item>
              
              <el-form-item label="监控采样间隔">
                <el-input-number v-model="executorConfig.monitoringInterval" :min="1" :max="60" :disabled="!executorConfig.enableMonitoring"></el-input-number>
                <span style="margin-left: 10px; color: #666;">秒</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 通知配置 -->
        <el-tab-pane label="通知配置" name="notifications">
          <div class="section-title">
            <el-icon><Bell /></el-icon>
            任务通知设置
          </div>
          
          <div class="notification-config">
            <el-form :model="notificationConfig" label-width="150px">
              <el-form-item label="启用任务通知">
                <el-switch v-model="notificationConfig.enabled"></el-switch>
              </el-form-item>
              
              <el-form-item label="通知方式">
                <el-checkbox-group v-model="notificationConfig.methods">
                  <el-checkbox label="email">邮件通知</el-checkbox>
                  <el-checkbox label="sms">短信通知</el-checkbox>
                  <el-checkbox label="webhook">Webhook</el-checkbox>
                  <el-checkbox label="dingtalk">钉钉通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="通知触发条件">
                <el-checkbox-group v-model="notificationConfig.triggers">
                  <el-checkbox label="success">任务成功</el-checkbox>
                  <el-checkbox label="failure">任务失败</el-checkbox>
                  <el-checkbox label="timeout">任务超时</el-checkbox>
                  <el-checkbox label="retry">任务重试</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="通知接收人">
                <el-input v-model="notificationConfig.recipients" type="textarea" :rows="3" placeholder="每行一个邮箱地址或手机号"></el-input>
              </el-form-item>
              
              <el-form-item label="Webhook地址">
                <el-input v-model="notificationConfig.webhookUrl" placeholder="http://your-webhook-url.com/notify"></el-input>
              </el-form-item>
              
              <el-form-item label="钉钉机器人Token">
                <el-input v-model="notificationConfig.dingtalkToken" placeholder="钉钉机器人的access_token"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 存储配置 -->
        <el-tab-pane label="存储配置" name="storage">
          <div class="section-title">
            <el-icon><FolderOpened /></el-icon>
            任务存储设置
          </div>
          
          <div class="storage-config">
            <el-form :model="storageConfig" label-width="150px">
              <el-form-item label="日志保留天数">
                <el-input-number v-model="storageConfig.logRetentionDays" :min="1" :max="365"></el-input-number>
                <span style="margin-left: 10px; color: #666;">天</span>
              </el-form-item>
              
              <el-form-item label="结果保留天数">
                <el-input-number v-model="storageConfig.resultRetentionDays" :min="1" :max="365"></el-input-number>
                <span style="margin-left: 10px; color: #666;">天</span>
              </el-form-item>
              
              <el-form-item label="最大日志文件大小">
                <el-input-number v-model="storageConfig.maxLogFileSize" :min="1" :max="1000"></el-input-number>
                <span style="margin-left: 10px; color: #666;">MB</span>
              </el-form-item>
              
              <el-form-item label="启用日志压缩">
                <el-switch v-model="storageConfig.enableLogCompression"></el-switch>
              </el-form-item>
              
              <el-form-item label="存储路径">
                <el-input v-model="storageConfig.storagePath" placeholder="/var/log/tasks"></el-input>
              </el-form-item>
              
              <el-form-item label="备份存储路径">
                <el-input v-model="storageConfig.backupPath" placeholder="/var/backup/tasks"></el-input>
              </el-form-item>
              
              <el-form-item label="自动清理">
                <el-switch v-model="storageConfig.autoCleanup"></el-switch>
              </el-form-item>
              
              <el-form-item label="清理时间">
                <el-time-picker v-model="storageConfig.cleanupTime" placeholder="选择清理时间" :disabled="!storageConfig.autoCleanup"></el-time-picker>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 保存按钮 -->
      <div class="save-actions">
        <el-button type="primary" @click="saveConfig" size="large">保存配置</el-button>
        <el-button @click="resetConfig" size="large">重置配置</el-button>
        <el-button @click="exportConfig" size="large">导出配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Setting, Document, Plus, Upload, Cpu, Bell, FolderOpened, DataLine, Timer, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('templates')

// 任务模板
const taskTemplates = ref([
  {
    id: 1,
    name: '数据同步模板',
    description: '用于数据库之间的数据同步任务',
    category: '数据',
    icon: 'DataLine',
    usageCount: 25
  },
  {
    id: 2,
    name: '定时清理模板',
    description: '定时清理系统日志和临时文件',
    category: '系统',
    icon: 'Delete',
    usageCount: 18
  },
  {
    id: 3,
    name: '报表生成模板',
    description: '自动生成各类业务报表',
    category: '报表',
    icon: 'Document',
    usageCount: 12
  },
  {
    id: 4,
    name: '数据备份模板',
    description: '定期备份重要数据',
    category: '备份',
    icon: 'FolderOpened',
    usageCount: 8
  }
])

// 执行器配置
const executorConfig = ref({
  maxConcurrent: 10,
  timeout: 300,
  retryCount: 3,
  retryInterval: 30,
  priorityStrategy: 'priority',
  enableMonitoring: true,
  monitoringInterval: 5
})

// 通知配置
const notificationConfig = ref({
  enabled: true,
  methods: ['email', 'webhook'],
  triggers: ['failure', 'timeout'],
  recipients: '<EMAIL>\<EMAIL>',
  webhookUrl: '',
  dingtalkToken: ''
})

// 存储配置
const storageConfig = ref({
  logRetentionDays: 30,
  resultRetentionDays: 90,
  maxLogFileSize: 100,
  enableLogCompression: true,
  storagePath: '/var/log/tasks',
  backupPath: '/var/backup/tasks',
  autoCleanup: true,
  cleanupTime: null
})

// 显示创建模板对话框
const showCreateTemplateDialog = () => {
  ElMessage.info('创建模板功能开发中...')
}

// 导入模板
const importTemplate = () => {
  ElMessage.info('导入模板功能开发中...')
}

// 使用模板
const useTemplate = (template: any) => {
  ElMessage.success(`使用模板: ${template.name}`)
}

// 编辑模板
const editTemplate = (template: any) => {
  ElMessage.info(`编辑模板: ${template.name}`)
}

// 删除模板
const deleteTemplate = (template: any) => {
  ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('模板删除成功')
  })
}

// 保存配置
const saveConfig = () => {
  ElMessage.success('任务配置保存成功')
}

// 重置配置
const resetConfig = () => {
  ElMessage.info('配置已重置为默认值')
}

// 导出配置
const exportConfig = () => {
  ElMessage.info('配置导出功能开发中...')
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary);
  padding-left: 12px;
}

.template-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.template-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: all 0.3s;
}

.template-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #409eff;
  font-size: 20px;
}

.template-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.template-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.usage-count {
  font-size: 12px;
  color: #999;
}

.template-actions {
  display: flex;
  gap: 8px;
}

.executor-config, .notification-config, .storage-config {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.save-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.config-tabs {
  margin-bottom: 20px;
}
</style>
