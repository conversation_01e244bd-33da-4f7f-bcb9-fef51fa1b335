---
type: "always_apply"
---

# 接口参数配置设计方案

## 概述

参考 Postman/Apifox 的参数结构，设计一套完整的接口参数配置系统，将ORM模型配置和接口参数配置分离。

## 参数分类

### 1. Path Parameters (路径参数)

```json
{
  "type": "path",
  "name": "id",
  "description": "用户ID",
  "required": true,
  "dataType": "integer",
  "example": "123"
}
```

### 2. Query Parameters (查询参数)

#### 2.1 系统级参数

```json
{
  "type": "query",
  "category": "system",
  "parameters": [
    {
      "name": "page",
      "description": "页码",
      "required": false,
      "dataType": "integer",
      "defaultValue": 1,
      "example": "1"
    },
    {
      "name": "size",
      "description": "每页数量",
      "required": false,
      "dataType": "integer",
      "defaultValue": 10,
      "example": "10"
    },
    {
      "name": "keyword",
      "description": "模糊查询关键词",
      "required": false,
      "dataType": "string",
      "example": "张三"
    }
  ]
}
```

#### 2.2 业务查询参数

```json
{
  "type": "query",
  "category": "business",
  "parameters": [
    {
      "name": "status",
      "description": "状态筛选",
      "required": false,
      "dataType": "enum",
      "options": ["active", "inactive", "pending"],
      "example": "active"
    },
    {
      "name": "createTime",
      "description": "创建时间范围",
      "required": false,
      "dataType": "dateRange",
      "example": "2024-01-01,2024-12-31"
    }
  ]
}
```

### 3. Header Parameters (请求头参数)

```json
{
  "type": "header",
  "parameters": [
    {
      "name": "Authorization",
      "description": "认证令牌",
      "required": true,
      "dataType": "string",
      "example": "Bearer eyJhbGciOiJIUzI1NiIs..."
    },
    {
      "name": "Content-Type",
      "description": "内容类型",
      "required": true,
      "dataType": "string",
      "defaultValue": "application/json",
      "example": "application/json"
    }
  ]
}
```

### 4. Body Parameters (请求体参数)

```json
{
  "type": "body",
  "contentType": "application/json",
  "schema": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "用户名",
        "required": true,
        "example": "张三"
      },
      "email": {
        "type": "string",
        "description": "邮箱",
        "required": true,
        "format": "email",
        "example": "<EMAIL>"
      }
    }
  }
}
```

## ORM模型配置 vs 接口参数配置

### ORM模型配置

- **作用**: 定义数据表字段映射关系
- **内容**: 字段类型、字段别名、数据转换规则
- **示例**: `user_name` -> `userName`, `created_at` -> `createdTime`

### 接口参数配置

- **作用**: 定义接口的输入参数结构
- **内容**: 参数验证、参数类型、默认值、示例值
- **示例**: 分页参数、查询条件、认证信息

## 实现建议

### 1. 参数配置编辑器

创建类似 Postman 的参数编辑界面：

- Tab切换：Path、Query、Headers、Body
- 表格形式编辑参数
- 参数类型选择器
- 实时预览功能

### 2. 参数模板

提供常用参数模板：

- 分页模板：page, size, sort
- 查询模板：keyword, status, dateRange
- 认证模板：Authorization, API-Key

### 3. 参数验证

- 必填参数检查
- 数据类型验证
- 格式验证（邮箱、手机号等）
- 参数依赖关系

### 4. 文档生成

自动生成接口文档：

- 参数说明表格
- 请求示例
- 响应示例
- 错误码说明

## 数据结构设计

```typescript
interface InterfaceParams {
  pathParams: PathParam[];
  queryParams: QueryParam[];
  headerParams: HeaderParam[];
  bodyParams: BodyParam;
}

interface BaseParam {
  name: string;
  description: string;
  required: boolean;
  dataType: string;
  example?: string;
  defaultValue?: any;
}

interface PathParam extends BaseParam {
  type: 'path';
}

interface QueryParam extends BaseParam {
  type: 'query';
  category: 'system' | 'business';
}

interface HeaderParam extends BaseParam {
  type: 'header';
}

interface BodyParam {
  type: 'body';
  contentType: string;
  schema: JSONSchema;
}
```

## 下一步实现

1. 创建参数配置编辑器组件
2. 实现参数模板功能
3. 集成到接口配置管理
4. 添加参数验证逻辑
5. 生成接口文档功能
