<!-- 此组件用于展示页签容器，支持创建、关闭、刷新页签，以及通过右键菜单对页签进行批量操作 -->
<template>
  <div class="tab-container">
    <el-tabs 
      ref="elTabsRef"
      v-model:activeName="activeName"
      :key="tabs.map(t => t.name).join(',')"
      type="card" 
      closable 
      @tab-remove="closeCurrentTab" 
      @tab-click="handleTabClick" 
      @contextmenu.prevent="onTabsContextMenu"
    >
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.name"
        :label="tab.title"
        :name="tab.name"
      >
      <template #label>
        <span :style="tab.name === activeName ? 'color:#1677ff;font-weight:500;display:inline-flex;align-items:center;' : ''"
              @contextmenu.prevent="tabStore.showContextMenu($event, tab)"
        >
          <template v-if="tab.name === activeName">
            <el-icon style="font-size:16px;color:#1677ff;margin-right:4px;vertical-align:middle;">
              <CircleCheckFilled />
            </el-icon>
          </template>
          {{ tab.title }}
        </span>
        <i 
          class="el-icon-close" 
          @click.stop="() => {
            // 设置 tabStore 中的当前上下文页签信息，用于后续关闭、刷新等操作
            tabStore.contextTab = { 
              name: tab.name, 
              title: tab.title, 
              path: tab.path, 
              paneName: tab.name 
            };
            closeCurrentTab();
          }" 
          style="margin-left: 8px; cursor: pointer;"
        ></i>
      </template>
        <iframe :src="tab.path" class="page-frame" :ref="(el) => setPageFrameRef(tab.name, el as HTMLIFrameElement | null)"></iframe>
      </el-tab-pane>
    </el-tabs>
    <div v-if="tabStore.contextMenuVisible" class="context-menu" :style="tabStore.contextMenuStyle" ref="contextMenuRef">
      <div class="context-menu-item" @click="closeCurrentTab">关闭当前页签</div>
      <div class="context-menu-item" @click="closeOtherTabs">关闭其他页签</div>
      <div class="context-menu-item" @click="closeAllTabs">关闭所有页签</div>
      <div class="context-menu-item" @click="closeLeftTabs">关闭左侧页签</div>
      <div class="context-menu-item" @click="closeRightTabs">关闭右侧页签</div>
      <div class="context-menu-item" @click="refreshTab">刷新当前页签</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { ElIcon, ElTabs } from 'element-plus';
import { CircleCheckFilled } from '@element-plus/icons-vue';
import { useTabContainerStore } from '@/stores/tabContainerStore';
import type { TabsPaneContext } from 'element-plus';

// 从Pinia store获取状态，替代props
const tabStore = useTabContainerStore();
const tabs = toRefs(tabStore).openedTabs;
const activeName = toRefs(tabStore).modelActiveName;

const elTabsRef = ref<InstanceType<typeof ElTabs> | null>(null);
// 本地引用
const contextMenuRef = ref<HTMLElement | null>(null);
const pageFrames = ref<Record<string, HTMLIFrameElement | null>>({});


// 事件处理逻辑
//点击外部区域关闭右键菜单
const handleClickOutside = (event: MouseEvent) => {
  if (tabStore.contextMenuVisible && contextMenuRef.value) {
    // 检查点击是否发生在菜单外部
    if (!contextMenuRef.value.contains(event.target as Node)) {
      tabStore.hideContextMenu();
    }
  }
};

// 注册事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

/**
 * iframe内部点击事件处理函数
 * 当iframe内发生点击时关闭右键菜单
 */
const handleIframeClick = () => {
  if (tabStore.contextMenuVisible) {
    tabStore.hideContextMenu();
  }
};

// 存储每个iframe的load处理函数，用于正确移除事件监听器
const iframeLoadHandlers = new Map<HTMLIFrameElement, () => void>();

// 封装ref赋值逻辑以解决类型推断问题
const setPageFrameRef = (tabName: string, el: HTMLIFrameElement | null) => {
  pageFrames.value[tabName] = el;
  if (el) {
    // 移除可能存在的旧监听器（避免重复绑定）
    const oldHandler = iframeLoadHandlers.get(el);
    if (oldHandler) {
      el.removeEventListener('load', oldHandler);
    }

    /**
     * iframe加载完成事件处理函数
     * 为iframe内部文档添加点击事件监听，解决iframe内点击无法关闭右键菜单的问题
     */
    const handleIframeLoad = () => {
      try {
        const iframeWindow = el?.contentWindow;
        if (iframeWindow) {
          // 移除可能存在的旧监听器
          iframeWindow.removeEventListener('click', handleIframeClick);
          // 添加新的点击事件监听器
          iframeWindow.addEventListener('click', handleIframeClick);
        }
      } catch (e) {
        console.warn('为iframe添加点击监听失败（可能存在跨域限制）:', e);
      }
    };

    // 存储处理函数引用，以便后续移除
    iframeLoadHandlers.set(el, handleIframeLoad);
    // 添加新的事件监听器
    el.addEventListener('load', handleIframeLoad);
  }
};

// 事件处理逻辑
//关闭当前指定页签
const closeCurrentTab = () => {
  if (!tabStore.contextTab) return;
  tabStore.closeCurrentTab(tabStore.contextTab.name);
  tabStore.hideContextMenu();
};

const closeOtherTabs = () => {
  if (!tabStore.contextTab) return;
  tabStore.closeOtherTabs(tabStore.contextTab.name);
  tabStore.hideContextMenu();
};

const handleTabClick = (pane: TabsPaneContext) => {
  if (pane.paneName) {
     tabStore.modelActiveName = pane.paneName.toString();
  }
};

/*
onTabsContextMenu 是标签栏区域的右键菜单触发处理器，
专门处理 在标签栏空白处或标签页上右键点击 的场景，
是右键菜单功能的重要入口。
*/
const onTabsContextMenu = (event: MouseEvent) => {
  // 1. 查找右键点击的标签页元素
  let el = event.target as HTMLElement | null;
  while (el && !el.classList.contains('el-tabs__item')) {
    el = el.parentElement;
  }

  // 2. 如果找到了标签页元素
  if (el) {
    // 3. 获取标签页ID（从aria-controls属性解析）
    const paneId = el.getAttribute('aria-controls');
    if (paneId) {
      // 4. 提取标签页名称（移除前缀"pane-"）
      const tabName = paneId.replace('pane-', '');
      // 5. 查找对应的标签页数据
      const tab = tabs.value.find(t => t.name === tabName);
      if (tab) {
        // 6. 调用组合式函数显示右键菜单
        tabStore.showContextMenu(event, tab);
      }
    }
  }
};

const closeAllTabs = () => {
  tabStore.closeAllTabs();
  tabStore.hideContextMenu();
}; 
const closeLeftTabs = () => {
  if (!tabStore.contextTab) return;
  tabStore.closeLeftTabs(tabStore.contextTab.name);
  tabStore.hideContextMenu();
};
const closeRightTabs = () => {
  if (!tabStore.contextTab) return;
  tabStore.closeRightTabs(tabStore.contextTab.name);
  tabStore.hideContextMenu();
}; 
const refreshTab = () => {
  if (!tabStore.contextTab) return;
  // 使用模板ref获取iframe元素
  const iframe = pageFrames.value[tabStore.contextTab.name];
  if (iframe) iframe.src = tabStore.contextTab.path;
  tabStore.hideContextMenu();
}; 

// 调试监听器是否初始化
watch(
  // 改为监听整个数组而非仅长度，增加深度监听 新增页签激活并显示在可视区域
  () => tabStore.openedTabs,
  async (newTabs, oldTabs) => {
    if (newTabs.length > (oldTabs?.length || 0)) {
      const newTab = newTabs[newTabs.length - 1];
      if (newTab) {
        tabStore.modelActiveName = newTab.paneName.toString();       
        await nextTick();
        const tabElements = elTabsRef.value?.$el.querySelectorAll('.el-tabs__item');
        const newTabElement = tabElements?.[tabElements.length - 1];
        if (newTabElement && 'click' in newTabElement) {
          (newTabElement as HTMLElement).click();
        }
      }
    } 
  },
  // 增加深度监听选项确保数组内部变化可被检测
  { deep: true, immediate: true }
);

// 监听pendingTabActivation状态变化以触发页签点击事件
// 修改pendingTabActivation监听器，仅处理已存在页签的点击激活 设置is-active属性
watch(
  () => tabStore.pendingTabActivation,
  async (tab) => {
    if (tab) {
      await nextTick();
      // 直接通过tab.name查找对应的页签元素
      const tabElements = elTabsRef.value?.$el.querySelectorAll('.el-tabs__item');
      const targetTabElement = Array.from(tabElements || []).find(el => {
        return (el as HTMLElement).getAttribute('aria-controls') === `pane-${tab.name}`;
      });
      if (targetTabElement && typeof targetTabElement === 'object' && 'click' in targetTabElement) {
        (targetTabElement as HTMLElement).click();
      }
      tabStore.pendingTabActivation = null;
    }
  }
);
</script>

<style lang="scss" scoped>
@use '@/assets/styles/framework.scss' as *;

/* TabContainer.vue 特有样式 */
.tab-container {
  height: 100%;
}

.el-tabs {
  height: 100%;
}

.el-tab-pane {
  height: 100%;
  overflow: hidden;
}

.page-frame {
  width: 100%;
  height: 100%;
  border: none;
  overflow: hidden;
}

/* 隐藏iframe内部滚动条 */
:deep(.page-frame) {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

:deep(.page-frame::-webkit-scrollbar) {
  display: none; /* Chrome, Safari, Opera */
}

:deep(.el-tabs__item.is-active) {
  color: #1677ff;
  border-bottom: 2px solid #1677ff !important;
  /* 移除默认下边框 */
  box-shadow: none !important;
}
:deep(.el-tabs__item) {
  border-bottom: none !important;

  &:first-child {
    border-left: none !important;
    box-shadow: none !important;
    &::before {
      display: none !important;
    }
  }
}

:deep(.el-tabs__active-bar) {
  display: none !important;
  height: 0 !important;
}

:deep(.el-tabs__header) {
  border-top: none !important;
  margin-bottom: 0 !important;
}

:deep(.el-tabs__nav-wrap) {
  border-radius: 0 !important;
  overflow-x: auto !important;
}

:deep(.el-tabs__nav) {
  border-radius: 0 !important;
}

:deep(.el-tabs__nav.is-top) {
  border-top: none !important;
  border-left: none !important;
  box-shadow: none !important;
}

/* Tab特有的右键菜单样式 */
.context-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  @include framework-transition-base;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}
</style>
