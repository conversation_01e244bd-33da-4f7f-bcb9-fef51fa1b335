<!-- 
  ConfirmDialog - 通用确认对话框组件
  功能: 提供标准化的操作确认弹窗，支持自定义标题、内容、按钮文本及图标类型
  特点:
    1. 支持四种预设图标类型(warning/info/success/error)
    2. 可自定义确认/取消按钮文本和类型
    3. 支持加载状态控制和禁用状态
    4. 提供自定义内容插槽(slot)扩展
  Props:
    modelValue - 控制对话框显示/隐藏
    title - 对话框标题
    content - 主要提示内容
    warning - 警告说明文本
    confirmText/cancelText - 按钮文本
    confirmType - 确认按钮类型
    iconType - 图标类型
    loading - 加载状态控制
  Events:
    confirm - 确认按钮点击事件
    cancel - 取消按钮点击事件
-->
<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    center
    :append-to-body="true"
  >
    <div class="confirm-content">
      <el-icon v-if="showIcon" :class="['confirm-icon', iconType]">
        <component :is="iconComponent" />
      </el-icon>
      
      <div class="confirm-text">
        <p v-if="content" class="main-content">{{ content }}</p>
        <p v-if="warning" class="warning-text">{{ warning }}</p>
        
        <!-- 支持自定义内容插槽 -->
        <slot></slot>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button
          :type="confirmType"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Warning, InfoFilled, CircleCheck, CircleClose } from '@element-plus/icons-vue';

interface Props {
  modelValue: boolean;
  title?: string;
  content?: string;
  warning?: string;
  confirmText?: string;
  cancelText?: string;
  confirmType?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  width?: string;
  showIcon?: boolean;
  iconType?: 'warning' | 'info' | 'success' | 'error';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '确认操作',
  confirmText: '确认',
  cancelText: '取消',
  confirmType: 'primary',
  width: '400px',
  showIcon: true,
  iconType: 'warning',
  loading: false
});

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}

const emit = defineEmits<Emits>();

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

const iconComponent = computed(() => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: CircleCheck,
    error: CircleClose
  };
  return iconMap[props.iconType];
});

// 事件处理
const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};
</script>

<style scoped>
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px 0;
}

.confirm-icon {
  font-size: 50px;
  flex-shrink: 0;
  margin-top: 5px;
}

.confirm-icon.warning {
  color: #e6a23c;
}

.confirm-icon.info {
  color: #409eff;
}

.confirm-icon.success {
  color: #67c23a;
}

.confirm-icon.error {
  color: #f56c6c;
}

.confirm-text {
  flex: 1;
}

.main-content {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
  line-height: 1.5;
}

.warning-text {
  margin: 10px 0 0 0;
  color: #f56c6c;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

/* 当没有图标时，调整布局 */
.confirm-content:has(.confirm-icon[style*="display: none"]) {
  justify-content: center;
  text-align: center;
}

/* 修复按钮区域的样式 */
:deep(.el-dialog__footer) {
  padding: 15px 20px 20px 20px !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}
</style>
