"""
数据源数据模式
Pydantic模型定义，用于API请求和响应的数据验证
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class DatabaseType(str, Enum):
    """数据库类型枚举"""
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    SQLSERVER = "sqlserver"
    ORACLE = "oracle"
    SQLITE = "sqlite"

class DataSourceStatus(str, Enum):
    """数据源状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class DataSourceBase(BaseModel):
    """数据源基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="数据源名称")
    description: Optional[str] = Field(None, max_length=200, description="数据源描述")
    db_type: DatabaseType = Field(..., description="数据库类型")
    host: str = Field(..., min_length=1, max_length=255, description="主机地址")
    port: int = Field(..., ge=0, le=65535, description="端口号")
    database: str = Field(..., min_length=1, max_length=100, description="数据库名")
    username: str = Field(..., min_length=1, max_length=100, description="用户名")
    max_connections: int = Field(10, ge=1, le=100, description="最大连接数")
    connection_timeout: int = Field(60, ge=5, le=300, description="连接超时时间(秒)")
    refresh_time: Optional[str] = Field(None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="定时刷新时间(HH:MM)")
    status: DataSourceStatus = Field(DataSourceStatus.ACTIVE, description="状态")

class DataSourceCreate(DataSourceBase):
    """创建数据源请求模式"""
    password: str = Field(..., min_length=1, max_length=500, description="密码（支持加密后的长密码）")
    created_by: Optional[str] = Field(None, max_length=50, description="创建人")

class DataSourceUpdate(BaseModel):
    """更新数据源请求模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="数据源名称")
    description: Optional[str] = Field(None, max_length=200, description="数据源描述")
    db_type: Optional[DatabaseType] = Field(None, description="数据库类型")
    host: Optional[str] = Field(None, min_length=1, max_length=255, description="主机地址")
    port: Optional[int] = Field(None, ge=0, le=65535, description="端口号")
    database: Optional[str] = Field(None, min_length=1, max_length=100, description="数据库名")
    username: Optional[str] = Field(None, min_length=1, max_length=100, description="用户名")
    password: Optional[str] = Field(None, min_length=1, max_length=500, description="密码（支持加密后的长密码）")
    max_connections: Optional[int] = Field(None, ge=1, le=100, description="最大连接数")
    connection_timeout: Optional[int] = Field(None, ge=5, le=300, description="连接超时时间(秒)")
    refresh_time: Optional[str] = Field(None, pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="定时刷新时间(HH:MM)")
    status: Optional[DataSourceStatus] = Field(None, description="状态")

class DataSourceResponse(DataSourceBase):
    """数据源响应模式"""
    id: int = Field(..., description="数据源ID")
    password: str = Field(..., description="密码(加密)")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建人")

    class Config:
        from_attributes = True
        orm_mode = True

class DataSourceListResponse(BaseModel):
    """数据源列表响应模式"""
    items: List[DataSourceResponse] = Field(..., description="数据源列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class ConnectionTestResponse(BaseModel):
    """连接测试响应模式"""
    success: bool = Field(..., description="连接是否成功")
    message: str = Field(..., description="测试结果消息")
    response_time: Optional[float] = Field(None, description="响应时间(毫秒)")
    error_detail: Optional[str] = Field(None, description="错误详情")


