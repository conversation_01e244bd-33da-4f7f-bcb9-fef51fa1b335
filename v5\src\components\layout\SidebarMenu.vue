<template>
  <el-aside
    class="sidebar-menu-component"
    :class="{ collapsed: sidebarStore.isCollapse }"
    :width="sidebarStore.isCollapse ? '60px' : '240px'"
  >
  <div class="nav-logo">
    <div class="logo-container">
      <span v-if="sidebarStore.isCollapse" class="logo-m">M</span>
      <span class="logo-text" v-show="sidebarStore.showText">MAPI管理系统</span>
    </div>
  </div>

  <div class="nav-menu">
    <el-menu
      ref="menuRef"
      :default-active="sidebarStore.currentMenu"
      :default-openeds="defaultOpenedMenus"
      mode="vertical"
      :collapse="sidebarStore.isCollapse"
      background-color="#2D313D"
      text-color="#fff"
      active-text-color="#ffd04b"
      @select="handleMenuSelect"
      :unique-opened="true"
    >
        <!-- 动态菜单渲染 -->
        <template v-if="sidebarStore.menuData?.topLevelMenus">
          <el-sub-menu
            v-for="menu in sidebarStore.menuData.topLevelMenus"
            :key="menu.key"
            :index="menu.key"
          >
            <template #title>
              <el-icon :class="`top-level-icon icon-${menu.key}`">
                <component :is="menu.icon || House" />
              </el-icon>
              <span>{{ menu.title }}</span>
            </template>
            <el-menu-item
              v-for="subMenu in menu.children"
              :key="subMenu.name"
              :index="subMenu.name"
            >
              {{ subMenu.title }}
            </el-menu-item>
          </el-sub-menu>
        </template>

        <template v-else>
          <!-- 加载中占位 -->
          <el-menu-item disabled :index="'loading-' + Math.random().toString(36).substr(2, 9)">
            <el-skeleton width="100%" height="40px" />
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </el-aside>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { ElMenu } from 'element-plus';
import { House } from '@element-plus/icons-vue';
import { useSidebarMenuStore } from '@/stores/sidebarMenuStore';
import { useTabContainerStore } from '@/stores/tabContainerStore';

const sidebarStore = useSidebarMenuStore();
const tabStore = useTabContainerStore();

// 计算默认展开的一级菜单（用于页面初始化）
const defaultOpenedMenus = computed(() => {
  if (sidebarStore.menuData?.topLevelMenus?.length) {
    return [sidebarStore.menuData.topLevelMenus[0].key];
  }
  return [];
});

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  sidebarStore.setSelectedMenu(index);
  const pageInfo = sidebarStore.handleMenuSelect(index);
  if (pageInfo) {
    tabStore.addTab(pageInfo);
  }
};

// 监听菜单数据变化，设置默认选中的菜单项
watch(
  () => sidebarStore.menuData,
  (newMenuData) => {
    if (newMenuData && newMenuData.topLevelMenus && newMenuData.topLevelMenus.length > 0) {
      const firstMenu = newMenuData.topLevelMenus[0];
      if (firstMenu.children && firstMenu.children.length > 0) {
        const firstSubMenu = firstMenu.children[0];
        sidebarStore.setSelectedMenu(firstSubMenu.name);
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
@use '@/assets/styles/framework.scss' as *;

/* SidebarMenu.vue 特有样式 */
.el-aside {
  width: 240px !important;
  min-width: 240px !important;
  background-color: var(--sidebar-bg, #2D313D) !important;
  border-right: 1px solid var(--border-color, #e4e7ed);
  @include framework-flex-column;
  @include framework-transition-width;
}

.sidebar-menu-component.collapsed {
  width: 60px !important;
  min-width: 60px !important;
  @include framework-transition-base;

  .logo-text {
    visibility: hidden;
    opacity: 0;
    transform: translateX(-10px);
    @include framework-transition-opacity;
  }

  .el-menu {
    --el-tooltip-max-width: 100px !important;
    width: 60px !important;
    @include framework-transition-width;
  }

  .el-sub-menu__title span,
  .el-menu-item span {
    visibility: hidden;
    opacity: 0;
    transform: translateX(-10px);
    @include framework-transition-opacity;
  }

  :deep(.el-sub-menu__icon-arrow) {
    display: none !important;
  }
}

.logo-icon {
  margin-right: 2px;
  flex-shrink: 0;
}

.nav-logo {
  height: var(--header-height, 60px) !important;
  padding: 0 15px;
  @include framework-flex-center;
  font-weight: bold;
  @include framework-text-white;
  border-bottom: 1px solid #0fd7f6;
  flex-shrink: 0;
  justify-content: center; // Logo区域居中
  background-color: var(--primary-color, #3FC8DD) !important;
}

.logo-container {
  height: var(--header-height, 60px) !important;
  width: 100%;
  @include framework-flex-center;
  justify-content: center; // 居中显示
  padding-left: 0; // 避免整体偏右
  gap: 8px;
}

.logo-text {
  font-size: calc(16px + 0.3vw);
  max-width: 100%;
  text-align: left;
  min-width: min-content;
  transform: translateY(-1px) scale(1) translateX(0);
  transform-origin: left center;
  opacity: 1;
  margin-left: -10px; // 左移3px（原为-7px）
  transition: opacity 0.3s ease-out 0.4s, transform 0.3s cubic-bezier(0.22, 1, 0.36, 1) 0.4s;
}

.sidebar-menu-component:not(.collapsed) .logo-text {
  visibility: visible !important;
  opacity: 1 !important;
  transform: translateY(-1px) scale(1) translateX(0) !important;
  transition: opacity 0.3s ease-out 0.4s, transform 0.3s cubic-bezier(0.22, 1, 0.36, 1) 0.4s;
}

.sidebar-menu-component.collapsed .logo-text {
  visibility: hidden;
  opacity: 0;
  transform: translateY(-1px) scale(0.9) translateX(-10px);
  transition: visibility 0s linear 0.2s, opacity 0.3s ease-out, transform 0.3s cubic-bezier(0.22, 1, 0.36, 1);
}

.sidebar-menu-component:not(.collapsed) .logo-text {
  opacity: 1;
  transform: translateY(-1px) scale(1) translateX(0);
}

.logo-m {
  font-style: italic;
  font-size: 28px; // 稍微大点
  font-weight: bold;
  color: #2c6fbb;
  letter-spacing: 2px;
  user-select: none;
  transform: skewX(-7deg); // 斜度小点
  border-radius: 6px;
  padding: 2px 8px 2px 8px;
  margin-right: 0;
  margin-left: 4px; // 右移2px
  box-shadow: 0 1px 4px 0 rgba(44,111,187,0.06);
}

:deep(.logo-text) {
  font-style: italic; /* 或使用 transform: skewX(-5deg); 模拟斜度 */
  font-size: calc(16px + 0.3vw); /* 与 index.html 一致 */
  text-align: left; /* 保持左对齐 */
}

@media (max-width: 300px) {
  .logo-text {
    font-size: 16px;
  }
}

.nav-menu {
  flex: 1;
  @include framework-custom-scrollbar;
  min-height: 0;
  padding: 10px 0;
}

.el-menu {
  --el-tooltip-max-width: 100px !important; /* 菜单特有的Tooltip宽度 */
  border-right: none !important;
  background-color: transparent !important;
}

/* 菜单特有的Tooltip样式 */
:deep(.sidebar-menu-component .el-menu .el-tooltip__popper) {
  max-width: 100px !important;
  width: auto !important;
  min-width: auto !important;
  padding: 5px 10px !important;
}

:deep(.el-menu-item),
.el-menu-item,
:deep(.el-sub-menu__title) {
  margin: 2px 0;
  height: 44px;
  line-height: 44px;
  padding: 0 16px;
  @include framework-transition-base;
}

.el-sub-menu__title span,
.el-menu-item span {
  @include framework-transition-opacity;
}

.el-sub-menu__title {
  border-bottom: 1px solid #f0f0f0 !important;
  margin-bottom: 5px !important;
}

.el-menu .el-menu-item {
  padding-left: 52px !important;
  font-size: 14px;
  height: 44px !important; /* 调整为与index.html一致的较小高度 */
  line-height: 44px !important;
}

.el-sub-menu__title > .el-icon:not(.el-sub-menu__icon-arrow) {
  order: -1 !important;
  margin-right: 10px !important;
}

.el-menu-item > .el-icon {
  order: -1 !important;
  margin-right: 10px !important;
}

.el-sub-menu__icon-arrow {
  order: 3 !important;
  margin-left: auto !important;
  position: static !important;
  transform: none !important;
  font-size: 14px;
}

.el-menu-item:hover,
.el-sub-menu__title:hover {
  background-color: #919495 !important;
}

.el-menu-item.is-active {
  background-color: #212128 !important;
  font-weight: 500;
}

.el-menu-item.is-active i {
  color: var(--primary-color, #3FC8DD) !important;
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  font-weight: 500;
}

.top-level-icon {
  font-size: 18px;
  color: #fff; // 菜单图标统一为白色
}

.collapse-trigger {
  cursor: pointer;
  font-size: 18px;
  margin-left: 0;
}
</style>


