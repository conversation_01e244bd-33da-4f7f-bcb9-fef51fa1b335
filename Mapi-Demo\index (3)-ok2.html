<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表BI系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#00B42A',
                        neutral: '#F5F7FA',
                        'neutral-dark': '#E5E6EB',
                        'text-primary': '#1D2129',
                        'text-secondary': '#4E5969',
                        'text-tertiary': '#86909C'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .card-shadow {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            }
            .canvas-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }
            .canvas-layout-2x2 {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                gap: 20px;
            }
            .canvas-layout-1x2 {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            .canvas-layout-2x1 {
                display: grid;
                grid-template-rows: 1fr 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body class="font-inter bg-neutral text-text-primary min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <i class="fa fa-bar-chart text-primary text-2xl"></i>
                <h1 class="text-xl font-bold text-primary">报表BI系统</h1>
            </div>
            
            <div class="flex items-center space-x-6">
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-primary font-medium border-b-2 border-primary pb-1">组件管理</a>
                    <a href="#" class="text-text-secondary hover:text-primary transition-colors duration-200">画布管理</a>
                    <a href="#" class="text-text-secondary hover:text-primary transition-colors duration-200">报表管理</a>
                </nav>
                
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-full hover:bg-neutral transition-colors duration-200">
                        <i class="fa fa-bell-o text-text-secondary"></i>
                    </button>
                    <button class="p-2 rounded-full hover:bg-neutral transition-colors duration-200">
                        <i class="fa fa-cog text-text-secondary"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://picsum.photos/id/1005/200/200" alt="用户头像" class="w-8 h-8 rounded-full object-cover">
                        <span class="text-sm font-medium hidden md:inline">系统管理员</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="flex-grow container mx-auto px-4 py-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center space-x-2 text-sm mb-6">
            <a href="#" class="text-text-secondary hover:text-primary transition-colors duration-200">首页</a>
            <i class="fa fa-angle-right text-text-tertiary"></i>
            <span class="text-primary font-medium">组件管理</span>
        </div>

        <!-- 样式切换器 -->
        <div class="mb-8 flex justify-end">
            <div class="bg-white rounded-lg shadow-sm p-2 inline-flex">
                <button class="px-4 py-2 rounded-md bg-primary text-white" data-style="default">默认风格</button>
                <button class="px-4 py-2 rounded-md text-text-secondary hover:bg-neutral transition-colors duration-200" data-style="business">商务风格</button>
                <button class="px-4 py-2 rounded-md text-text-secondary hover:bg-neutral transition-colors duration-200" data-style="fresh">清新风格</button>
            </div>
        </div>

        <!-- 功能区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold mb-2">组件管理</h2>
                    <p class="text-text-tertiary">创建和管理数据可视化组件，如饼图、柱状图、表格等</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="搜索组件..." class="pl-10 pr-4 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full md:w-64">
                        <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary"></i>
                    </div>
                    <button class="bg-secondary hover:bg-secondary/90 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fa fa-plus mr-2"></i> 新建组件
                    </button>
                </div>
            </div>

            <!-- 组件卡片网格 -->
            <div class="canvas-grid">
                <!-- 合同饼图组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4">
                        <canvas id="contractPieChart"></canvas>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">合同饼图</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">展示不同类型合同的占比情况</p>
                    </div>
                </div>

                <!-- 付款饼图组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4">
                        <canvas id="paymentPieChart"></canvas>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">付款饼图</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">展示不同付款方式的占比情况</p>
                    </div>
                </div>

                <!-- 结算柱状图组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4">
                        <canvas id="settlementBarChart"></canvas>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">结算柱状图</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">展示各部门月度结算金额对比</p>
                    </div>
                </div>

                <!-- 地图组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4 flex items-center justify-center">
                        <img src="https://picsum.photos/id/101/800/400" alt="数据地图" class="w-full h-full object-cover rounded-md">
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">数据地图</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">展示各地区业务分布情况</p>
                    </div>
                </div>

                <!-- 销售趋势线图组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4">
                        <canvas id="salesLineChart"></canvas>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">销售趋势图</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">展示近12个月销售趋势变化</p>
                    </div>
                </div>

                <!-- KPI仪表盘组件 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-48 p-4 flex items-center justify-center">
                        <img src="https://picsum.photos/id/180/800/400" alt="KPI仪表盘" class="w-full h-full object-cover rounded-md">
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">KPI仪表盘</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">关键绩效指标实时监控</p>
                    </div>
                </div>
            </div>

            <!-- 分页控制 -->
            <div class="mt-8 flex justify-between items-center">
                <div class="text-sm text-text-tertiary">
                    显示 1 至 6 条，共 24 条
                </div>
                <div class="flex space-x-1">
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-left"></i>
                    </button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md bg-primary text-white">1</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">2</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">3</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">4</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 画布管理区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold mb-2">画布管理</h2>
                    <p class="text-text-tertiary">创建和管理不同风格的画布布局，如合同驾驶舱、资金驾驶舱等</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="搜索画布..." class="pl-10 pr-4 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full md:w-64">
                        <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary"></i>
                    </div>
                    <button class="bg-secondary hover:bg-secondary/90 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fa fa-plus mr-2"></i> 新建画布
                    </button>
                </div>
            </div>

            <!-- 画布卡片网格 -->
            <div class="canvas-grid">
                <!-- 合同驾驶舱画布 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-60 p-4 bg-neutral rounded-t-lg">
                        <div class="canvas-layout-2x2 h-full">
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">合同饼图</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">付款饼图</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">结算柱状图</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">销售趋势图</div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">合同驾驶舱</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">合同管理相关数据可视化</p>
                        <div class="mt-3 flex justify-between items-center">
                            <div class="flex items-center space-x-1 text-xs text-text-tertiary">
                                <i class="fa fa-user-o"></i>
                                <span>系统管理员</span>
                            </div>
                            <div class="flex items-center space-x-3 text-xs text-text-tertiary">
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-calendar-o"></i>
                                    <span>2025-07-20</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-cubes"></i>
                                    <span>4个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资金驾驶舱画布 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-60 p-4 bg-neutral rounded-t-lg">
                        <div class="canvas-layout-1x2 h-full">
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">KPI仪表盘</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">数据地图</div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">资金驾驶舱</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">资金管理相关数据可视化</p>
                        <div class="mt-3 flex justify-between items-center">
                            <div class="flex items-center space-x-1 text-xs text-text-tertiary">
                                <i class="fa fa-user-o"></i>
                                <span>财务经理</span>
                            </div>
                            <div class="flex items-center space-x-3 text-xs text-text-tertiary">
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-calendar-o"></i>
                                    <span>2025-07-18</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-cubes"></i>
                                    <span>2个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 销售分析画布 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-60 p-4 bg-neutral rounded-t-lg">
                        <div class="canvas-layout-2x1 h-full">
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">销售趋势图</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">结算柱状图</div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">销售分析画布</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">销售数据多维度分析</p>
                        <div class="mt-3 flex justify-between items-center">
                            <div class="flex items-center space-x-1 text-xs text-text-tertiary">
                                <i class="fa fa-user-o"></i>
                                <span>销售主管</span>
                            </div>
                            <div class="flex items-center space-x-3 text-xs text-text-tertiary">
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-calendar-o"></i>
                                    <span>2025-07-15</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-cubes"></i>
                                    <span>2个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 客户分析画布 -->
                <div class="bg-white border border-neutral-dark rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                    <div class="h-60 p-4 bg-neutral rounded-t-lg">
                        <div class="canvas-grid h-full">
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">客户分布</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">客户增长</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">客户留存</div>
                            </div>
                            <div class="bg-white rounded-md p-2 shadow-sm">
                                <div class="text-xs font-medium text-center">客户价值</div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 border-t border-neutral-dark">
                        <div class="flex justify-between items-center">
                            <h3 class="font-medium">客户分析画布</h3>
                            <div class="flex space-x-2">
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                                <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                    <i class="fa fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-text-tertiary mt-1">客户数据全方位分析</p>
                        <div class="mt-3 flex justify-between items-center">
                            <div class="flex items-center space-x-1 text-xs text-text-tertiary">
                                <i class="fa fa-user-o"></i>
                                <span>市场经理</span>
                            </div>
                            <div class="flex items-center space-x-3 text-xs text-text-tertiary">
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-calendar-o"></i>
                                    <span>2025-07-10</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fa fa-cubes"></i>
                                    <span>4个组件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控制 -->
            <div class="mt-8 flex justify-between items-center">
                <div class="text-sm text-text-tertiary">
                    显示 1 至 4 条，共 12 条
                </div>
                <div class="flex space-x-1">
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-left"></i>
                    </button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md bg-primary text-white">1</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">2</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">3</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 报表管理区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-bold mb-2">报表管理</h2>
                    <p class="text-text-tertiary">查看和管理已创建的报表，支持数据导出等功能</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="搜索报表..." class="pl-10 pr-4 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full md:w-64">
                        <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary"></i>
                    </div>
                    <div class="relative">
                        <select class="appearance-none pl-4 pr-10 py-2 border border-neutral-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200 w-full md:w-40 bg-white">
                            <option value="">所有类型</option>
                            <option value="daily">日报</option>
                            <option value="weekly">周报</option>
                            <option value="monthly">月报</option>
                            <option value="quarterly">季报</option>
                            <option value="annual">年报</option>
                        </select>
                        <i class="fa fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-text-tertiary pointer-events-none"></i>
                    </div>
                    <button class="bg-secondary hover:bg-secondary/90 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                        <i class="fa fa-plus mr-2"></i> 新建报表
                    </button>
                </div>
            </div>

            <!-- 报表列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-neutral-dark">
                    <thead class="bg-neutral">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">报表名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">画布</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">最近更新</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">所有者</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-text-tertiary uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-neutral-dark">
                        <tr class="hover:bg-neutral/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center mr-3">
                                        <i class="fa fa-file-text-o text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">月度合同分析报表</div>
                                        <div class="text-xs text-text-tertiary">2025年6月</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm">合同驾驶舱</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">月报</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                                2025-07-05 14:30
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1005/200/200" alt="用户头像">
                                    <span class="text-sm">系统管理员</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-pencil"></i>
                                    </button>
                                    <div class="relative inline-block text-left">
                                        <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                            <i class="fa fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-neutral/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-secondary/10 rounded-md flex items-center justify-center mr-3">
                                        <i class="fa fa-file-text-o text-secondary"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">季度资金分析报表</div>
                                        <div class="text-xs text-text-tertiary">2025年第二季度</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm">资金驾驶舱</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">季报</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                                2025-07-10 10:15
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1012/200/200" alt="用户头像">
                                    <span class="text-sm">财务经理</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-pencil"></i>
                                    </button>
                                    <div class="relative inline-block text-left">
                                        <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                            <i class="fa fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-neutral/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center mr-3">
                                        <i class="fa fa-file-text-o text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">区域销售分析报表</div>
                                        <div class="text-xs text-text-tertiary">2025年上半年</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm">销售分析画布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">半年报</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                                2025-07-15 09:45
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1025/200/200" alt="用户头像">
                                    <span class="text-sm">销售主管</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-pencil"></i>
                                    </button>
                                    <div class="relative inline-block text-left">
                                        <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                            <i class="fa fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-neutral/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-secondary/10 rounded-md flex items-center justify-center mr-3">
                                        <i class="fa fa-file-text-o text-secondary"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">客户分析报表</div>
                                        <div class="text-xs text-text-tertiary">2025年上半年</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm">客户分析画布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-indigo-100 text-indigo-800 rounded-full">半年报</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                                2025-07-20 16:20
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-6 w-6 rounded-full mr-2" src="https://picsum.photos/id/1066/200/200" alt="用户头像">
                                    <span class="text-sm">市场经理</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                        <i class="fa fa-pencil"></i>
                                    </button>
                                    <div class="relative inline-block text-left">
                                        <button class="p-1.5 rounded hover:bg-neutral transition-colors duration-200 text-text-secondary">
                                            <i class="fa fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页控制 -->
            <div class="mt-8 flex justify-between items-center">
                <div class="text-sm text-text-tertiary">
                    显示 1 至 4 条，共 16 条
                </div>
                <div class="flex space-x-1">
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-left"></i>
                    </button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md bg-primary text-white">1</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">2</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">3</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark hover:border-primary hover:text-primary transition-colors duration-200">4</button>
                    <button class="w-9 h-9 flex items-center justify-center rounded-md border border-neutral-dark text-text-tertiary hover:border-primary hover:text-primary transition-colors duration-200">
                        <i class="fa fa-angle-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-neutral-dark py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center mb-4 md:mb-0">
                    <i class="fa fa-bar-chart text-primary text-xl mr-2"></i>
                    <span class="text-sm font-medium">报表BI系统 © 2025</span>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-text-tertiary hover:text-primary transition-colors duration-200 text-sm">
                        <i class="fa fa-question-circle mr-1"></i> 帮助中心
                    </a>
                    <a href="#" class="text-text-tertiary hover:text-primary transition-colors duration-200 text-sm">
                        <i class="fa fa-file-text-o mr-1"></i> 文档
                    </a>
                    <a href="#" class="text-text-tertiary hover:text-primary transition-colors duration-200 text-sm">
                        <i class="fa fa-envelope-o mr-1"></i> 联系我们
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 合同饼图
            const contractCtx = document.getElementById('contractPieChart').getContext('2d');
            new Chart(contractCtx, {
                type: 'pie',
                data: {
                    labels: ['服务合同', '销售合同', '采购合同', '其他合同'],
                    datasets: [{
                        data: [35, 25, 30, 10],
                        backgroundColor: [
                            '#165DFF',
                            '#00B42A',
                            '#FF7D00',
                            '#86909C'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 6,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });

            // 付款饼图
            const paymentCtx = document.getElementById('paymentPieChart').getContext('2d');
            new Chart(paymentCtx, {
                type: 'doughnut',
                data: {
                    labels: ['在线支付', '银行转账', '现金支付', '其他方式'],
                    datasets: [{
                        data: [45, 30, 15, 10],
                        backgroundColor: [
                            '#165DFF',
                            '#00B42A',
                            '#FF7D00',
                            '#86909C'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 6,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    cutout: '70%'
                }
            });

            // 结算柱状图
            const settlementCtx = document.getElementById('settlementBarChart').getContext('2d');
            new Chart(settlementCtx, {
                type: 'bar',
                data: {
                    labels: ['财务部', '销售部', '市场部', '技术部', '人力资源部'],
                    datasets: [{
                        label: '结算金额(万元)',
                        data: [120, 85, 60, 45, 30],
                        backgroundColor: '#165DFF',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });

            // 销售趋势线图
            const salesCtx = document.getElementById('salesLineChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '销售额(万元)',
                        data: [65, 59, 80, 81, 56, 55, 72, 78, 85, 90, 92, 100],
                        borderColor: '#165DFF',
                        backgroundColor: 'rgba(22, 93, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });

            // 样式切换功能
            const styleButtons = document.querySelectorAll('[data-style]');
            styleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    styleButtons.forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('text-text-secondary', 'hover:bg-neutral');
                    });
                    
                    // 设置当前按钮为激活状态
                    this.classList.remove('text-text-secondary', 'hover:bg-neutral');
                    this.classList.add('bg-primary', 'text-white');
                    
                    // 应用样式更改
                    const style = this.getAttribute('data-style');
                    applyStyle(style);
                });
            });

            // 应用不同风格
            function applyStyle(style) {
                const root = document.documentElement;
                
                // 重置为默认样式
                root.style.setProperty('--primary-color', '#165DFF');
                root.style.setProperty('--secondary-color', '#00B42A');
                root.style.setProperty('--neutral-color', '#F5F7FA');
                root.style.setProperty('--text-primary', '#1D2129');
                root.style.setProperty('--text-secondary', '#4E5969');
                root.style.setProperty('--text-tertiary', '#86909C');
                
                // 根据选择的风格应用不同的颜色方案
                if (style === 'business') {
                    // 商务风格
                    root.style.setProperty('--primary-color', '#004080');
                    root.style.setProperty('--secondary-color', '#364D79');
                    root.style.setProperty('--neutral-color', '#F5F7FA');
                } else if (style === 'fresh') {
                    // 清新风格
                    root.style.setProperty('--primary-color', '#13C2C2');
                    root.style.setProperty('--secondary-color', '#52C41A');
                    root.style.setProperty('--neutral-color', '#F0F2F5');
                }
                
                // 这里可以添加更多样式更改逻辑
            }
        });
    </script>
</body>
</html>
    