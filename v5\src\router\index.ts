import { createRouter, createWebHistory } from 'vue-router';
import MainIndex from '@/views/MainIndex.vue';

const routes = [
  { path: '/', component: MainIndex },
  { path: '/dashboard/SystemOverview',
    component: () => import('@/views/dashboard/SystemOverview.vue')
  },
  { path: '/dashboard/ServiceMonitor',
    component: () => import('@/views/dashboard/ServiceMonitor.vue')
  },
  { path: '/dashboard/UserAnalysis',
    component: () => import('@/views/dashboard/UserAnalysis.vue')
  },
  { path: '/dashboard/OperationLogs',
    component: () => import('@/views/dashboard/OperationLogs.vue')
  },

  { path: '/datasource',
    component: () => import('@/views/datasource/DataSourceList.vue')
  },
  { path: '/datasource/add',
    component: () => import('@/views/datasource/DataSourceForm.vue')
  },
  { path: '/datasource/edit/:id',
    component: () => import('@/views/datasource/DataSourceForm.vue')
  },
  
  // 接口管理模块路由
  { path: '/interface/group',
    component: () => import('@/views/interface/InterfaceGroup.vue')
  },
  { path: '/interface/tag',
    component: () => import('@/views/interface/InterfaceTag.vue')
  },
  { path: '/interface/config',
    component: () => import('@/views/interface/InterfaceConfig.vue')
  },
  { path: '/interface/test',
    component: () => import('@/views/interface/InterfaceTest.vue')
  },

  { path: '/client/ClientManagement',
    component: () => import('@/views/client/ClientManagement.vue')
  },
  { path: '/client/PermissionManagement',
    component: () => import('@/views/client/PermissionManagement.vue')
  },
  { path: '/log/LogManagement',
    component: () => import('@/views/log/LogManagement.vue')
  },
  { path: '/security/SecurityConfig',
    component: () => import('@/views/security/SecurityConfig.vue')
  },
  { path: '/system/Permissions',
    component: () => import('@/views/system/Permissions.vue')
  },
  { path: '/system/Settings',
    component: () => import('@/views/system/Settings.vue')
  },
  { path: '/task/TaskMonitor',
    component: () => import('@/views/task/TaskMonitor.vue')
  },
  { path: '/task/ScheduledTasks',
    component: () => import('@/views/task/ScheduledTasks.vue')
  },
  { path: '/task/TaskManagement',
    component: () => import('@/views/task/TaskManagement.vue')
  },
  { path: '/task/TaskConfig',
    component: () => import('@/views/task/TaskConfig.vue')
  },
  { path: '/task/TaskHistory',
    component: () => import('@/views/task/TaskHistory.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
