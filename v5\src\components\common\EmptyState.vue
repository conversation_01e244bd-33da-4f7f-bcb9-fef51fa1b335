<template>
  <div class="empty-state">
    <div class="empty-image">
      <svg viewBox="0 0 200 200" class="empty-svg">
        <!-- 空文件夹图标 -->
        <g v-if="type === 'folder'">
          <path d="M40 60h120c4 0 8 4 8 8v80c0 4-4 8-8 8H40c-4 0-8-4-8-8V68c0-4 4-8 8-8z" 
                fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
          <path d="M40 60h40l8-12h72c4 0 8 4 8 8v4H40z" 
                fill="#fafafa" stroke="#d9d9d9" stroke-width="2"/>
        </g>
        
        <!-- 空数据表格图标 -->
        <g v-else-if="type === 'table'">
          <rect x="30" y="50" width="140" height="100" rx="4" 
                fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
          <line x1="30" y1="70" x2="170" y2="70" stroke="#d9d9d9" stroke-width="2"/>
          <line x1="70" y1="50" x2="70" y2="150" stroke="#d9d9d9" stroke-width="1"/>
          <line x1="130" y1="50" x2="130" y2="150" stroke="#d9d9d9" stroke-width="1"/>
          <!-- 空行 -->
          <circle cx="50" cy="90" r="3" fill="#e6e6e6"/>
          <circle cx="100" cy="90" r="3" fill="#e6e6e6"/>
          <circle cx="150" cy="90" r="3" fill="#e6e6e6"/>
          <circle cx="50" cy="110" r="3" fill="#e6e6e6"/>
          <circle cx="100" cy="110" r="3" fill="#e6e6e6"/>
          <circle cx="150" cy="110" r="3" fill="#e6e6e6"/>
        </g>
        
        <!-- 空搜索结果图标 -->
        <g v-else-if="type === 'search'">
          <circle cx="80" cy="80" r="30" fill="none" stroke="#d9d9d9" stroke-width="3"/>
          <line x1="105" y1="105" x2="130" y2="130" stroke="#d9d9d9" stroke-width="3"/>
          <line x1="60" y1="80" x2="100" y2="80" stroke="#e6e6e6" stroke-width="2"/>
          <line x1="65" y1="90" x2="95" y2="90" stroke="#e6e6e6" stroke-width="2"/>
        </g>
        
        <!-- 默认空状态图标 -->
        <g v-else>
          <rect x="60" y="40" width="80" height="100" rx="8" 
                fill="#f0f2f5" stroke="#d9d9d9" stroke-width="2"/>
          <rect x="70" y="60" width="60" height="4" rx="2" fill="#e6e6e6"/>
          <rect x="70" y="75" width="40" height="4" rx="2" fill="#e6e6e6"/>
          <rect x="70" y="90" width="50" height="4" rx="2" fill="#e6e6e6"/>
          <rect x="70" y="105" width="35" height="4" rx="2" fill="#e6e6e6"/>
        </g>
      </svg>
    </div>
    
    <div class="empty-content">
      <h3 class="empty-title">{{ title || getDefaultTitle() }}</h3>
      <p class="empty-description">{{ description || getDefaultDescription() }}</p>
      
      <div v-if="showAction" class="empty-actions">
        <el-button 
          v-if="actionText" 
          type="primary" 
          :icon="actionIcon"
          @click="handleAction"
        >
          {{ actionText }}
        </el-button>
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">


interface Props {
  type?: 'default' | 'folder' | 'table' | 'search';
  title?: string;
  description?: string;
  actionText?: string;
  actionIcon?: any;
  showAction?: boolean;
}

interface Emits {
  (e: 'action'): void;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  showAction: true
});

const emit = defineEmits<Emits>();

const getDefaultTitle = () => {
  const titles = {
    folder: '暂无文件',
    table: '暂无数据',
    search: '无搜索结果',
    default: '暂无内容'
  };
  return titles[props.type];
};

const getDefaultDescription = () => {
  const descriptions = {
    folder: '当前文件夹为空，您可以上传文件或创建新文件夹',
    table: '当前没有数据记录，您可以添加新的数据',
    search: '没有找到符合条件的结果，请尝试其他搜索条件',
    default: '当前没有可显示的内容'
  };
  return descriptions[props.type];
};

const handleAction = () => {
  emit('action');
};
</script>

<style scoped lang="scss">
@use '@/assets/styles/empty-state.scss' as *;
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  
  .empty-image {
    margin-bottom: 24px;
    
    .empty-svg {
      width: 120px;
      height: 120px;
      opacity: 0.8;
    }
  }
  
  .empty-content {
    max-width: 400px;
    
    .empty-title {
      font-size: 16px;
      font-weight: 500;
      color: #666;
      margin: 0 0 8px 0;
    }
    
    .empty-description {
      font-size: 14px;
      color: #999;
      line-height: 1.5;
      margin: 0 0 24px 0;
    }
    
    .empty-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .empty-state {
    padding: 40px 16px;
    min-height: 200px;
    
    .empty-image .empty-svg {
      width: 80px;
      height: 80px;
    }
    
    .empty-content {
      .empty-title {
        font-size: 14px;
      }
      
      .empty-description {
        font-size: 13px;
      }
    }
  }
}
</style>
