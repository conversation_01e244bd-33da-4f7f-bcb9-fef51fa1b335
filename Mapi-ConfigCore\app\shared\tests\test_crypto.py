"""
加密功能测试
"""

from app.shared.crypto_utils import encrypt_password, decrypt_password, is_password_encrypted

def test_password_encryption():
    """测试密码加密"""
    print("🔍 测试密码加密...")
    
    try:
        test_password = "test123456"
        
        # 加密
        encrypted = encrypt_password(test_password)
        print(f"✅ 密码加密成功: {test_password} -> {encrypted[:20]}...")
        
        # 解密
        decrypted = decrypt_password(encrypted)
        print(f"✅ 密码解密成功: {encrypted[:20]}... -> {decrypted}")
        
        # 验证一致性
        if test_password == decrypted:
            print("✅ 加密解密一致性验证通过")
            return True
        else:
            print("❌ 加密解密一致性验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 密码加密测试失败: {e}")
        return False

def test_encryption_detection():
    """测试加密检测"""
    print("🔍 测试加密检测...")
    
    try:
        plain_text = "plaintext123"
        encrypted_text = encrypt_password(plain_text)
        
        # 检测明文
        if not is_password_encrypted(plain_text):
            print("✅ 明文检测正确")
        else:
            print("❌ 明文检测错误")
            return False
        
        # 检测密文
        if is_password_encrypted(encrypted_text):
            print("✅ 密文检测正确")
        else:
            print("❌ 密文检测错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 加密检测测试失败: {e}")
        return False

def run_crypto_tests():
    """运行所有加密测试"""
    print("🚀 开始加密功能测试...\n")
    
    tests = [
        ("密码加密", test_password_encryption),
        ("加密检测", test_encryption_detection)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    return results
