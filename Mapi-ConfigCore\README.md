# Mapi-ConfigCore

Mapi配置管理核心服务 - FastAPI后端项目

## 快速开始

### 1. 安装依赖
```bash
poetry install
```

### 2. 初始化数据库
```bash
python init_database.py
```

### 3. 启动开发服务器
```bash
poetry run dev
```

或者
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问API文档
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 项目结构

```
app/
├── shared/          # 共享层
│   ├── database.py  # 数据库连接
│   ├── crypto_utils.py  # 加密工具
│   └── ...
├── config/          # 配置模块
│   └── datasource/  # 数据源管理
└── service/         # 服务模块
    └── ...
```

## 数据库

- **类型**: SQLite
- **文件**: `data/mapi_config.db`
- **表结构**: 参见 `init_database.py`

## 环境变量

创建 `.env` 文件：
```bash
cp .env.example .env
```

主要配置：
- `MAPI_CRYPTO_KEY`: AES加密密钥
- `DATABASE_URL`: 数据库连接字符串

## 开发说明

### 密码加密
- 使用AES加密存储数据源密码
- 支持加密/解密/显示密码功能
- 加密密钥通过环境变量配置

### 模块化部署
- **配置版**: 共享层 + 配置层
- **服务版**: 共享层 + 服务层  
- **全功能版**: 共享层 + 配置层 + 服务层
