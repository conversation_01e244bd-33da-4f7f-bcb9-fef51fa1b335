#!/usr/bin/env python3
"""
检查数据库中的实际数据
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.database import get_database
from sqlalchemy import text

def check_db_data():
    """检查数据库中的实际数据"""
    print("🔍 检查数据库中的接口配置数据...")

    db = next(get_database())

    try:
        # 直接使用SQL查询避免模型关系问题
        result = db.execute(text("""
            SELECT id, name, query_fields, required_fields, response_fields
            FROM interface_configs
            ORDER BY id
        """))

        configs = result.fetchall()

        for config in configs:
            print(f"\n📋 接口配置: {config.name} (ID={config.id})")
            print(f"   query_fields (原始): {repr(config.query_fields)}")
            print(f"   required_fields (原始): {repr(config.required_fields)}")
            print(f"   response_fields (原始): {repr(config.response_fields)}")

            # 尝试解析JSON
            import json
            try:
                if config.query_fields:
                    parsed_query = json.loads(config.query_fields)
                    print(f"   query_fields (解析后): {parsed_query}")
                else:
                    print(f"   query_fields (解析后): None")

                if config.required_fields:
                    parsed_required = json.loads(config.required_fields)
                    print(f"   required_fields (解析后): {parsed_required}")
                else:
                    print(f"   required_fields (解析后): None")

                if config.response_fields:
                    parsed_response = json.loads(config.response_fields)
                    print(f"   response_fields (解析后): {parsed_response}")
                else:
                    print(f"   response_fields (解析后): None")

            except Exception as e:
                print(f"   JSON解析错误: {e}")

    except Exception as e:
        print(f"❌ 数据库查询异常: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_db_data()
