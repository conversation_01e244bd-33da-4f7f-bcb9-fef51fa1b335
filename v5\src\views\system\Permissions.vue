<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><User /></el-icon>
      权限管理
    </h2>
    
    <div class="content-container">
      <el-tabs v-model="activeTab" class="permission-tabs">
        <!-- 角色管理 -->
        <el-tab-pane label="角色管理" name="roles">
          <div class="section-title">
            <el-icon><Avatar /></el-icon>
            系统角色
          </div>
          
          <div class="config-card">
            <div class="list-header">
              <span>角色列表</span>
              <el-button type="primary" @click="showAddRoleDialog">添加角色</el-button>
            </div>
            
            <el-table :data="roles" style="width: 100%">
              <el-table-column prop="name" label="角色名称" width="150"></el-table-column>
              <el-table-column prop="description" label="角色描述"></el-table-column>
              <el-table-column prop="userCount" label="用户数量" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                    {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="editRole(scope.row)">编辑</el-button>
                  <el-button size="small" @click="setPermissions(scope.row)">权限</el-button>
                  <el-button size="small" type="danger" @click="deleteRole(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <!-- 权限配置 -->
        <el-tab-pane label="权限配置" name="permissions">
          <div class="section-title">
            <el-icon><Lock /></el-icon>
            权限树
          </div>
          
          <div class="config-card">
            <el-tree
              :data="permissionTree"
              show-checkbox
              node-key="id"
              :default-expanded-keys="[1, 2, 3]"
              :default-checked-keys="checkedPermissions"
              :props="treeProps">
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.icon"><component :is="data.icon" /></el-icon>
                  <span>{{ data.label }}</span>
                  <el-tag v-if="data.type" size="small" :type="data.type === 'menu' ? 'primary' : 'success'">
                    {{ data.type === 'menu' ? '菜单' : '操作' }}
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </div>
        </el-tab-pane>
        
        <!-- 用户权限 -->
        <el-tab-pane label="用户权限" name="users">
          <div class="section-title">
            <el-icon><UserFilled /></el-icon>
            用户权限分配
          </div>
          
          <div class="config-card">
            <el-table :data="users" style="width: 100%">
              <el-table-column prop="username" label="用户名" width="150"></el-table-column>
              <el-table-column prop="email" label="邮箱" width="200"></el-table-column>
              <el-table-column prop="roles" label="角色" width="200">
                <template #default="scope">
                  <el-tag v-for="role in scope.row.roles" :key="role" size="small" style="margin-right: 5px;">
                    {{ role }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastLogin" label="最后登录" width="180"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                    {{ scope.row.status === 'active' ? '正常' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="editUserPermission(scope.row)">编辑权限</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { User, Avatar, Lock, UserFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('roles')

// 角色列表
const roles = ref([
  { id: 1, name: '超级管理员', description: '拥有所有权限', userCount: 1, status: 'active' },
  { id: 2, name: '系统管理员', description: '系统配置和用户管理', userCount: 3, status: 'active' },
  { id: 3, name: '接口管理员', description: '接口配置和测试', userCount: 5, status: 'active' },
  { id: 4, name: '普通用户', description: '基本查看权限', userCount: 20, status: 'active' }
])

// 权限树配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 已选中的权限
const checkedPermissions = ref([1, 2, 3, 11, 12, 21, 22])

// 权限树数据
const permissionTree = ref([
  {
    id: 1,
    label: '仪表盘',
    icon: 'House',
    type: 'menu',
    children: [
      { id: 11, label: '运维监控', type: 'action' },
      { id: 12, label: '系统概况', type: 'action' }
    ]
  },
  {
    id: 2,
    label: '数据源管理',
    icon: 'DataLine',
    type: 'menu',
    children: [
      { id: 21, label: '数据源配置', type: 'action' },
      { id: 22, label: '连接测试', type: 'action' }
    ]
  },
  {
    id: 3,
    label: '接口管理',
    icon: 'SetUp',
    type: 'menu',
    children: [
      { id: 31, label: '接口配置', type: 'action' },
      { id: 32, label: '接口分组', type: 'action' },
      { id: 33, label: '接口测试', type: 'action' }
    ]
  },
  {
    id: 4,
    label: '安全管理',
    icon: 'Lock',
    type: 'menu',
    children: [
      { id: 41, label: 'IP访问控制', type: 'action' },
      { id: 42, label: 'SQL白名单', type: 'action' },
      { id: 43, label: '流量控制', type: 'action' }
    ]
  },
  {
    id: 5,
    label: '系统管理',
    icon: 'Setting',
    type: 'menu',
    children: [
      { id: 51, label: '用户管理', type: 'action' },
      { id: 52, label: '权限管理', type: 'action' },
      { id: 53, label: '系统设置', type: 'action' }
    ]
  }
])

// 用户列表
const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    roles: ['超级管理员'],
    lastLogin: '2023-12-15 14:30:25',
    status: 'active'
  },
  {
    id: 2,
    username: 'manager',
    email: '<EMAIL>',
    roles: ['系统管理员'],
    lastLogin: '2023-12-15 13:45:12',
    status: 'active'
  },
  {
    id: 3,
    username: 'developer',
    email: '<EMAIL>',
    roles: ['接口管理员'],
    lastLogin: '2023-12-15 12:20:33',
    status: 'active'
  }
])

// 显示添加角色对话框
const showAddRoleDialog = () => {
  ElMessage.info('添加角色功能开发中...')
}

// 编辑角色
const editRole = (role: any) => {
  ElMessage.info('编辑角色功能开发中...')
}

// 设置权限
const setPermissions = (role: any) => {
  ElMessage.info('设置权限功能开发中...')
}

// 删除角色
const deleteRole = (index: number) => {
  roles.value.splice(index, 1)
  ElMessage.success('角色删除成功')
}

// 编辑用户权限
const editUserPermission = (user: any) => {
  ElMessage.info('编辑用户权限功能开发中...')
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary);
  padding-left: 12px;
}

.config-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-tabs {
  margin-bottom: 20px;
}
</style>
