/**
 * 抽屉刷新机制 Composable
 * 统一处理抽屉关闭后的数据刷新逻辑
 */
import { onMounted, onUnmounted } from 'vue';

interface DrawerRefreshOptions {
  /** 刷新函数 */
  refreshFunction: () => void | Promise<void>;
  /** 匹配的抽屉标题关键词列表 */
  titleKeywords: string[];
  /** 全局刷新方法名称（可选，用作备用方案） */
  globalMethodName?: string;
}

/**
 * 使用抽屉刷新机制
 * @param options 配置选项
 */
export function useDrawerRefresh(options: DrawerRefreshOptions) {
  const { refreshFunction, titleKeywords, globalMethodName } = options;

  // 处理抽屉关闭事件
  const handleDrawerClosed = (event: CustomEvent) => {
    const { title } = event.detail;
    if (title && titleKeywords.some(keyword => title.includes(keyword))) {
      refreshFunction();
    }
  };

  // 注册事件监听和全局方法
  const setupListeners = () => {
    // 注册事件监听器
    window.addEventListener('drawerClosed', handleDrawerClosed as EventListener);

    // 如果在iframe中，也监听父窗口的事件
    if (window.parent && window.parent !== window) {
      window.parent.addEventListener('drawerClosed', handleDrawerClosed as EventListener);
    }

    // 暴露全局刷新方法作为备用方案
    if (globalMethodName) {
      (window as any)[globalMethodName] = refreshFunction;
      if (window.parent && window.parent !== window) {
        (window.parent as any)[globalMethodName] = refreshFunction;
      }
    }
  };

  // 清理事件监听和全局方法
  const cleanupListeners = () => {
    // 清理事件监听器
    window.removeEventListener('drawerClosed', handleDrawerClosed as EventListener);

    // 清理父窗口的事件监听
    if (window.parent && window.parent !== window) {
      window.parent.removeEventListener('drawerClosed', handleDrawerClosed as EventListener);
    }

    // 清理全局方法
    if (globalMethodName) {
      delete (window as any)[globalMethodName];
      if (window.parent && window.parent !== window) {
        delete (window.parent as any)[globalMethodName];
      }
    }
  };

  // 在组件挂载时设置监听器
  onMounted(() => {
    setupListeners();
  });

  // 在组件卸载时清理监听器
  onUnmounted(() => {
    cleanupListeners();
  });

  return {
    setupListeners,
    cleanupListeners,
    handleDrawerClosed
  };
}

/**
 * 预定义的常用刷新配置
 */
export const DRAWER_REFRESH_CONFIGS = {
  /** 数据源管理 */
  DATA_SOURCE: {
    titleKeywords: ['数据源'],
    globalMethodName: 'refreshDataSourceList'
  },
  /** 接口分组管理 */
  INTERFACE_GROUP: {
    titleKeywords: ['分组', '接口分组'],
    globalMethodName: 'refreshInterfaceGroupList'
  },
  /** 接口标签管理 */
  INTERFACE_TAG: {
    titleKeywords: ['标签', '接口标签'],
    globalMethodName: 'refreshInterfaceTagList'
  },
  /** 接口配置管理 */
  INTERFACE_CONFIG: {
    titleKeywords: ['接口配置', '接口'],
    globalMethodName: 'refreshInterfaceConfigList'
  }
} as const;
