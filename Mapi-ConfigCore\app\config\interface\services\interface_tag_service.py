"""
接口标签业务逻辑层
处理接口标签相关的业务逻辑
"""

import math
from typing import Optional
from sqlalchemy.orm import Session
from app.config.interface.repositories.interface_tag_repository import InterfaceTagRepository
from app.config.interface.schemas.interface_tag_schema import (
    InterfaceTagCreate,
    InterfaceTagUpdate,
    InterfaceTagResponse,
    InterfaceTagListResponse
)
from app.shared.core.exception_handler import BusinessException, TechnicalException
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil


class InterfaceTagService:
    """接口标签业务逻辑类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repository = InterfaceTagRepository(db)
        LogUtil.debug("接口标签服务初始化", service="InterfaceTagService")
    
    def get_interface_tags(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None
    ) -> InterfaceTagListResponse:
        """
        获取接口标签列表
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词
            
        Returns:
            接口标签列表响应
        """
        try:
            LogUtil.debug("开始获取接口标签列表", 
                         operation="get_interface_tags",
                         page=page, 
                         size=size, 
                         search=search)
            
            # 参数验证
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )
            
            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )
            
            # 获取数据
            items, total = self.repository.get_list(page, size, search)
            
            # 转换为响应格式
            tags = []
            for item in items:
                # TODO: 计算使用该标签的接口数量
                tag_response = InterfaceTagResponse.from_orm(item)
                tag_response.interface_count = 0  # 暂时设为0，后续实现
                tags.append(tag_response)
            
            # 计算总页数
            pages = math.ceil(total / size) if total > 0 else 0
            
            LogUtil.info("接口标签列表获取成功", 
                        operation="get_interface_tags",
                        total_count=total,
                        returned_count=len(tags),
                        page=page,
                        size=size)
            
            return InterfaceTagListResponse(
                items=tags,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_tags",
                    "error": str(e),
                    "params": {
                        "page": page, 
                        "size": size, 
                        "search": search
                    }
                }
            )
    
    def get_interface_tag(self, tag_id: int) -> InterfaceTagResponse:
        """
        获取单个接口标签
        
        Args:
            tag_id: 接口标签ID
            
        Returns:
            接口标签响应
        """
        try:
            LogUtil.debug("开始获取接口标签详情", 
                         operation="get_interface_tag",
                         tag_id=tag_id)
            
            tag = self.repository.get_by_id(tag_id)
            if not tag:
                raise BusinessException(
                    user_message="接口标签不存在",
                    user_detail={"tag_id": tag_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 转换为响应格式
            tag_response = InterfaceTagResponse.from_orm(tag)
            tag_response.interface_count = 0  # TODO: 计算使用该标签的接口数量
            
            LogUtil.info("接口标签详情获取成功", 
                        operation="get_interface_tag",
                        tag_id=tag_id,
                        name=tag.name)
            
            return tag_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_tag",
                    "error": str(e),
                    "tag_id": tag_id
                }
            )
    
    def create_interface_tag(self, tag_data: InterfaceTagCreate) -> InterfaceTagResponse:
        """
        创建接口标签
        
        Args:
            tag_data: 接口标签创建数据
            
        Returns:
            创建的接口标签响应
        """
        try:
            LogUtil.debug("开始创建接口标签", 
                         operation="create_interface_tag",
                         name=tag_data.name,
                         color=tag_data.color)
            
            # 检查名称是否已存在
            if self.repository.check_name_exists(tag_data.name):
                raise BusinessException(
                    user_message="接口标签名称已存在",
                    user_detail={
                        "name": tag_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 创建接口标签
            tag = self.repository.create(tag_data)
            
            # 转换为响应格式
            tag_response = InterfaceTagResponse.from_orm(tag)
            tag_response.interface_count = 0
            
            LogUtil.info("接口标签创建成功", 
                        operation="create_interface_tag",
                        tag_id=tag.id,
                        name=tag.name,
                        color=tag.color)
            
            return tag_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "create_interface_tag",
                    "error": str(e),
                    "tag_data": tag_data.dict()
                }
            )
    
    def update_interface_tag(
        self, 
        tag_id: int, 
        tag_data: InterfaceTagUpdate
    ) -> InterfaceTagResponse:
        """
        更新接口标签
        
        Args:
            tag_id: 接口标签ID
            tag_data: 更新数据
            
        Returns:
            更新后的接口标签响应
        """
        try:
            LogUtil.debug("开始更新接口标签", 
                         operation="update_interface_tag",
                         tag_id=tag_id)
            
            # 检查接口标签是否存在
            existing_tag = self.repository.get_by_id(tag_id)
            if not existing_tag:
                raise BusinessException(
                    user_message="接口标签不存在",
                    user_detail={"tag_id": tag_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 检查名称冲突
            if tag_data.name and self.repository.check_name_exists(tag_data.name, tag_id):
                raise BusinessException(
                    user_message="接口标签名称已存在",
                    user_detail={
                        "name": tag_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 更新接口标签
            updated_tag = self.repository.update(tag_id, tag_data)
            
            # 转换为响应格式
            tag_response = InterfaceTagResponse.from_orm(updated_tag)
            tag_response.interface_count = 0  # TODO: 计算使用该标签的接口数量
            
            LogUtil.info("接口标签更新成功", 
                        operation="update_interface_tag",
                        tag_id=tag_id,
                        name=updated_tag.name)
            
            return tag_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "update_interface_tag",
                    "error": str(e),
                    "tag_id": tag_id,
                    "tag_data": tag_data.dict()
                }
            )
    
    def delete_interface_tag(self, tag_id: int) -> dict:
        """
        删除接口标签
        
        Args:
            tag_id: 接口标签ID
            
        Returns:
            删除结果
        """
        try:
            LogUtil.debug("开始删除接口标签", 
                         operation="delete_interface_tag",
                         tag_id=tag_id)
            
            # 检查接口标签是否存在
            existing_tag = self.repository.get_by_id(tag_id)
            if not existing_tag:
                raise BusinessException(
                    user_message="接口标签不存在",
                    user_detail={"tag_id": tag_id},
                    error_type=ErrorType.资源未找到
                )
            
            # TODO: 检查是否有关联的接口配置
            # 如果有关联的接口配置，应该提示用户先解除关联
            
            # 删除接口标签
            success = self.repository.delete(tag_id)
            
            if success:
                LogUtil.info("接口标签删除成功", 
                            operation="delete_interface_tag",
                            tag_id=tag_id,
                            name=existing_tag.name)
                
                return {"message": "接口标签删除成功"}
            else:
                raise TechnicalException(
                    error_type=ErrorType.操作失败,
                    developer_detail={
                        "operation": "delete_interface_tag",
                        "tag_id": tag_id,
                        "error": "删除操作返回失败"
                    }
                )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "delete_interface_tag",
                    "error": str(e),
                    "tag_id": tag_id
                }
            )
    
    def get_all_enabled_tags(self) -> list[InterfaceTagResponse]:
        """
        获取所有启用的接口标签（用于下拉选择）
        
        Returns:
            启用的接口标签列表
        """
        try:
            LogUtil.debug("获取所有启用的接口标签", operation="get_all_enabled_tags")
            
            tags = self.repository.get_all_enabled()
            
            # 转换为响应格式
            tag_responses = []
            for tag in tags:
                tag_response = InterfaceTagResponse.from_orm(tag)
                tag_response.interface_count = 0  # TODO: 计算使用该标签的接口数量
                tag_responses.append(tag_response)
            
            LogUtil.info("启用的接口标签获取成功", 
                        operation="get_all_enabled_tags",
                        count=len(tag_responses))
            
            return tag_responses
            
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_all_enabled_tags",
                    "error": str(e)
                }
            )
