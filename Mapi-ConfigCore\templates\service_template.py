"""
服务层模板 - Service Layer Template
用于新模块开发的标准模板

异常处理规则：
1. BusinessException - 业务异常，给前端用户看，记录到biz日志
2. TechnicalException - 技术异常，给开发者看，记录到tech日志
3. 标准异常处理模式必须严格遵循

日志规范：
1. 使用LogUtil记录日志，包含6个独立文件：
   - debug_*.log - 调试日志，只记录DEBUG级别
   - info_*.log - 信息日志，只记录INFO级别
   - warning_*.log - 警告日志，只记录WARNING级别
   - error_*.log - 错误日志，只记录ERROR级别
   - biz_*.log - 业务异常日志，由异常处理系统调用
   - tech_*.log - 技术异常日志，由异常处理系统调用
2. 日志格式包含：时间、级别、文件名、行号、方法名、消息和结构化数据
3. 每条日志间有分隔线，便于阅读

代码规范：
1. 测试文件应放在tests目录下，不要放在项目根目录
2. 完成调试后删除所有临时测试文件，保持代码库整洁

作者：系统生成
日期：2025-07-21
"""

from typing import Optional, List
from app.shared.core.exception_handler import BusinessException, TechnicalException
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil


class TemplateService:
    """
    模板服务类
    所有新的服务类都应该遵循这个模板的异常处理模式
    """
    
    def __init__(self, repository):
        """
        初始化服务
        
        Args:
            repository: 数据访问层实例
        """
        self.repository = repository
    
    def get_list(self, page: int = 1, size: int = 10, search: Optional[str] = None) -> dict:
        """
        获取列表数据 - 标准模板方法
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词
            
        Returns:
            列表响应数据
            
        Raises:
            BusinessException: 参数验证失败等业务异常
            TechnicalException: 数据库错误等技术异常
        """
        try:
            # 记录调试日志
            LogUtil.debug("开始获取列表数据",
                         operation="get_list",
                         page=page,
                         size=size,
                         search=search)

            # 1. 参数验证 - 业务规则验证，直接抛出BusinessException
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )

            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )

            # 2. 业务逻辑处理
            items, total = self.repository.get_list(page, size, search)

            # 记录信息日志
            LogUtil.info("列表数据获取成功",
                        operation="get_list",
                        total_count=total,
                        returned_count=len(items),
                        page=page,
                        size=size)

            # 3. 数据转换和返回
            return {
                "items": items,
                "total": total,
                "page": page,
                "size": size
            }
            
        except BusinessException:
            # 业务异常直接抛出，不做任何处理
            # 会被记录到biz日志，显示给前端用户
            raise
        except Exception as e:
            # 捕获所有未知异常，包装为TechnicalException
            # 会被记录到tech日志，供开发者查看
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_list",
                    "error": str(e),
                    "params": {"page": page, "size": size, "search": search}
                }
            )
    
    def get_by_id(self, item_id: int) -> dict:
        """
        根据ID获取单个数据 - 标准模板方法
        
        Args:
            item_id: 数据ID
            
        Returns:
            数据响应
            
        Raises:
            BusinessException: 资源不存在等业务异常
            TechnicalException: 数据库错误等技术异常
        """
        try:
            # 1. 获取数据
            item = self.repository.get_by_id(item_id)
            
            # 2. 业务验证
            if not item:
                raise BusinessException(
                    user_message="数据不存在",
                    user_detail={"id": item_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 3. 返回数据
            return item
            
        except BusinessException:
            # 业务异常直接抛出
            raise
        except Exception as e:
            # 技术异常包装后抛出
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_by_id",
                    "error": str(e),
                    "item_id": item_id
                }
            )
    
    def create(self, create_data: dict) -> dict:
        """
        创建数据 - 标准模板方法
        
        Args:
            create_data: 创建数据
            
        Returns:
            创建的数据响应
            
        Raises:
            BusinessException: 数据验证失败、资源冲突等业务异常
            TechnicalException: 数据库错误等技术异常
        """
        try:
            # 1. 业务验证
            if not create_data.get("name"):
                raise BusinessException(
                    user_message="名称不能为空",
                    user_detail={"name": create_data.get("name")},
                    error_type=ErrorType.验证错误
                )
            
            # 2. 检查重复
            if self.repository.check_name_exists(create_data["name"]):
                raise BusinessException(
                    user_message="名称已存在",
                    user_detail={
                        "name": create_data["name"],
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 3. 创建数据
            item = self.repository.create(create_data)
            
            # 4. 返回结果
            return item
            
        except BusinessException:
            # 业务异常直接抛出
            raise
        except Exception as e:
            # 技术异常包装后抛出
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "create",
                    "error": str(e),
                    "create_data": create_data
                }
            )
    
    def update(self, item_id: int, update_data: dict) -> dict:
        """
        更新数据 - 标准模板方法
        
        Args:
            item_id: 数据ID
            update_data: 更新数据
            
        Returns:
            更新后的数据响应
            
        Raises:
            BusinessException: 资源不存在、数据验证失败等业务异常
            TechnicalException: 数据库错误等技术异常
        """
        try:
            # 1. 检查数据是否存在
            existing_item = self.repository.get_by_id(item_id)
            if not existing_item:
                raise BusinessException(
                    user_message="数据不存在",
                    user_detail={"id": item_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 2. 业务验证（如果更新名称，检查是否重复）
            if update_data.get("name") and self.repository.check_name_exists(update_data["name"], item_id):
                raise BusinessException(
                    user_message="名称已存在",
                    user_detail={
                        "name": update_data["name"],
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 3. 更新数据
            updated_item = self.repository.update(item_id, update_data)
            
            # 4. 返回结果
            return updated_item
            
        except BusinessException:
            # 业务异常直接抛出
            raise
        except Exception as e:
            # 技术异常包装后抛出
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "update",
                    "error": str(e),
                    "item_id": item_id,
                    "update_data": update_data
                }
            )
    
    def delete(self, item_id: int) -> bool:
        """
        删除数据 - 标准模板方法
        
        Args:
            item_id: 数据ID
            
        Returns:
            是否删除成功
            
        Raises:
            BusinessException: 资源不存在等业务异常
            TechnicalException: 数据库错误等技术异常
        """
        try:
            # 1. 检查数据是否存在
            existing_item = self.repository.get_by_id(item_id)
            if not existing_item:
                raise BusinessException(
                    user_message="数据不存在",
                    user_detail={"id": item_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 2. 执行删除
            success = self.repository.delete(item_id)
            
            # 3. 验证删除结果
            if not success:
                raise BusinessException(
                    user_message="删除失败",
                    user_detail={
                        "id": item_id,
                        "error": "删除操作失败"
                    },
                    error_type=ErrorType.操作失败
                )
            
            return True
            
        except BusinessException:
            # 业务异常直接抛出
            raise
        except Exception as e:
            # 技术异常包装后抛出
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "delete",
                    "error": str(e),
                    "item_id": item_id
                }
            )


# ==================== 异常处理规则说明 ====================
"""
异常处理标准规则：

1. 所有服务方法必须使用标准的try-except模式
2. 业务异常（BusinessException）直接抛出，不做处理
3. 技术异常（Exception）必须包装为TechnicalException后抛出
4. 不允许直接返回错误响应，必须抛出异常
5. 异常信息要详细，包含操作名称、错误信息、相关参数

标准模式：
```python
try:
    # 业务逻辑
    pass
except BusinessException:
    raise
except Exception as e:
    raise TechnicalException(
        error_type=ErrorType.相应类型,
        developer_detail={
            "operation": "方法名",
            "error": str(e),
            # 其他参数
        }
    )
```

常用ErrorType：
- ErrorType.验证错误 - 参数验证失败
- ErrorType.资源未找到 - 数据不存在
- ErrorType.资源冲突 - 数据重复
- ErrorType.数据库错误 - 数据库操作失败
- ErrorType.连接错误 - 网络连接失败
- ErrorType.加密错误 - 加密解密失败
- ErrorType.操作失败 - 业务操作失败

日志使用规范：

1. 调试日志（LogUtil.debug）：
   - 用于记录详细的执行过程
   - 包含方法参数、中间变量等调试信息
   - 示例：LogUtil.debug("开始处理请求", operation="create", params=data)

2. 信息日志（LogUtil.info）：
   - 用于记录重要的业务操作
   - 包含操作结果、统计信息等
   - 示例：LogUtil.info("数据创建成功", operation="create", id=new_id)

3. 警告日志（LogUtil.warning）：
   - 用于记录潜在问题或异常情况
   - 包含警告原因、影响范围等
   - 示例：LogUtil.warning("查询结果为空", operation="search", query=search_term)

4. 错误日志（LogUtil.error）：
   - 用于记录系统错误或异常
   - 包含错误详情、影响范围等
   - 示例：LogUtil.error("数据处理失败", operation="process", error=str(e))

注意：
- biz_info和tech_error由异常处理系统自动调用，不需要手动使用
- 所有日志都会自动记录调用者信息（文件名、方法名、行号）
- 日志参数使用键值对形式，便于后续分析和查询
"""
