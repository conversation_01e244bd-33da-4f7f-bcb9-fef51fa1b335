/**
 * 空状态样式
 * 全局空状态组件的样式定义
 */

// 空状态基础样式
.empty-state {
  --empty-color-primary: #666;
  --empty-color-secondary: #999;
  --empty-color-border: #d9d9d9;
  --empty-color-bg: #f0f2f5;
  --empty-color-bg-light: #fafafa;
  --empty-color-icon: #e6e6e6;
  
  // 动画效果
  .empty-image {
    animation: empty-float 3s ease-in-out infinite;
  }
  
  .empty-content {
    animation: empty-fade-in 0.5s ease-out;
  }
}

// 浮动动画
@keyframes empty-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

// 淡入动画
@keyframes empty-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 主题变体
.empty-state {
  // 暗色主题
  &.dark {
    --empty-color-primary: #ccc;
    --empty-color-secondary: #999;
    --empty-color-border: #555;
    --empty-color-bg: #333;
    --empty-color-bg-light: #444;
    --empty-color-icon: #666;
  }
  
  // 小尺寸变体
  &.small {
    padding: 40px 16px;
    min-height: 200px;
    
    .empty-image .empty-svg {
      width: 80px;
      height: 80px;
    }
    
    .empty-content {
      .empty-title {
        font-size: 14px;
      }
      
      .empty-description {
        font-size: 12px;
      }
    }
  }
  
  // 大尺寸变体
  &.large {
    padding: 80px 20px;
    min-height: 400px;
    
    .empty-image .empty-svg {
      width: 160px;
      height: 160px;
    }
    
    .empty-content {
      .empty-title {
        font-size: 18px;
      }
      
      .empty-description {
        font-size: 16px;
      }
    }
  }
}

// 特定类型的样式增强
.empty-state {
  // 表格空状态
  &.type-table {
    .empty-svg {
      filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
    }
  }
  
  // 搜索空状态
  &.type-search {
    .empty-svg {
      opacity: 0.6;
    }
    
    .empty-title {
      color: var(--el-color-warning);
    }
  }
  
  // 文件夹空状态
  &.type-folder {
    .empty-svg {
      filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
    }
  }
}

// 响应式断点
@media (max-width: 768px) {
  .empty-state {
    padding: 30px 12px;
    min-height: 180px;
    
    .empty-image .empty-svg {
      width: 60px;
      height: 60px;
    }
    
    .empty-content {
      .empty-title {
        font-size: 13px;
      }
      
      .empty-description {
        font-size: 11px;
        line-height: 1.4;
      }
      
      .empty-actions {
        flex-direction: column;
        align-items: center;
        
        .el-button {
          width: 120px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .empty-state {
    padding: 20px 8px;
    min-height: 150px;
    
    .empty-image .empty-svg {
      width: 50px;
      height: 50px;
    }
    
    .empty-content {
      .empty-title {
        font-size: 12px;
        margin-bottom: 4px;
      }
      
      .empty-description {
        font-size: 10px;
        margin-bottom: 16px;
      }
    }
  }
}

// 与Element Plus表格的集成样式
.el-table__empty-block {
  .empty-state {
    padding: 40px 20px;
    min-height: 200px;
  }
}

// 与Element Plus卡片的集成样式
.el-card__body {
  .empty-state {
    margin: -20px;
    padding: 60px 20px;
  }
}
