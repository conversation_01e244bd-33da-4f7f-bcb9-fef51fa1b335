"""
数据源数据访问层
负责数据库的CRUD操作
"""

from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.config.datasource.models.data_source_model import DataSourceModel
from app.config.datasource.schemas.data_source_schema import DataSourceCreate, DataSourceUpdate
from app.shared.crypto_utils import encrypt_password

class DataSourceRepository:
    """数据源数据访问类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_by_id(self, data_source_id: int) -> Optional[DataSourceModel]:
        """根据ID获取数据源"""
        return self.db.query(DataSourceModel).filter(DataSourceModel.id == data_source_id).first()
    
    def get_by_name(self, name: str) -> Optional[DataSourceModel]:
        """根据名称获取数据源"""
        return self.db.query(DataSourceModel).filter(DataSourceModel.name == name).first()
    
    def get_list(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None,
        status: Optional[str] = None,
        db_type: Optional[str] = None
    ) -> Tuple[List[DataSourceModel], int]:
        """
        获取数据源列表（分页）
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词（名称、描述、主机地址）
            status: 状态过滤
            db_type: 数据库类型过滤
            
        Returns:
            (数据源列表, 总数量)
        """
        query = self.db.query(DataSourceModel)
        
        # 搜索过滤
        if search:
            search_filter = or_(
                DataSourceModel.name.ilike(f"%{search}%"),
                DataSourceModel.description.ilike(f"%{search}%"),
                DataSourceModel.host.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 状态过滤
        if status:
            query = query.filter(DataSourceModel.status == status)
        
        # 数据库类型过滤
        if db_type:
            query = query.filter(DataSourceModel.db_type == db_type)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        offset = (page - 1) * size
        items = query.order_by(DataSourceModel.created_at.desc()).offset(offset).limit(size).all()
        
        return items, total
    
    def create(self, data_source_data: DataSourceCreate) -> DataSourceModel:
        """
        创建数据源
        
        Args:
            data_source_data: 数据源创建数据
            
        Returns:
            创建的数据源模型
        """
        # 加密密码
        encrypted_password = encrypt_password(data_source_data.password)
        
        # 创建数据源模型
        db_data_source = DataSourceModel(
            name=data_source_data.name,
            description=data_source_data.description,
            db_type=data_source_data.db_type.value,
            host=data_source_data.host,
            port=data_source_data.port,
            database=data_source_data.database,
            username=data_source_data.username,
            password=encrypted_password,
            max_connections=data_source_data.max_connections,
            connection_timeout=data_source_data.connection_timeout,
            refresh_time=data_source_data.refresh_time,
            status=data_source_data.status.value,
            created_by=data_source_data.created_by
        )
        
        self.db.add(db_data_source)
        self.db.commit()
        self.db.refresh(db_data_source)
        
        return db_data_source
    
    def update(self, data_source_id: int, data_source_data: DataSourceUpdate) -> Optional[DataSourceModel]:
        """
        更新数据源
        
        Args:
            data_source_id: 数据源ID
            data_source_data: 更新数据
            
        Returns:
            更新后的数据源模型
        """
        db_data_source = self.get_by_id(data_source_id)
        if not db_data_source:
            return None
        
        # 更新字段
        update_data = data_source_data.dict(exclude_unset=True)

        # 智能处理密码字段
        if 'password' in update_data:
            new_password = update_data['password']
            current_encrypted_password = db_data_source.password

            # 比对密码是否发生变化
            if new_password == current_encrypted_password:
                # 密码未修改，保持原密码不变
                update_data['password'] = current_encrypted_password
            else:
                # 密码已修改，重新加密保存
                update_data['password'] = encrypt_password(new_password)
        
        # 处理枚举类型
        if 'db_type' in update_data and update_data['db_type'] is not None:
            # 如果是枚举对象，获取其值；如果已经是字符串，直接使用
            if hasattr(update_data['db_type'], 'value'):
                update_data['db_type'] = update_data['db_type'].value
        if 'status' in update_data and update_data['status'] is not None:
            # 如果是枚举对象，获取其值；如果已经是字符串，直接使用
            if hasattr(update_data['status'], 'value'):
                update_data['status'] = update_data['status'].value
        
        # 应用更新
        try:
            for field, value in update_data.items():
                setattr(db_data_source, field, value)

            self.db.commit()
            self.db.refresh(db_data_source)

            return db_data_source
        except Exception as e:
            self.db.rollback()
            raise
    
    def delete(self, data_source_id: int) -> bool:
        """
        删除数据源
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            是否删除成功
        """
        db_data_source = self.get_by_id(data_source_id)
        if not db_data_source:
            return False
        
        self.db.delete(db_data_source)
        self.db.commit()
        
        return True
    

    
    def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查名称是否已存在
        
        Args:
            name: 数据源名称
            exclude_id: 排除的ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = self.db.query(DataSourceModel).filter(DataSourceModel.name == name)
        
        if exclude_id:
            query = query.filter(DataSourceModel.id != exclude_id)
        
        return query.first() is not None
