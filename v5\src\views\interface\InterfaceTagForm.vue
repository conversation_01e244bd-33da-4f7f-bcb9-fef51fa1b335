<template>
  <div class="interface-tag-form drawer-form-content">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      label-position="left"
    >
      <el-form-item label="标签名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入标签名称"
          maxlength="50"
          show-word-limit
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="标签颜色" prop="color">
        <div class="color-selector">
          <el-color-picker v-model="formData.color" :disabled="loading" />
          <span class="color-value">{{ formData.color }}</span>
        </div>
        <div class="color-presets">
          <div class="preset-label">预设颜色：</div>
          <div class="preset-colors">
            <div
              v-for="preset in colorPresets"
              :key="preset.value"
              class="preset-color"
              :style="{ backgroundColor: preset.value }"
              :title="preset.name"
              @click="selectPresetColor(preset.value)"
            ></div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="isEnabled">
        <el-radio-group v-model="formData.isEnabled" :disabled="loading">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入标签描述（可选）"
          maxlength="200"
          show-word-limit
          :disabled="loading"
        />
      </el-form-item>
    </el-form>
  </div>

  <!-- 底部按钮已抽象到MainIndex中统一管理 -->
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

import interfaceTagService from '@/services/interface-tag.service';
import type { InterfaceTag, InterfaceTagRequest } from '@/types/interface-tag';
import { TAG_COLOR_PRESETS } from '@/types/interface-tag';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { PageRefresh } from '@/utils/pageRefreshUtil';

// 使用全局抽屉状态
const globalDrawerStore = useGlobalDrawerStore();

// 表单引用
const formRef = ref();
const loading = ref(false);

// 颜色预设
const colorPresets = TAG_COLOR_PRESETS;

// 计算属性
const isEdit = computed(() => globalDrawerStore.props.isEdit || false);
const editData = computed(() => globalDrawerStore.props.editData || null);

// 取消操作
const handleCancel = () => {
  globalDrawerStore.closeDrawer();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value && editData.value) {
      await interfaceTagService.updateInterfaceTag(editData.value.id, formData.value);
      ElMessage.success('标签更新成功');

      // 修改操作：保持当前页刷新
      PageRefresh.interfaceTag.afterEdit();
    } else {
      await interfaceTagService.createInterfaceTag(formData.value);
      ElMessage.success('标签创建成功');

      // 新增操作：跳转第一页刷新
      PageRefresh.interfaceTag.afterAdd();
    }

    globalDrawerStore.closeDrawer();
  } catch (error: any) {
    console.error('提交失败:', error);

    // 智能错误消息提取
    let errorMessage = '操作失败';
    if (error?.message) {
      errorMessage = error.message;
    } else if (error?.detail) {
      errorMessage = error.detail;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 更新抽屉底部按钮配置
const updateDrawerButtons = () => {
  // console.log('🔧 InterfaceTagForm updateDrawerButtons 被调用');
  try {
    const rightButtons = [
      {
        text: '取消',
        handler: handleCancel
      },
      {
        text: isEdit.value ? '更新' : '创建',
        type: 'primary' as const,
        handler: handleSubmit,
        loading: loading.value
      }
    ];

    // 更新store中的按钮配置
    globalDrawerStore.leftButtons = []; // 接口标签管理没有左侧按钮
    globalDrawerStore.rightButtons = rightButtons;
    // console.log('✅ 按钮配置更新成功:', rightButtons);
  } catch (error) {
    console.error('❌ updateDrawerButtons 错误:', error);
  }
};

// 表单数据
const formData = ref<InterfaceTagRequest>({
  name: '',
  color: '#3FC8DD',
  description: '',
  isEnabled: true
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 50, message: '标签名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 选择预设颜色
const selectPresetColor = (color: string) => {
  formData.value.color = color;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    color: '#3FC8DD',
    description: '',
    isEnabled: true
  };
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 初始化表单数据
const initFormData = () => {
  if (isEdit.value && editData.value) {
    const data = editData.value as InterfaceTag;
    formData.value = {
      name: data.name,
      color: data.color,
      description: data.description || '',
      isEnabled: data.isEnabled
    };
  } else {
    resetForm();
  }
};

// 监听抽屉store中的数据变化，确保编辑数据正确加载
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps) {
    initFormData();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 监听加载状态变化，更新按钮
watch([loading, isEdit], () => {
  updateDrawerButtons();
});

// 重复的函数定义已移至前面

// 组件挂载时初始化
onMounted(() => {
  initFormData();
  updateDrawerButtons(); // 初始化按钮配置
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 2024-12-27: 表单内容样式继承自 page-common.scss 的 .drawer-form-content */

/* 2024-12-27: 颜色值显示样式(.color-value)已抽象到 page-common.scss，为共性样式 */

/* 接口标签管理特有的颜色选择器样式 - 个性化样式，不抽象 */
.color-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  /* .color-value 样式已移至 page-common.scss，作为共性样式 */
}

.color-presets {
  .preset-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }

  .preset-colors {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .preset-color {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s;

    &:hover {
      border-color: #409eff;
      transform: scale(1.1);
    }
  }
}
</style>
