<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Monitor /></el-icon>
        <span style="margin-left: 10px;">服务与接口监控</span>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData">刷新数据</el-button>
        <el-button @click="exportReport">导出报告</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="接口监控" name="api">
        <!-- 接口性能概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总接口数</h4>
            <div class="card-value">{{ apiOverview.totalApis }}</div>
            <div class="card-trend success">↑ 12%</div>
          </div>
          <div class="overview-card">
            <h4>平均响应时间</h4>
            <div class="card-value">{{ apiOverview.avgResponseTime }}ms</div>
            <div class="card-trend warning">↑ 5%</div>
          </div>
          <div class="overview-card">
            <h4>成功率</h4>
            <div class="card-value">{{ apiOverview.successRate }}%</div>
            <div class="card-trend success">↑ 2%</div>
          </div>
          <div class="overview-card">
            <h4>今日调用量</h4>
            <div class="card-value">{{ apiOverview.todayCalls }}</div>
            <div class="card-trend success">↑ 18%</div>
          </div>
        </div>

        <!-- 接口性能排行 -->
        <div class="section">
          <h3 class="section-title">接口调用频率排行</h3>
          <el-table :data="apiRanking" style="width: 100%">
            <el-table-column prop="path" label="接口路径" min-width="200" />
            <el-table-column prop="method" label="方法" width="80">
              <template #default="{ row }">
                <el-tag :type="getMethodType(row.method)" size="small">{{ row.method }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="calls" label="调用次数" width="120" />
            <el-table-column prop="avgResponseTime" label="平均响应时间" width="140">
              <template #default="{ row }">
                <span :class="getResponseTimeClass(row.avgResponseTime)">{{ row.avgResponseTime }}ms</span>
              </template>
            </el-table-column>
            <el-table-column prop="successRate" label="成功率" width="100">
              <template #default="{ row }">
                <span :class="getSuccessRateClass(row.successRate)">{{ row.successRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="errorCount" label="错误次数" width="100">
              <template #default="{ row }">
                <span :class="row.errorCount > 0 ? 'error' : 'success'">{{ row.errorCount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click="viewApiDetails(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="数据源监控" name="datasource">
        <!-- 数据源状态概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总数据源</h4>
            <div class="card-value">{{ datasourceOverview.total }}</div>
          </div>
          <div class="overview-card">
            <h4>在线数据源</h4>
            <div class="card-value success">{{ datasourceOverview.online }}</div>
          </div>
          <div class="overview-card">
            <h4>离线数据源</h4>
            <div class="card-value error">{{ datasourceOverview.offline }}</div>
          </div>
          <div class="overview-card">
            <h4>平均连接时间</h4>
            <div class="card-value">{{ datasourceOverview.avgConnTime }}ms</div>
          </div>
        </div>

        <!-- 数据源状态列表 -->
        <div class="section">
          <h3 class="section-title">数据源连接状态</h3>
          <el-table :data="datasourceStatus" style="width: 100%">
            <el-table-column prop="name" label="数据源名称" min-width="150" />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="host" label="主机地址" width="150" />
            <el-table-column prop="status" label="连接状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="responseTime" label="响应时间" width="120">
              <template #default="{ row }">
                <span :class="getResponseTimeClass(row.responseTime)">{{ row.responseTime }}ms</span>
              </template>
            </el-table-column>
            <el-table-column prop="lastCheck" label="最后检查" width="160" />
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" type="success" @click="testConnection(row)">测试连接</el-button>
                <el-button size="small" @click="viewDatasourceDetails(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="多实例监控" name="instances">
        <!-- 实例状态概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总实例数</h4>
            <div class="card-value">{{ instanceOverview.total }}</div>
          </div>
          <div class="overview-card">
            <h4>运行中实例</h4>
            <div class="card-value success">{{ instanceOverview.running }}</div>
          </div>
          <div class="overview-card">
            <h4>停止实例</h4>
            <div class="card-value error">{{ instanceOverview.stopped }}</div>
          </div>
          <div class="overview-card">
            <h4>平均负载</h4>
            <div class="card-value">{{ instanceOverview.avgLoad }}%</div>
          </div>
        </div>

        <!-- 实例详细信息 -->
        <div class="section">
          <h3 class="section-title">实例运行状态</h3>
          <el-table :data="instanceStatus" style="width: 100%">
            <el-table-column prop="name" label="实例名称" width="150" />
            <el-table-column prop="host" label="主机地址" width="150" />
            <el-table-column prop="port" label="端口" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cpu" label="CPU使用率" width="120">
              <template #default="{ row }">
                <span :class="getUsageClass(row.cpu)">{{ row.cpu }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="memory" label="内存使用率" width="120">
              <template #default="{ row }">
                <span :class="getUsageClass(row.memory)">{{ row.memory }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="uptime" label="运行时间" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="manageInstance(row)">管理</el-button>
                <el-button size="small" @click="viewInstanceLogs(row)">日志</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Monitor } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const activeTab = ref('api')

// API监控数据
const apiOverview = ref({
  totalApis: 156,
  avgResponseTime: 125,
  successRate: 98.5,
  todayCalls: 15420
})

const apiRanking = ref([
  { path: '/api/user/login', method: 'POST', calls: 2340, avgResponseTime: 89, successRate: 99.2, errorCount: 3 },
  { path: '/api/data/export', method: 'GET', calls: 1890, avgResponseTime: 245, successRate: 96.8, errorCount: 12 },
  { path: '/api/user/profile', method: 'GET', calls: 1560, avgResponseTime: 67, successRate: 99.8, errorCount: 1 },
  { path: '/api/data/import', method: 'POST', calls: 1230, avgResponseTime: 189, successRate: 98.1, errorCount: 5 },
  { path: '/api/system/status', method: 'GET', calls: 980, avgResponseTime: 45, successRate: 100, errorCount: 0 }
])

// 数据源监控数据
const datasourceOverview = ref({
  total: 8,
  online: 6,
  offline: 2,
  avgConnTime: 156
})

const datasourceStatus = ref([
  { name: '生产环境MySQL', type: 'MySQL', host: '*************', status: 'active', responseTime: 89, lastCheck: '2024-01-10 14:25:30' },
  { name: '测试环境PostgreSQL', type: 'PostgreSQL', host: '*************', status: 'active', responseTime: 156, lastCheck: '2024-01-10 14:24:15' },
  { name: '数据仓库Oracle', type: 'Oracle', host: '*************', status: 'error', responseTime: 0, lastCheck: '2024-01-10 14:20:45' },
  { name: '报表系统SQL Server', type: 'SQL Server', host: '*************', status: 'active', responseTime: 234, lastCheck: '2024-01-10 14:23:20' }
])

// 多实例监控数据
const instanceOverview = ref({
  total: 4,
  running: 3,
  stopped: 1,
  avgLoad: 65
})

const instanceStatus = ref([
  { name: 'instance-01', host: '************', port: 8080, status: 'active', cpu: 45, memory: 67, uptime: '15天3小时' },
  { name: 'instance-02', host: '************', port: 8080, status: 'active', cpu: 52, memory: 71, uptime: '15天3小时' },
  { name: 'instance-03', host: '************', port: 8080, status: 'active', cpu: 38, memory: 59, uptime: '12天8小时' },
  { name: 'instance-04', host: '************', port: 8080, status: 'error', cpu: 0, memory: 0, uptime: '已停止' }
])

// 方法
const getMethodType = (method: string) => {
  const types: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const getResponseTimeClass = (time: number) => {
  if (time < 100) return 'success'
  if (time < 300) return 'warning'
  return 'error'
}

const getSuccessRateClass = (rate: number) => {
  if (rate >= 99) return 'success'
  if (rate >= 95) return 'warning'
  return 'error'
}

const getStatusType = (status: string) => {
  return status === 'active' ? 'success' : 'danger'
}

const getStatusText = (status: string) => {
  return status === 'active' ? '在线' : '离线'
}

const getUsageClass = (usage: number) => {
  if (usage < 50) return 'success'
  if (usage < 80) return 'warning'
  return 'error'
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const exportReport = () => {
  ElMessage.info('报告导出功能开发中')
}

const viewApiDetails = (api: any) => {
  ElMessage.info(`查看接口详情: ${api.path}`)
}

const testConnection = (datasource: any) => {
  ElMessage.info(`测试连接: ${datasource.name}`)
}

const viewDatasourceDetails = (datasource: any) => {
  ElMessage.info(`查看数据源详情: ${datasource.name}`)
}

const manageInstance = (instance: any) => {
  ElMessage.info(`管理实例: ${instance.name}`)
}

const viewInstanceLogs = (instance: any) => {
  ElMessage.info(`查看实例日志: ${instance.name}`)
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
/* 服务监控页面特有样式 */

/* 重复的页签和表格样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 重复的按钮和滚动条样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.overview-card h4 {
  margin: 0 0 10px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-value.success {
  color: #10b981;
}

.card-value.error {
  color: #ef4444;
}

.card-trend {
  font-size: 12px;
  font-weight: 500;
}

.card-trend.success {
  color: #10b981;
}

.card-trend.warning {
  color: #f59e0b;
}

.card-trend.error {
  color: #ef4444;
}

/* 区域样式 */
.section {
  margin-bottom: 30px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #374151;
  font-weight: 600;
}

/* 表格中的状态样式 */
.success {
  color: #10b981;
  font-weight: 500;
}

.warning {
  color: #f59e0b;
  font-weight: 500;
}

.error {
  color: #ef4444;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
}
</style>
