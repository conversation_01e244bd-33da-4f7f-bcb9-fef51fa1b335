"""
数据源控制器层
处理HTTP请求和响应
"""

from fastapi import Depends
from sqlalchemy.orm import Session
from typing import Optional
from app.config.datasource.services.data_source_service import DataSourceService
from app.config.datasource.schemas.data_source_schema import (
    DataSourceCreate,    # 数据源创建模式
    DataSourceUpdate,    # 数据源更新模式
    DataSourceResponse,  # 单个数据源响应模式
    DataSourceListResponse,  # 数据源列表响应模式
    ConnectionTestResponse  # 连接测试响应模式
)
from app.shared.database import get_database

class DataSourceController:
    """数据源控制器类"""
    
    def __init__(self, db: Session = Depends(get_database)):
        self.db = db
        self.service = DataSourceService(db)
    
    async def get_data_sources(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None,
        db_type: Optional[str] = None
    ) -> DataSourceListResponse:
        """
        获取数据源列表
        
        Args:
            page: 页码，默认1
            size: 每页大小，默认10
            search: 搜索关键词，可选
            status: 状态过滤，可选
            db_type: 数据库类型过滤，可选
            
        Returns:
            数据源列表响应
        """
        return self.service.get_data_sources(page, size, search, status, db_type)
    
    async def get_data_source(self, data_source_id: int) -> DataSourceResponse:
        """
        获取单个数据源
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            数据源响应
        """
        return self.service.get_data_source(data_source_id)
    
    async def create_data_source(self, data_source_data: DataSourceCreate) -> DataSourceResponse:
        """
        创建数据源
        
        Args:
            data_source_data: 数据源创建数据
            
        Returns:
            创建的数据源响应
        """
        return self.service.create_data_source(data_source_data)
    
    async def update_data_source(
        self, 
        data_source_id: int, 
        data_source_data: DataSourceUpdate
    ) -> DataSourceResponse:
        """
        更新数据源
        
        Args:
            data_source_id: 数据源ID
            data_source_data: 更新数据
            
        Returns:
            更新后的数据源响应
        """
        return self.service.update_data_source(data_source_id, data_source_data)
    
    async def delete_data_source(self, data_source_id: int) -> dict:
        """
        删除数据源
        
        Args:
            data_source_id: 数据源ID
            
        Returns:
            删除结果
        """
        return self.service.delete_data_source(data_source_id)
    

    async def test_saved_data_source_connection(self, data_source_id: int) -> ConnectionTestResponse:
        """
        测试已保存数据源的连接

        Args:
            data_source_id: 数据源ID

        Returns:
            连接测试响应
        """
        return await self.service.test_saved_data_source_connection(data_source_id)

    async def validate_table_name(self, data_source_id: int, table_name: str, table_type: str) -> dict:
        """
        校验表/视图/存储过程名称是否存在

        Args:
            data_source_id: 数据源ID
            table_name: 表/视图/存储过程名称
            table_type: 类型（table/view/procedure）

        Returns:
            校验结果
        """
        return await self.service.validate_table_name(data_source_id, table_name, table_type)

    async def get_table_structure(self, data_source_id: int, table_name: str, table_type: str) -> dict:
        """
        获取表结构信息

        Args:
            data_source_id: 数据源ID
            table_name: 表名称
            table_type: 类型（table/view）

        Returns:
            表结构信息
        """
        return await self.service.get_table_structure(data_source_id, table_name, table_type)


