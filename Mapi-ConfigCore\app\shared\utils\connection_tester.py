"""
数据库连接测试工具
支持多种数据库类型的连接测试
"""

import sqlite3
from typing import NamedTuple
from dataclasses import dataclass
from app.shared.core.log_util import LogUtil

@dataclass
class ConnectionTestResult:
    """连接测试结果"""
    success: bool
    message: str
    error_detail: str = None

class ConnectionTester:
    """数据库连接测试器"""

    def __init__(self):
        pass  # 使用LogUtil静态方法，不需要实例化
    
    def test_connection(
        self,
        db_type: str,
        host: str,
        port: int,
        database: str,
        username: str,
        password: str,
        timeout: int = 10
    ) -> ConnectionTestResult:
        """
        测试数据库连接
        
        Args:
            db_type: 数据库类型
            host: 主机地址
            port: 端口
            database: 数据库名
            username: 用户名
            password: 密码
            timeout: 超时时间
            
        Returns:
            连接测试结果
        """
        try:
            if db_type == "mysql":
                return self._test_mysql(host, port, database, username, password, timeout)
            elif db_type == "postgresql":
                return self._test_postgresql(host, port, database, username, password, timeout)
            elif db_type == "sqlserver":
                return self._test_sqlserver(host, port, database, username, password, timeout)
            elif db_type == "oracle":
                return self._test_oracle(host, port, database, username, password, timeout)
            elif db_type == "sqlite":
                return self._test_sqlite(database)
            else:
                return ConnectionTestResult(
                    success=False,
                    message=f"不支持的数据库类型: {db_type}",
                    error_detail=f"支持的类型: mysql, postgresql, sqlserver, oracle, sqlite"
                )
                
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="连接测试异常",
                error_detail=str(e)
            )
    
    def _test_mysql(self, host: str, port: int, database: str, username: str, password: str, timeout: int) -> ConnectionTestResult:
        """测试MySQL连接"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host=host,
                port=port,
                user=username,
                password=password,
                database=database,
                connect_timeout=timeout,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
            
            connection.close()
            
            return ConnectionTestResult(
                success=True,
                message=f"MySQL连接成功，版本: {version}"
            )
            
        except ImportError:
            return ConnectionTestResult(
                success=False,
                message="MySQL驱动未安装",
                error_detail="请安装pymysql: pip install pymysql"
            )
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="MySQL连接失败",
                error_detail=str(e)
            )
    
    def _test_postgresql(self, host: str, port: int, database: str, username: str, password: str, timeout: int) -> ConnectionTestResult:
        """测试PostgreSQL连接"""
        try:
            import psycopg2
            
            connection = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=username,
                password=password,
                connect_timeout=timeout
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT version()")
                version = cursor.fetchone()[0]
            
            connection.close()
            
            return ConnectionTestResult(
                success=True,
                message=f"PostgreSQL连接成功，版本: {version}"
            )
            
        except ImportError:
            return ConnectionTestResult(
                success=False,
                message="PostgreSQL驱动未安装",
                error_detail="请安装psycopg2: pip install psycopg2-binary"
            )
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="PostgreSQL连接失败",
                error_detail=str(e)
            )
    
    def _test_sqlserver(self, host: str, port: int, database: str, username: str, password: str, timeout: int) -> ConnectionTestResult:
        """测试SQL Server连接 - 使用pyodbc驱动"""
        try:
            import pyodbc

            # 使用SQL Server驱动程序（兼容性最好）
            connection_string = (
                f"DRIVER={{SQL Server}};"
                f"SERVER={host},{port};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"Timeout={timeout};"
            )

            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            cursor.close()
            connection.close()

            return ConnectionTestResult(
                success=True,
                message=f"SQL Server连接成功，版本: {version}"
            )

        except ImportError:
            return ConnectionTestResult(
                success=False,
                message="缺少SQL Server驱动",
                error_detail="请安装pyodbc驱动: poetry add pyodbc"
            )
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="SQL Server连接失败",
                error_detail=str(e)
            )


    
    def _test_oracle(self, host: str, port: int, database: str, username: str, password: str, timeout: int) -> ConnectionTestResult:
        """测试Oracle连接"""
        try:
            import cx_Oracle
            
            # Oracle连接字符串
            dsn = cx_Oracle.makedsn(host, port, service_name=database)
            connection = cx_Oracle.connect(username, password, dsn)
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT * FROM v$version WHERE rownum = 1")
                version = cursor.fetchone()[0]
            
            connection.close()
            
            return ConnectionTestResult(
                success=True,
                message=f"Oracle连接成功，版本: {version}"
            )
            
        except ImportError:
            return ConnectionTestResult(
                success=False,
                message="Oracle驱动未安装",
                error_detail="请安装cx_Oracle: pip install cx_Oracle"
            )
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="Oracle连接失败",
                error_detail=str(e)
            )
    
    def _test_sqlite(self, database: str) -> ConnectionTestResult:
        """测试SQLite连接"""
        try:
            connection = sqlite3.connect(database)
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT sqlite_version()")
                version = cursor.fetchone()[0]
            
            connection.close()
            
            return ConnectionTestResult(
                success=True,
                message=f"SQLite连接成功，版本: {version}"
            )
            
        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message="SQLite连接失败",
                error_detail=str(e)
            )

    async def validate_table_name(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """
        校验表/视图/存储过程名称是否存在

        Args:
            connection_params: 连接参数字典
            table_name: 表/视图/存储过程名称
            table_type: 类型（table/view/procedure）

        Returns:
            校验结果字典
        """
        try:
            db_type = connection_params.get("db_type")

            if db_type == "mysql":
                return await self._validate_mysql_table(connection_params, table_name, table_type)
            elif db_type == "postgresql":
                return await self._validate_postgresql_table(connection_params, table_name, table_type)
            elif db_type == "sqlserver":
                return await self._validate_sqlserver_table(connection_params, table_name, table_type)
            elif db_type == "oracle":
                return await self._validate_oracle_table(connection_params, table_name, table_type)
            elif db_type == "sqlite":
                return await self._validate_sqlite_table(connection_params, table_name, table_type)
            else:
                return {
                    "exists": False,
                    "message": f"不支持的数据库类型: {db_type}"
                }

        except Exception as e:
            LogUtil.error("表名称校验异常", error=str(e), table_name=table_name, table_type=table_type)
            return {
                "exists": False,
                "message": f"校验异常: {str(e)}"
            }

    async def _validate_mysql_table(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """校验MySQL表/视图/存储过程是否存在"""
        try:
            import pymysql

            connection = pymysql.connect(
                host=connection_params["host"],
                port=connection_params["port"],
                user=connection_params["username"],
                password=connection_params["password"],
                database=connection_params["database"],
                connect_timeout=10,
                charset='utf8mb4'
            )

            with connection.cursor() as cursor:
                if table_type == "table":
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.tables
                        WHERE table_schema = %s AND table_name = %s AND table_type = 'BASE TABLE'
                    """, (connection_params["database"], table_name))
                elif table_type == "view":
                    # 检查视图是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.views
                        WHERE table_schema = %s AND table_name = %s
                    """, (connection_params["database"], table_name))
                elif table_type == "procedure":
                    # 检查存储过程是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.routines
                        WHERE routine_schema = %s AND routine_name = %s AND routine_type = 'PROCEDURE'
                    """, (connection_params["database"], table_name))
                else:
                    return {"exists": False, "message": f"不支持的类型: {table_type}"}

                count = cursor.fetchone()[0]
                exists = count > 0

            connection.close()

            return {
                "exists": exists,
                "message": f"{'存在' if exists else '不存在'}"
            }

        except Exception as e:
            return {
                "exists": False,
                "message": f"MySQL校验失败: {str(e)}"
            }

    async def _validate_postgresql_table(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """校验PostgreSQL表/视图/存储过程是否存在"""
        try:
            import psycopg2

            connection = psycopg2.connect(
                host=connection_params["host"],
                port=connection_params["port"],
                database=connection_params["database"],
                user=connection_params["username"],
                password=connection_params["password"],
                connect_timeout=10
            )

            with connection.cursor() as cursor:
                if table_type == "table":
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.tables
                        WHERE table_catalog = %s AND table_name = %s AND table_type = 'BASE TABLE'
                    """, (connection_params["database"], table_name))
                elif table_type == "view":
                    # 检查视图是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.views
                        WHERE table_catalog = %s AND table_name = %s
                    """, (connection_params["database"], table_name))
                elif table_type == "procedure":
                    # 检查存储过程是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.routines
                        WHERE routine_catalog = %s AND routine_name = %s AND routine_type = 'PROCEDURE'
                    """, (connection_params["database"], table_name))
                else:
                    return {"exists": False, "message": f"不支持的类型: {table_type}"}

                count = cursor.fetchone()[0]
                exists = count > 0

            connection.close()

            return {
                "exists": exists,
                "message": f"{'存在' if exists else '不存在'}"
            }

        except Exception as e:
            return {
                "exists": False,
                "message": f"PostgreSQL校验失败: {str(e)}"
            }

    async def _validate_sqlite_table(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """校验SQLite表/视图是否存在"""
        try:
            import sqlite3

            connection = sqlite3.connect(connection_params["database"])
            cursor = connection.cursor()

            if table_type == "table":
                # 检查表是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type = 'table' AND name = ?
                """, (table_name,))
            elif table_type == "view":
                # 检查视图是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type = 'view' AND name = ?
                """, (table_name,))
            else:
                return {"exists": False, "message": f"SQLite不支持类型: {table_type}"}

            count = cursor.fetchone()[0]
            exists = count > 0

            cursor.close()
            connection.close()

            return {
                "exists": exists,
                "message": f"{'存在' if exists else '不存在'}"
            }

        except Exception as e:
            return {
                "exists": False,
                "message": f"SQLite校验失败: {str(e)}"
            }

    async def _validate_sqlserver_table(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """校验SQL Server表/视图/存储过程是否存在"""
        try:
            import pyodbc

            connection_string = (
                f"DRIVER={{SQL Server}};"
                f"SERVER={connection_params['host']},{connection_params['port']};"
                f"DATABASE={connection_params['database']};"
                f"UID={connection_params['username']};"
                f"PWD={connection_params['password']};"
                f"Timeout=10;"
            )

            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()

            if table_type == "table":
                # 检查表是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_CATALOG = ? AND TABLE_NAME = ? AND TABLE_TYPE = 'BASE TABLE'
                """, connection_params["database"], table_name)
            elif table_type == "view":
                # 检查视图是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS
                    WHERE TABLE_CATALOG = ? AND TABLE_NAME = ?
                """, connection_params["database"], table_name)
            elif table_type == "procedure":
                # 检查存储过程是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES
                    WHERE ROUTINE_CATALOG = ? AND ROUTINE_NAME = ? AND ROUTINE_TYPE = 'PROCEDURE'
                """, connection_params["database"], table_name)
            else:
                return {"exists": False, "message": f"不支持的类型: {table_type}"}

            count = cursor.fetchone()[0]
            exists = count > 0

            cursor.close()
            connection.close()

            return {
                "exists": exists,
                "message": f"{'存在' if exists else '不存在'}"
            }

        except Exception as e:
            return {
                "exists": False,
                "message": f"SQL Server校验失败: {str(e)}"
            }

    async def _validate_oracle_table(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """校验Oracle表/视图/存储过程是否存在"""
        try:
            import cx_Oracle

            dsn = cx_Oracle.makedsn(connection_params["host"], connection_params["port"],
                                  service_name=connection_params["database"])
            connection = cx_Oracle.connect(connection_params["username"],
                                         connection_params["password"], dsn)

            with connection.cursor() as cursor:
                if table_type == "table":
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name = UPPER(:table_name)
                    """, table_name=table_name)
                elif table_type == "view":
                    # 检查视图是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM user_views WHERE view_name = UPPER(:table_name)
                    """, table_name=table_name)
                elif table_type == "procedure":
                    # 检查存储过程是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM user_procedures WHERE object_name = UPPER(:table_name)
                    """, table_name=table_name)
                else:
                    return {"exists": False, "message": f"不支持的类型: {table_type}"}

                count = cursor.fetchone()[0]
                exists = count > 0

            connection.close()

            return {
                "exists": exists,
                "message": f"{'存在' if exists else '不存在'}"
            }

        except Exception as e:
            return {
                "exists": False,
                "message": f"Oracle校验失败: {str(e)}"
            }

    async def get_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """
        获取表结构信息

        Args:
            connection_params: 连接参数字典
            table_name: 表名称
            table_type: 类型（table/view）

        Returns:
            表结构信息字典
        """
        try:
            db_type = connection_params.get("db_type")

            if db_type == "mysql":
                return await self._get_mysql_table_structure(connection_params, table_name, table_type)
            elif db_type == "postgresql":
                return await self._get_postgresql_table_structure(connection_params, table_name, table_type)
            elif db_type == "sqlserver":
                return await self._get_sqlserver_table_structure(connection_params, table_name, table_type)
            elif db_type == "oracle":
                return await self._get_oracle_table_structure(connection_params, table_name, table_type)
            elif db_type == "sqlite":
                return await self._get_sqlite_table_structure(connection_params, table_name, table_type)
            else:
                return {
                    "success": False,
                    "message": f"不支持的数据库类型: {db_type}",
                    "columns": []
                }

        except Exception as e:
            LogUtil.error("获取表结构异常", error=str(e), table_name=table_name, table_type=table_type)
            return {
                "success": False,
                "message": f"获取表结构异常: {str(e)}",
                "columns": []
            }

    async def _get_mysql_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """获取MySQL表结构"""
        try:
            import pymysql

            connection = pymysql.connect(
                host=connection_params["host"],
                port=connection_params["port"],
                user=connection_params["username"],
                password=connection_params["password"],
                database=connection_params["database"],
                connect_timeout=10,
                charset='utf8mb4'
            )

            columns = []
            with connection.cursor() as cursor:
                # 查询表结构
                cursor.execute("""
                    SELECT
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        COLUMN_KEY,
                        EXTRA,
                        COLUMN_COMMENT,
                        CHARACTER_MAXIMUM_LENGTH,
                        NUMERIC_PRECISION,
                        NUMERIC_SCALE
                    FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                    ORDER BY ORDINAL_POSITION
                """, (connection_params["database"], table_name))

                for row in cursor.fetchall():
                    column_info = {
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == 'YES',
                        "default": row[3],
                        "key": row[4],
                        "extra": row[5],
                        "comment": row[6] or '',
                        "length": row[7],
                        "precision": row[8],
                        "scale": row[9]
                    }
                    columns.append(column_info)

            connection.close()

            return {
                "success": True,
                "message": "获取表结构成功",
                "columns": columns
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"MySQL表结构查询失败: {str(e)}",
                "columns": []
            }

    async def _get_sqlite_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """获取SQLite表结构"""
        try:
            import sqlite3

            connection = sqlite3.connect(connection_params["database"])
            cursor = connection.cursor()

            columns = []
            if table_type == "table":
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                for row in cursor.fetchall():
                    column_info = {
                        "name": row[1],
                        "type": row[2],
                        "nullable": not bool(row[3]),  # not null = 1, nullable = 0
                        "default": row[4],
                        "key": "PRI" if row[5] else "",  # primary key = 1
                        "extra": "",
                        "comment": "",
                        "length": None,
                        "precision": None,
                        "scale": None
                    }
                    columns.append(column_info)
            elif table_type == "view":
                # 对于视图，获取列信息（简化版）
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 0")
                column_names = [description[0] for description in cursor.description]
                for name in column_names:
                    column_info = {
                        "name": name,
                        "type": "TEXT",  # SQLite视图默认类型
                        "nullable": True,
                        "default": None,
                        "key": "",
                        "extra": "",
                        "comment": "",
                        "length": None,
                        "precision": None,
                        "scale": None
                    }
                    columns.append(column_info)

            cursor.close()
            connection.close()

            return {
                "success": True,
                "message": "获取表结构成功",
                "columns": columns
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"SQLite表结构查询失败: {str(e)}",
                "columns": []
            }

    async def _get_postgresql_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """获取PostgreSQL表结构"""
        try:
            import psycopg2

            connection = psycopg2.connect(
                host=connection_params["host"],
                port=connection_params["port"],
                database=connection_params["database"],
                user=connection_params["username"],
                password=connection_params["password"],
                connect_timeout=10
            )

            columns = []
            with connection.cursor() as cursor:
                if table_type == "table":
                    # 获取表结构
                    cursor.execute("""
                        SELECT
                            c.column_name,
                            c.data_type,
                            c.is_nullable,
                            c.column_default,
                            CASE WHEN pk.column_name IS NOT NULL THEN 'PRI' ELSE '' END as column_key,
                            '' as extra,
                            COALESCE(pgd.description, '') as column_comment,
                            c.character_maximum_length,
                            c.numeric_precision,
                            c.numeric_scale
                        FROM information_schema.columns c
                        LEFT JOIN (
                            SELECT kcu.column_name
                            FROM information_schema.table_constraints tc
                            INNER JOIN information_schema.key_column_usage kcu
                                ON tc.constraint_name = kcu.constraint_name
                            WHERE tc.constraint_type = 'PRIMARY KEY'
                                AND tc.table_name = %s
                                AND tc.table_catalog = %s
                        ) pk ON c.column_name = pk.column_name
                        LEFT JOIN pg_catalog.pg_statio_all_tables st ON st.relname = c.table_name
                        LEFT JOIN pg_catalog.pg_description pgd ON pgd.objoid = st.relid
                        WHERE c.table_name = %s AND c.table_catalog = %s
                        ORDER BY c.ordinal_position
                    """, (table_name, connection_params["database"], table_name, connection_params["database"]))
                elif table_type == "view":
                    # 获取视图结构
                    cursor.execute("""
                        SELECT
                            c.column_name,
                            c.data_type,
                            c.is_nullable,
                            c.column_default,
                            '' as column_key,
                            '' as extra,
                            '' as column_comment,
                            c.character_maximum_length,
                            c.numeric_precision,
                            c.numeric_scale
                        FROM information_schema.columns c
                        WHERE c.table_name = %s AND c.table_catalog = %s
                        ORDER BY c.ordinal_position
                    """, (table_name, connection_params["database"]))

                for row in cursor.fetchall():
                    column_info = {
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == 'YES',
                        "default": row[3],
                        "key": row[4],
                        "extra": row[5],
                        "comment": row[6] or '',
                        "length": row[7],
                        "precision": row[8],
                        "scale": row[9]
                    }
                    columns.append(column_info)

            connection.close()

            return {
                "success": True,
                "message": "获取表结构成功",
                "columns": columns
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"PostgreSQL表结构查询失败: {str(e)}",
                "columns": []
            }

    async def _get_sqlserver_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """获取SQL Server表结构"""
        try:
            import pyodbc

            connection_string = (
                f"DRIVER={{SQL Server}};"
                f"SERVER={connection_params['host']},{connection_params['port']};"
                f"DATABASE={connection_params['database']};"
                f"UID={connection_params['username']};"
                f"PWD={connection_params['password']};"
                f"Timeout=10;"
            )

            connection = pyodbc.connect(connection_string)
            cursor = connection.cursor()

            columns = []
            if table_type == "table":
                # 获取表结构
                cursor.execute("""
                    SELECT
                        c.COLUMN_NAME,
                        c.DATA_TYPE,
                        c.IS_NULLABLE,
                        c.COLUMN_DEFAULT,
                        CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PRI' ELSE '' END as COLUMN_KEY,
                        CASE WHEN c.EXTRA IS NOT NULL THEN c.EXTRA ELSE '' END as EXTRA,
                        ISNULL(ep.value, '') as COLUMN_COMMENT,
                        c.CHARACTER_MAXIMUM_LENGTH,
                        c.NUMERIC_PRECISION,
                        c.NUMERIC_SCALE
                    FROM INFORMATION_SCHEMA.COLUMNS c
                    LEFT JOIN (
                        SELECT ku.TABLE_NAME, ku.COLUMN_NAME
                        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                        INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                            ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                        WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                    ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
                    LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME)
                        AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
                    WHERE c.TABLE_NAME = ? AND c.TABLE_CATALOG = ?
                    ORDER BY c.ORDINAL_POSITION
                """, table_name, connection_params['database'])
            elif table_type == "view":
                # 获取视图结构
                cursor.execute("""
                    SELECT
                        c.COLUMN_NAME,
                        c.DATA_TYPE,
                        c.IS_NULLABLE,
                        c.COLUMN_DEFAULT,
                        '' as COLUMN_KEY,
                        '' as EXTRA,
                        ISNULL(ep.value, '') as COLUMN_COMMENT,
                        c.CHARACTER_MAXIMUM_LENGTH,
                        c.NUMERIC_PRECISION,
                        c.NUMERIC_SCALE
                    FROM INFORMATION_SCHEMA.COLUMNS c
                    LEFT JOIN sys.extended_properties ep ON ep.major_id = OBJECT_ID(c.TABLE_SCHEMA + '.' + c.TABLE_NAME)
                        AND ep.minor_id = c.ORDINAL_POSITION AND ep.name = 'MS_Description'
                    WHERE c.TABLE_NAME = ? AND c.TABLE_CATALOG = ?
                    ORDER BY c.ORDINAL_POSITION
                """, table_name, connection_params['database'])

            for row in cursor.fetchall():
                column_info = {
                    "name": row[0],
                    "type": row[1],
                    "nullable": row[2] == 'YES',
                    "default": row[3],
                    "key": row[4],
                    "extra": row[5],
                    "comment": row[6] or '',
                    "length": row[7],
                    "precision": row[8],
                    "scale": row[9]
                }
                columns.append(column_info)

            cursor.close()
            connection.close()

            return {
                "success": True,
                "message": "获取表结构成功",
                "columns": columns
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"SQL Server表结构查询失败: {str(e)}",
                "columns": []
            }

    async def _get_oracle_table_structure(self, connection_params: dict, table_name: str, table_type: str) -> dict:
        """获取Oracle表结构"""
        try:
            import cx_Oracle

            dsn = cx_Oracle.makedsn(connection_params["host"], connection_params["port"],
                                  service_name=connection_params["database"])
            connection = cx_Oracle.connect(connection_params["username"],
                                         connection_params["password"], dsn)

            columns = []
            with connection.cursor() as cursor:
                if table_type == "table":
                    # 获取表结构
                    cursor.execute("""
                        SELECT
                            c.COLUMN_NAME,
                            c.DATA_TYPE,
                            c.NULLABLE,
                            c.DATA_DEFAULT,
                            CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PRI' ELSE '' END as COLUMN_KEY,
                            '' as EXTRA,
                            NVL(cc.COMMENTS, '') as COLUMN_COMMENT,
                            c.DATA_LENGTH,
                            c.DATA_PRECISION,
                            c.DATA_SCALE
                        FROM USER_TAB_COLUMNS c
                        LEFT JOIN (
                            SELECT cc.COLUMN_NAME
                            FROM USER_CONSTRAINTS uc
                            INNER JOIN USER_CONS_COLUMNS cc ON uc.CONSTRAINT_NAME = cc.CONSTRAINT_NAME
                            WHERE uc.CONSTRAINT_TYPE = 'P' AND uc.TABLE_NAME = UPPER(:table_name)
                        ) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
                        LEFT JOIN USER_COL_COMMENTS cc ON c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                        WHERE c.TABLE_NAME = UPPER(:table_name)
                        ORDER BY c.COLUMN_ID
                    """, table_name=table_name)
                elif table_type == "view":
                    # 获取视图结构
                    cursor.execute("""
                        SELECT
                            c.COLUMN_NAME,
                            c.DATA_TYPE,
                            c.NULLABLE,
                            c.DATA_DEFAULT,
                            '' as COLUMN_KEY,
                            '' as EXTRA,
                            NVL(cc.COMMENTS, '') as COLUMN_COMMENT,
                            c.DATA_LENGTH,
                            c.DATA_PRECISION,
                            c.DATA_SCALE
                        FROM USER_TAB_COLUMNS c
                        LEFT JOIN USER_COL_COMMENTS cc ON c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                        WHERE c.TABLE_NAME = UPPER(:table_name)
                        ORDER BY c.COLUMN_ID
                    """, table_name=table_name)

                for row in cursor.fetchall():
                    column_info = {
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == 'Y',
                        "default": row[3],
                        "key": row[4],
                        "extra": row[5],
                        "comment": row[6] or '',
                        "length": row[7],
                        "precision": row[8],
                        "scale": row[9]
                    }
                    columns.append(column_info)

            connection.close()

            return {
                "success": True,
                "message": "获取表结构成功",
                "columns": columns
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Oracle表结构查询失败: {str(e)}",
                "columns": []
            }
