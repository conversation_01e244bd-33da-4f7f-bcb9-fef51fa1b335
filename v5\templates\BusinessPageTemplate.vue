<!-- 
  业务页面标准模板
  使用说明：
  1. 复制此模板创建新页面
  2. 替换所有 {ModuleName} 为实际模块名
  3. 替换所有 {moduleName} 为实际模块名（小写）
  4. 替换所有 {module-name} 为实际模块名（短横线）
  5. 根据实际需求调整表格列和操作按钮
-->
<template>
  <div class="container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <el-icon><IconName /></el-icon>
        <span class="title-text">{ModuleName}管理</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索{ModuleName}名称或关键词"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-select v-model="filterStatus" placeholder="选择状态" clearable @change="handleSearch" style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="启用" value="enabled" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button type="primary" @click="handleAdd">新增{ModuleName}</el-button>
        <el-button @click="loadData">刷新</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; min-width: 1000px;"
      :row-style="{ height: '60px' }"
      :cell-style="{ padding: '12px 0' }"
    >
      <!-- 空状态插槽 -->
      <template #empty>
        <EmptyState
          :type="searchQuery ? 'search' : 'table'"
          :title="searchQuery ? '无搜索结果' : '暂无{ModuleName}'"
          :description="searchQuery ? `没有找到包含「${searchQuery}」的{ModuleName}，请尝试其他搜索条件` : '当前没有{ModuleName}数据，您可以点击上方按钮添加新的{ModuleName}'"
          :action-text="searchQuery ? '清除搜索' : '新增{ModuleName}'"
          @action="searchQuery ? clearSearch : handleAdd"
        />
      </template>
      <el-table-column prop="name" label="{ModuleName}名称" min-width="150">
        <template #default="{ row }">
          <div class="name-cell">
            <el-button
              type="text"
              size="small"
              @click="copyToClipboard(row.id)"
              class="copy-icon-btn"
            >
              <el-icon><CopyDocument /></el-icon>
            </el-button>
            <span class="name-text">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag 
            :type="row.status === 'enabled' ? 'success' : 'danger'"
            @click="handleToggleStatus(row)"
            style="cursor: pointer;"
            title="点击切换状态"
          >
            {{ row.status === 'enabled' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="primary" size="small" @click="handleDetail(row)">
            详情
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <PaginationComponent
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { IconName, CopyDocument } from '@element-plus/icons-vue'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import SearchComponent from '@/components/common/SearchComponent.vue'
import PaginationComponent from '@/components/common/PaginationComponent.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import {moduleName} from '@/services/{module-name}.service'

// 抽屉状态管理
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchQuery = ref('')
const filterStatus = ref('')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchQuery.value,
      status: filterStatus.value
    }
    
    const response = await {moduleName}Service.get{ModuleName}List(params)
    tableData.value = response.data
    totalCount.value = response.total
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error('加载{ModuleName}列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
  loadData()
}

// 分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 新增{ModuleName}
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增{ModuleName}',
    component: '{ModuleName}Form',
    props: {
      isEdit: false
    }
  })
}

// 编辑{ModuleName}
const handleEdit = (row) => {
  drawerMessenger.showDrawer({
    title: '编辑{ModuleName}',
    component: '{ModuleName}Form',
    props: {
      isEdit: true,
      editData: { ...row }
    }
  })
}

// 查看详情
const handleDetail = (row) => {
  drawerMessenger.showDrawer({
    title: `{ModuleName}详情 - ${row.name}`,
    component: '{ModuleName}Detail',
    props: {
      data: { ...row }
    }
  })
}

// 切换状态
const handleToggleStatus = async (row) => {
  const action = row.status === 'enabled' ? '禁用' : '启用'
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  try {
    await ElMessageBox.confirm(
      `确定要${action}{ModuleName}"${row.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await {moduleName}Service.toggle{ModuleName}Status(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除{ModuleName}
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除{ModuleName}"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await {moduleName}Service.delete{ModuleName}(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 页面特有样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-icon-btn {
  padding: 4px !important;
  min-height: auto !important;

  .el-icon {
    font-size: 14px;
    color: #409eff;
  }

  &:hover .el-icon {
    color: #66b1ff;
  }
}

.name-text {
  font-weight: 500;
}
</style>
