<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><User /></el-icon>
        <span style="margin-left: 10px;">用户与访问分析</span>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="default"
          @change="handleDateChange"
        />
        <el-button @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="用户活跃度" name="activity">
        <!-- 用户活跃度概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总用户数</h4>
            <div class="card-value">{{ userOverview.totalUsers }}</div>
            <div class="card-trend success">↑ 8%</div>
          </div>
          <div class="overview-card">
            <h4>今日活跃用户</h4>
            <div class="card-value">{{ userOverview.todayActiveUsers }}</div>
            <div class="card-trend success">↑ 12%</div>
          </div>
          <div class="overview-card">
            <h4>在线用户</h4>
            <div class="card-value success">{{ userOverview.onlineUsers }}</div>
          </div>
          <div class="overview-card">
            <h4>平均会话时长</h4>
            <div class="card-value">{{ userOverview.avgSessionTime }}分钟</div>
            <div class="card-trend warning">↓ 3%</div>
          </div>
        </div>

        <!-- 用户活跃度趋势 -->
        <div class="section">
          <h3 class="section-title">7天用户活跃度趋势</h3>
          <div class="activity-chart">
            <div class="chart-container">
              <div v-for="(day, index) in activityTrend" :key="index" class="chart-day">
                <div class="chart-bar-container">
                  <div class="chart-bar" :style="{ height: `${day.percentage}%` }"></div>
                </div>
                <div class="chart-label">{{ day.date }}</div>
                <div class="chart-value">{{ day.users }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能模块使用统计 -->
        <div class="section">
          <h3 class="section-title">功能模块使用频率</h3>
          <el-table :data="moduleUsage" style="width: 100%">
            <el-table-column prop="module" label="功能模块" width="200" />
            <el-table-column prop="visits" label="访问次数" width="120" />
            <el-table-column prop="users" label="使用用户数" width="120" />
            <el-table-column prop="avgTime" label="平均使用时长" width="140" />
            <el-table-column prop="percentage" label="使用占比" width="120">
              <template #default="{ row }">
                <div class="percentage-bar">
                  <div class="percentage-fill" :style="{ width: `${row.percentage}%` }"></div>
                  <span class="percentage-text">{{ row.percentage }}%</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="trend" label="趋势" width="100">
              <template #default="{ row }">
                <span :class="getTrendClass(row.trend)">{{ row.trend }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="客户端管理" name="clients">
        <!-- 客户端概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总客户端数</h4>
            <div class="card-value">{{ clientOverview.totalClients }}</div>
          </div>
          <div class="overview-card">
            <h4>活跃客户端</h4>
            <div class="card-value success">{{ clientOverview.activeClients }}</div>
          </div>
          <div class="overview-card">
            <h4>Token总数</h4>
            <div class="card-value">{{ clientOverview.totalTokens }}</div>
          </div>
          <div class="overview-card">
            <h4>即将过期Token</h4>
            <div class="card-value warning">{{ clientOverview.expiringTokens }}</div>
          </div>
        </div>

        <!-- 客户端状态列表 -->
        <div class="section">
          <h3 class="section-title">客户端连接状态</h3>
          <el-table :data="clientStatus" style="width: 100%">
            <el-table-column prop="name" label="客户端名称" width="150" />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastActive" label="最后活跃" width="160" />
            <el-table-column prop="tokenExpiry" label="Token过期时间" width="160" />
            <el-table-column prop="apiCalls" label="API调用次数" width="120" />
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="manageClient(row)">管理</el-button>
                <el-button size="small" type="warning" @click="refreshToken(row)">刷新Token</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="安全监控" name="security">
        <!-- 安全概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>今日登录次数</h4>
            <div class="card-value">{{ securityOverview.todayLogins }}</div>
            <div class="card-trend success">↑ 5%</div>
          </div>
          <div class="overview-card">
            <h4>登录失败次数</h4>
            <div class="card-value error">{{ securityOverview.failedLogins }}</div>
            <div class="card-trend warning">↑ 15%</div>
          </div>
          <div class="overview-card">
            <h4>异常访问</h4>
            <div class="card-value warning">{{ securityOverview.abnormalAccess }}</div>
          </div>
          <div class="overview-card">
            <h4>安全事件</h4>
            <div class="card-value error">{{ securityOverview.securityEvents }}</div>
          </div>
        </div>

        <!-- 安全事件列表 -->
        <div class="section">
          <h3 class="section-title">最近安全事件</h3>
          <el-table :data="securityEvents" style="width: 100%">
            <el-table-column prop="time" label="时间" width="160" />
            <el-table-column prop="type" label="事件类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getEventType(row.type)" size="small">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="user" label="用户/IP" width="150" />
            <el-table-column prop="description" label="事件描述" min-width="200" />
            <el-table-column prop="level" label="风险级别" width="100">
              <template #default="{ row }">
                <el-tag :type="getRiskType(row.level)" size="small">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="处理状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getHandleType(row.status)" size="small">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click="handleSecurityEvent(row)">处理</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const activeTab = ref('activity')
const dateRange = ref<[Date, Date] | null>(null)

// 用户活跃度数据
const userOverview = ref({
  totalUsers: 1248,
  todayActiveUsers: 356,
  onlineUsers: 156,
  avgSessionTime: 28
})

const activityTrend = ref([
  { date: '01-04', users: 245, percentage: 70 },
  { date: '01-05', users: 289, percentage: 82 },
  { date: '01-06', users: 312, percentage: 89 },
  { date: '01-07', users: 298, percentage: 85 },
  { date: '01-08', users: 334, percentage: 95 },
  { date: '01-09', users: 356, percentage: 100 },
  { date: '01-10', users: 342, percentage: 97 }
])

const moduleUsage = ref([
  { module: '数据源管理', visits: 2340, users: 156, avgTime: '12分钟', percentage: 85, trend: '↑ 12%' },
  { module: '接口管理', visits: 1890, users: 134, avgTime: '8分钟', percentage: 68, trend: '↑ 8%' },
  { module: '客户端设置', visits: 1560, users: 98, avgTime: '15分钟', percentage: 56, trend: '↓ 3%' },
  { module: '系统设置', visits: 1230, users: 87, avgTime: '20分钟', percentage: 44, trend: '↑ 5%' },
  { module: '日志管理', visits: 980, users: 76, avgTime: '6分钟', percentage: 35, trend: '↑ 15%' }
])

// 客户端管理数据
const clientOverview = ref({
  totalClients: 45,
  activeClients: 38,
  totalTokens: 67,
  expiringTokens: 5
})

const clientStatus = ref([
  { name: 'WebApp-Frontend', type: 'Web应用', status: 'active', lastActive: '2024-01-10 14:25', tokenExpiry: '2024-02-10', apiCalls: 2340 },
  { name: 'Mobile-iOS', type: '移动应用', status: 'active', lastActive: '2024-01-10 14:20', tokenExpiry: '2024-01-15', apiCalls: 1890 },
  { name: 'DataSync-Service', type: '后台服务', status: 'active', lastActive: '2024-01-10 14:18', tokenExpiry: '2024-03-01', apiCalls: 5670 },
  { name: 'Report-Generator', type: '报表工具', status: 'inactive', lastActive: '2024-01-09 16:30', tokenExpiry: '2024-01-12', apiCalls: 234 }
])

// 安全监控数据
const securityOverview = ref({
  todayLogins: 456,
  failedLogins: 23,
  abnormalAccess: 5,
  securityEvents: 3
})

const securityEvents = ref([
  { time: '2024-01-10 14:25', type: '登录失败', user: '*************', description: '连续5次密码错误', level: '中风险', status: '已处理' },
  { time: '2024-01-10 13:45', type: '异常访问', user: 'user123', description: '非工作时间大量API调用', level: '高风险', status: '处理中' },
  { time: '2024-01-10 12:30', type: '权限异常', user: 'admin001', description: '尝试访问未授权接口', level: '低风险', status: '已忽略' }
])

// 方法
const getTrendClass = (trend: string) => {
  if (trend.includes('↑')) return 'success'
  if (trend.includes('↓')) return 'error'
  return 'warning'
}

const getStatusType = (status: string) => {
  return status === 'active' ? 'success' : 'danger'
}

const getStatusText = (status: string) => {
  return status === 'active' ? '在线' : '离线'
}

const getEventType = (type: string) => {
  const types: Record<string, string> = {
    '登录失败': 'warning',
    '异常访问': 'danger',
    '权限异常': 'info'
  }
  return types[type] || 'info'
}

const getRiskType = (level: string) => {
  const types: Record<string, string> = {
    '高风险': 'danger',
    '中风险': 'warning',
    '低风险': 'success'
  }
  return types[level] || 'info'
}

const getHandleType = (status: string) => {
  const types: Record<string, string> = {
    '已处理': 'success',
    '处理中': 'warning',
    '已忽略': 'info'
  }
  return types[status] || 'info'
}

const handleDateChange = (dates: [Date, Date] | null) => {
  if (dates) {
    ElMessage.info(`查询时间范围: ${dates[0].toLocaleDateString()} - ${dates[1].toLocaleDateString()}`)
  }
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const manageClient = (client: any) => {
  ElMessage.info(`管理客户端: ${client.name}`)
}

const refreshToken = (client: any) => {
  ElMessage.info(`刷新Token: ${client.name}`)
}

const handleSecurityEvent = (event: any) => {
  ElMessage.info(`处理安全事件: ${event.type}`)
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
/* 用户分析页面特有样式 */

/* 重复的页签和表格样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 重复的按钮和滚动条样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.overview-card h4 {
  margin: 0 0 10px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-value.success {
  color: #10b981;
}

.card-value.warning {
  color: #f59e0b;
}

.card-value.error {
  color: #ef4444;
}

.card-trend {
  font-size: 12px;
  font-weight: 500;
}

.card-trend.success {
  color: #10b981;
}

.card-trend.warning {
  color: #f59e0b;
}

.card-trend.error {
  color: #ef4444;
}

/* 区域样式 */
.section {
  margin-bottom: 30px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #374151;
  font-weight: 600;
}

/* 活跃度图表 */
.activity-chart {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.chart-container {
  display: flex;
  align-items: end;
  gap: 15px;
  height: 200px;
}

.chart-day {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-bar-container {
  height: 150px;
  display: flex;
  align-items: end;
  width: 100%;
  justify-content: center;
}

.chart-bar {
  width: 30px;
  background: linear-gradient(135deg, #3FC8DD 0%, #2c6fbb 100%);
  border-radius: 4px 4px 0 0;
  min-height: 10px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(135deg, #35b3c7 0%, #1e5a96 100%);
}

.chart-label {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.chart-value {
  margin-top: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

/* 百分比进度条 */
.percentage-bar {
  position: relative;
  width: 100px;
  height: 20px;
  background: #f3f4f6;
  border-radius: 10px;
  overflow: hidden;
}

.percentage-fill {
  height: 100%;
  background: linear-gradient(135deg, #3FC8DD 0%, #2c6fbb 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.percentage-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

/* 表格中的状态样式 */
.success {
  color: #10b981;
  font-weight: 500;
}

.warning {
  color: #f59e0b;
  font-weight: 500;
}

.error {
  color: #ef4444;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-container {
    gap: 8px;
  }

  .chart-bar {
    width: 20px;
  }
}

@media (max-width: 480px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 150px;
  }

  .chart-bar-container {
    height: 100px;
  }
}
</style>
