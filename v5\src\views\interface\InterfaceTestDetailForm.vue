<template>
  <div class="test-detail-container">
    <!-- 内容区域 -->
    <div class="content-area custom-scrollbar">
      <div v-if="selectedRecord" class="test-detail-content">
        <!-- 接口基本信息 -->
        <div class="api-info-section">
          <div class="api-header">
            <div class="api-method-path">
              <el-tag
                :color="getMethodColor(selectedRecord.interface_method || 'GET')"
                size="large"
                class="method-tag"
              >
                {{ selectedRecord.interface_method }}
              </el-tag>
              <span class="api-path">{{ selectedRecord.interface_path }}</span>
            </div>
            <div class="api-name">{{ selectedRecord.interface_name }}</div>
          </div>

          <!-- 测试状态和基本信息 -->
          <div class="test-status-info">
            <div class="status-item">
              <span class="status-label">测试状态:</span>
              <el-tag
                :type="selectedRecord.success ? 'success' : 'danger'"
                size="large"
              >
                {{ selectedRecord.success ? '成功' : '失败' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">状态码:</span>
              <el-tag
                :type="getStatusCodeType(selectedRecord.status_code)"
                size="large"
              >
                {{ selectedRecord.status_code }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">响应时间:</span>
              <span class="status-value">{{ selectedRecord.response_time }}ms</span>
            </div>
            <div class="status-item">
              <span class="status-label">测试时间:</span>
              <span class="status-value">{{ selectedRecord.test_time }}</span>
            </div>
          </div>
        </div>

        <!-- 请求和响应内容 -->
        <div class="detail-content">
          <el-tabs v-model="detailActiveTab" class="detail-tabs">
            <!-- 请求信息 -->
            <el-tab-pane label="Request" name="request">
              <div class="request-section">
                <!-- 请求参数 -->
                <div class="param-section" v-if="selectedRecord.test_params && Object.keys(selectedRecord.test_params).length > 0">
                  <div class="param-title">Query Parameters</div>
                  <div class="param-table">
                    <div class="param-header">
                      <span class="param-name-col">参数名</span>
                      <span class="param-value-col">参数值</span>
                      <span class="param-type-col">类型</span>
                    </div>
                    <div
                      v-for="(value, key) in selectedRecord.test_params"
                      :key="key"
                      class="param-row"
                    >
                      <span class="param-name">{{ key }}</span>
                      <span class="param-value">{{ formatParamValue(value) }}</span>
                      <span class="param-type">{{ getParamType(value) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 请求头 -->
                <div class="headers-section" v-if="selectedRecord.test_headers && Object.keys(selectedRecord.test_headers).length > 0">
                  <div class="headers-title">Request Headers</div>
                  <div class="headers-table">
                    <div class="headers-header">
                      <span class="header-name-col">Header Name</span>
                      <span class="header-value-col">Header Value</span>
                    </div>
                    <div
                      v-for="(value, key) in selectedRecord.test_headers"
                      :key="key"
                      class="header-row"
                    >
                      <span class="header-name">{{ key }}</span>
                      <span class="header-value">{{ value }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 响应信息 -->
            <el-tab-pane label="Response" name="response">
              <div class="response-section">
                <!-- 响应体 -->
                <div class="response-body-section">
                  <div class="response-toolbar">
                    <span class="response-title">Response Body</span>
                    <div class="toolbar-actions">
                      <el-button size="small" @click="formatDetailJson">格式化JSON</el-button>
                      <el-button size="small" @click="copyDetailResponse">复制响应</el-button>
                    </div>
                  </div>
                  <div class="response-content">
                    <el-input
                      v-model="detailFormattedResponse"
                      type="textarea"
                      :rows="20"
                      readonly
                      class="response-textarea"
                    />
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 响应头 -->
            <el-tab-pane label="Response Headers" name="response-headers">
              <div class="response-headers-section">
                <div class="headers-table">
                  <div class="headers-header">
                    <span class="header-name-col">Header Name</span>
                    <span class="header-value-col">Header Value</span>
                  </div>
                  <div
                    v-for="(value, key) in selectedRecord.response_headers"
                    :key="key"
                    class="header-row"
                  >
                    <span class="header-name">{{ key }}</span>
                    <span class="header-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 测试信息 -->
            <el-tab-pane label="Test Info" name="test-info">
              <div class="test-info">
                <div class="info-item">
                  <span class="info-label">状态码:</span>
                  <span class="info-value">{{ selectedRecord.status_code }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">响应时间:</span>
                  <span class="info-value">{{ selectedRecord.response_time }}ms</span>
                </div>
                <div class="info-item">
                  <span class="info-label">响应大小:</span>
                  <span class="info-value">{{ getResponseSize(selectedRecord.response_data) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">测试时间:</span>
                  <span class="info-value">{{ selectedRecord.test_time }}</span>
                </div>
                <div class="info-item" v-if="selectedRecord.error_message">
                  <span class="info-label">错误信息:</span>
                  <span class="info-value error-message">{{ selectedRecord.error_message }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 底部按钮已抽象到MainIndex中统一管理 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';


// 抽屉状态管理
const globalDrawerStore = useGlobalDrawerStore();
const drawerMessenger = useGlobalDrawerMessenger();

// 响应式数据
const detailActiveTab = ref('request');
const detailFormattedResponse = ref('');

// 计算属性 - 使用第一层抽屉的数据
const selectedRecord = computed(() => globalDrawerStore.props.editData);

// 加载测试详情
const loadTestDetail = () => {
  if (selectedRecord.value) {
    detailActiveTab.value = 'request';
    // 格式化响应数据
    if (selectedRecord.value.response_data) {
      detailFormattedResponse.value = JSON.stringify(selectedRecord.value.response_data, null, 2);
    }
  }
};

// 关闭抽屉
const handleClose = () => {
  drawerMessenger.hideDrawer(); // 关闭第一层抽屉
};

// 更新抽屉底部按钮配置
const updateDrawerButtons = () => {
  const rightButtons = [
    {
      text: '关闭',
      handler: handleClose
    }
  ];

  // 更新store中的按钮配置
  globalDrawerStore.leftButtons = []; // 测试详情没有左侧按钮
  globalDrawerStore.rightButtons = rightButtons;
};

// 监听第一层抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.editData) {
    loadTestDetail();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 获取HTTP方法颜色
const getMethodColor = (method: string): string => {
  const colors: Record<string, string> = {
    'GET': '#67C23A',
    'POST': '#409EFF',
    'PUT': '#E6A23C',
    'DELETE': '#F56C6C',
    'PATCH': '#909399'
  };
  return colors[method.toUpperCase()] || '#909399';
};

// 获取状态码类型
const getStatusCodeType = (statusCode: number): string => {
  if (statusCode >= 200 && statusCode < 300) return 'success';
  if (statusCode >= 400 && statusCode < 500) return 'warning';
  if (statusCode >= 500) return 'danger';
  return 'info';
};

// 格式化参数值
const formatParamValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
};

// 获取参数类型
const getParamType = (value: any): string => {
  if (value === null || value === undefined) {
    return 'null';
  }
  if (Array.isArray(value)) {
    return 'array';
  }
  return typeof value;
};

// 获取响应大小
const getResponseSize = (data: any): string => {
  const size = JSON.stringify(data).length;
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
  return `${(size / (1024 * 1024)).toFixed(1)}MB`;
};

// 格式化详情JSON
const formatDetailJson = () => {
  try {
    if (selectedRecord.value?.response_data) {
      detailFormattedResponse.value = JSON.stringify(selectedRecord.value.response_data, null, 2);
    }
  } catch (error) {
    ElMessage.error('JSON格式化失败');
  }
};

// 复制详情响应
const copyDetailResponse = async () => {
  try {
    await navigator.clipboard.writeText(detailFormattedResponse.value);
    ElMessage.success('响应内容已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 重复的函数定义已移至前面

onMounted(() => {
  loadTestDetail();
  updateDrawerButtons(); // 初始化按钮配置
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.test-detail-container {
  /* 抽屉容器布局 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  
  .content-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 20px; /* 底部按钮已抽象到MainIndex中统一管理 */
  }

  /* 2024-12-27: 滚动条样式已使用 page-common.scss 中的 .custom-scrollbar 公共样式 */
  /*
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #9db7bd;
    border-radius: 2px;

    &:hover {
      background: #7a9ca3;
    }
  }

  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
  */
}

/* 接口信息样式 */
.api-info-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;

  .api-header {
    .api-method-path {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      .method-tag {
        font-weight: bold;
        color: white;
        border: none;
      }

      .api-path {
        font-family: 'Courier New', monospace;
        font-size: 16px;
        font-weight: 500;
        color: #374151;
      }
    }

    .api-name {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
    }
  }

  .test-status-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-label {
        font-weight: 500;
        color: #6b7280;
        min-width: 80px;
      }

      .status-value {
        font-weight: 500;
        color: #374151;
      }
    }
  }
}

/* 详情内容样式 */
.detail-content {
  .detail-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
      background: #f8fafc;
      border-radius: 8px;
      padding: 4px;
    }

    :deep(.el-tabs__item) {
      border-radius: 6px;
      margin: 0 2px;

      &.is-active {
        background: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* 参数表格样式 */
.param-section, .headers-section {
  margin-bottom: 24px;

  .param-title, .headers-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
  }
}

.param-table, .headers-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;

  .param-header, .headers-header {
    display: grid;
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    padding: 12px;
    border-bottom: 1px solid #e5e7eb;
  }

  .param-header {
    grid-template-columns: 1fr 2fr 100px;
  }

  .headers-header {
    grid-template-columns: 1fr 2fr;
  }

  .param-row, .header-row {
    display: grid;
    padding: 12px;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f9fafb;
    }
  }

  .param-row {
    grid-template-columns: 1fr 2fr 100px;
  }

  .header-row {
    grid-template-columns: 1fr 2fr;
  }

  .param-name, .header-name {
    font-weight: 500;
    color: #374151;
    font-family: 'Courier New', monospace;
  }

  .param-value, .header-value {
    color: #6b7280;
    word-break: break-all;
  }

  .param-type {
    font-size: 12px;
    color: #9ca3af;
    text-transform: uppercase;
  }
}

/* 响应样式 */
.response-section {
  .response-body-section {
    .response-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;

      .response-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }

      .toolbar-actions {
        display: flex;
        gap: 8px;
      }
    }

    .response-content {
      .response-textarea {
        :deep(.el-textarea__inner) {
          font-family: 'Courier New', monospace;
          font-size: 13px;
          line-height: 1.5;
          border-radius: 8px;
          border: 1px solid #e5e7eb;

          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
    }
  }
}

/* 测试信息样式 */
.test-info {
  .info-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-weight: 500;
      color: #6b7280;
      min-width: 120px;
    }

    .info-value {
      color: #374151;
      font-weight: 500;

      &.error-message {
        color: #dc2626;
        font-family: 'Courier New', monospace;
      }
    }
  }
}
</style>
