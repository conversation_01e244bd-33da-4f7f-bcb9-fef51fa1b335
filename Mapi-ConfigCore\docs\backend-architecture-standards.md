# 后端架构分层规范文档

## 📋 概述

本文档基于接口分组管理和数据源管理模块的检查分析，制定了后端架构的分层规范和字段命名标准，供后续模块开发参考。

## 🏗️ 分层架构规范

### 1. Router层（路由层）
**职责**：定义API路由和端点
**规范**：
- ✅ 只负责路由定义和API文档
- ✅ 参数验证（使用FastAPI的Query、Path等）
- ✅ 直接调用Controller层方法
- ❌ 不允许包含业务逻辑
- ❌ 不允许直接操作数据库
- ❌ 不允许进行数据转换

**示例**：
```python
@router.get("/", response_model=InterfaceGroupListResponse)
async def get_interface_groups(
    page: int = Query(1, ge=1, description="页码"),
    controller: InterfaceGroupController = Depends()
):
    return await controller.get_interface_groups(page, size, search)
```

### 2. Controller层（控制器层）
**职责**：处理HTTP请求和响应
**规范**：
- ✅ 只负责HTTP请求处理
- ✅ 参数接收和传递
- ✅ 直接调用Service层方法
- ❌ 不允许包含业务逻辑
- ❌ 不允许直接操作数据库
- ❌ 不允许进行数据转换

**示例**：
```python
async def get_interface_groups(self, page: int, size: int, search: str):
    return self.service.get_interface_groups(page, size, search)
```

### 3. Service层（业务逻辑层）
**职责**：处理业务逻辑
**规范**：
- ✅ 包含所有业务逻辑和验证
- ✅ 调用Repository层进行数据操作
- ✅ 使用`Schema.from_orm()`进行数据转换
- ✅ 异常处理和日志记录
- ❌ 不允许手动进行字段映射
- ❌ 不允许直接操作数据库

**示例**：
```python
def get_interface_groups(self, page: int, size: int, search: str):
    # 业务验证
    if page < 1:
        raise BusinessException("页码必须大于0")
    
    # 获取数据
    items, total = self.repository.get_list(page, size, search)
    
    # 转换为响应格式（使用from_orm）
    groups = []
    for item in items:
        group_response = InterfaceGroupResponse.from_orm(item)
        groups.append(group_response)
    
    return InterfaceGroupListResponse(items=groups, total=total, ...)
```

### 4. Repository层（数据访问层）
**职责**：数据库CRUD操作
**规范**：
- ✅ 只负责数据库访问
- ✅ 直接使用Schema进行数据操作
- ✅ 返回Model对象
- ❌ 不允许包含业务逻辑
- ❌ 不允许进行数据转换

**示例**：
```python
def create(self, group_data: InterfaceGroupCreate) -> InterfaceGroupModel:
    db_group = InterfaceGroupModel(
        name=group_data.name,
        path_prefix=group_data.path_prefix,
        # ... 其他字段
    )
    self.db.add(db_group)
    self.db.commit()
    return db_group
```

### 5. Schema层（数据模式层）
**职责**：定义数据结构和验证规则
**规范**：
- ✅ 统一使用snake_case字段命名
- ✅ 配置`from_attributes=True`支持ORM转换
- ✅ 完整的字段验证和文档
- ❌ 不允许使用camelCase字段名
- ❌ 不允许在Schema中进行字段转换

**示例**：
```python
class InterfaceGroupResponse(InterfaceGroupBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    interface_count: Optional[int] = Field(None, description="包含的接口数量")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
```

### 6. Model层（数据模型层）
**职责**：定义数据库表结构
**规范**：
- ✅ 统一使用snake_case字段命名
- ✅ 与数据库表结构保持一致
- ✅ 如有`to_dict`方法，必须保持snake_case命名
- ❌ 不允许在Model中进行字段转换
- ❌ 不允许为了"前端兼容"而使用camelCase

**示例**：
```python
class InterfaceGroupModel(Base):
    __tablename__ = "interface_groups"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    path_prefix = Column(String(50), nullable=False)  # snake_case
    is_enabled = Column(Boolean, default=True)        # snake_case
    created_at = Column(DateTime, default=func.now()) # snake_case
```

## 🔤 字段命名规范

### 后端统一使用snake_case
**所有后端层次必须统一使用snake_case命名**：
- ✅ `path_prefix`
- ✅ `is_enabled`
- ✅ `interface_count`
- ✅ `created_at`
- ✅ `updated_at`
- ✅ `created_by`

### 前端转换处理
**前端负责字段转换**：
- 后端API统一返回snake_case
- 前端使用`convertToCamelCase`转换为camelCase
- 前端发送请求时使用`convertToSnakeCase`转换为snake_case

## ❌ 常见错误和禁止事项

### 1. 字段命名混用
```python
# ❌ 错误：在Service层手动映射字段
group_response = InterfaceGroupResponse(
    pathPrefix=group.path_prefix,  # 混用camelCase
    isEnabled=group.is_enabled     # 混用camelCase
)

# ✅ 正确：使用from_orm自动映射
group_response = InterfaceGroupResponse.from_orm(group)
```

### 2. 层次职责混乱
```python
# ❌ 错误：在Controller中包含业务逻辑
async def create_interface_group(self, group_data):
    if self.repository.get_by_name(group_data.name):  # 不应该在Controller中
        raise Exception("名称已存在")
    return self.repository.create(group_data)

# ✅ 正确：Controller只负责调用Service
async def create_interface_group(self, group_data):
    return self.service.create_interface_group(group_data)
```

### 3. Model中的错误转换
```python
# ❌ 错误：Model中转换为camelCase
def to_dict(self):
    return {
        'pathPrefix': self.path_prefix,  # 错误的camelCase转换
        'isEnabled': self.is_enabled
    }

# ✅ 正确：Model保持snake_case
def to_dict(self):
    return {
        'path_prefix': self.path_prefix,  # 保持snake_case
        'is_enabled': self.is_enabled
    }
```

## 📊 检查结果对比

### 接口分组管理模块 ✅
- Router层：职责清晰 ✅
- Controller层：职责清晰 ✅
- Service层：使用from_orm ✅
- Repository层：直接操作Schema ✅
- Schema层：统一snake_case ✅
- Model层：统一snake_case ✅
- API输出：统一snake_case ✅

### 数据源管理模块 ✅
- Router层：职责清晰 ✅
- Controller层：职责清晰 ✅
- Service层：使用from_orm ✅
- Repository层：直接操作Schema ✅
- Schema层：统一snake_case ✅
- Model层：统一snake_case ✅（已修复to_dict方法）
- API输出：统一snake_case ✅

## 🎯 新模块开发指南

### 1. 创建新模块时
1. 严格按照分层架构创建文件
2. 每层只负责自己的职责
3. 统一使用snake_case字段命名
4. Schema配置`from_attributes=True`
5. Service层使用`from_orm`进行转换

### 2. 代码审查检查点
- [ ] 是否有层次职责混乱
- [ ] 是否有字段命名混用
- [ ] 是否有手动字段映射
- [ ] 是否正确使用from_orm
- [ ] API输出是否统一snake_case

### 3. 测试验证
- 使用API测试工具验证所有端点
- 确保返回字段都是snake_case
- 检查是否有camelCase字段泄露

## 📝 总结

通过对接口分组管理和数据源管理模块的深入分析，我们建立了清晰的分层架构规范：

1. **层次清晰**：每层职责明确，无越界处理
2. **字段统一**：全栈统一使用snake_case
3. **转换标准**：后端统一输出，前端统一转换
4. **质量保证**：通过规范和检查确保一致性

这套规范将确保后续模块开发的一致性和可维护性。
