<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Lock /></el-icon>
      安全设置
    </h2>
    
    <div class="content-container">
      <el-tabs v-model="activeTab" class="security-tabs">
        <!-- IP访问控制 -->
        <el-tab-pane label="IP访问控制" name="ip">
          <div class="section-title">
            <el-icon><Monitor /></el-icon>
            IP白名单管理
          </div>
          
          <div class="config-card">
            <el-form :model="ipConfig" label-width="120px">
              <el-form-item label="启用IP控制">
                <el-switch v-model="ipConfig.enabled"></el-switch>
              </el-form-item>
              
              <el-form-item label="默认策略">
                <el-radio-group v-model="ipConfig.defaultPolicy">
                  <el-radio value="allow">允许所有</el-radio>
                  <el-radio value="deny">拒绝所有</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            
            <div class="ip-list">
              <div class="list-header">
                <span>IP地址列表</span>
                <el-button type="primary" size="small" @click="showAddIpDialog">添加IP</el-button>
              </div>
              
              <el-table :data="ipList" style="width: 100%">
                <el-table-column prop="ip" label="IP地址" width="200"></el-table-column>
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.type === 'allow' ? 'success' : 'danger'">
                      {{ scope.row.type === 'allow' ? '允许' : '拒绝' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="editIp(scope.row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteIp(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- SQL白名单 -->
        <el-tab-pane label="SQL白名单" name="sql">
          <div class="section-title">
            <el-icon><DocumentChecked /></el-icon>
            SQL白名单管理
          </div>
          
          <div class="config-card">
            <el-form :model="sqlConfig" label-width="120px">
              <el-form-item label="启用SQL检查">
                <el-switch v-model="sqlConfig.enabled"></el-switch>
              </el-form-item>
              
              <el-form-item label="检查模式">
                <el-radio-group v-model="sqlConfig.mode">
                  <el-radio value="whitelist">白名单模式</el-radio>
                  <el-radio value="blacklist">黑名单模式</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            
            <div class="sql-rules">
              <div class="list-header">
                <span>SQL规则列表</span>
                <el-button type="primary" size="small" @click="showAddSqlDialog">添加规则</el-button>
              </div>
              
              <el-table :data="sqlRules" style="width: 100%">
                <el-table-column prop="pattern" label="SQL模式" min-width="300"></el-table-column>
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.type === 'allow' ? 'success' : 'danger'">
                      {{ scope.row.type === 'allow' ? '允许' : '拒绝' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button type="primary" size="small" @click="editSqlRule(scope.row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteSqlRule(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 流量控制 -->
        <el-tab-pane label="流量控制" name="rate">
          <div class="section-title">
            <el-icon><Timer /></el-icon>
            流量限制配置
          </div>
          
          <div class="config-card">
            <el-form :model="rateConfig" label-width="150px">
              <el-form-item label="启用流量控制">
                <el-switch v-model="rateConfig.enabled"></el-switch>
              </el-form-item>
              
              <el-form-item label="全局请求限制">
                <el-input-number v-model="rateConfig.globalLimit" :min="1" :max="10000"></el-input-number>
                <span style="margin-left: 10px;">次/分钟</span>
              </el-form-item>
              
              <el-form-item label="单IP请求限制">
                <el-input-number v-model="rateConfig.ipLimit" :min="1" :max="1000"></el-input-number>
                <span style="margin-left: 10px;">次/分钟</span>
              </el-form-item>
              
              <el-form-item label="API请求限制">
                <el-input-number v-model="rateConfig.apiLimit" :min="1" :max="1000"></el-input-number>
                <span style="margin-left: 10px;">次/分钟</span>
              </el-form-item>
              
              <el-form-item label="超限处理">
                <el-radio-group v-model="rateConfig.action">
                  <el-radio value="block">阻止请求</el-radio>
                  <el-radio value="delay">延迟响应</el-radio>
                  <el-radio value="log">仅记录日志</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- Token配置 -->
        <el-tab-pane label="Token配置" name="token">
          <div class="section-title">
            <el-icon><Key /></el-icon>
            Token安全配置
          </div>
          
          <div class="config-card">
            <el-form :model="tokenConfig" label-width="150px">
              <el-form-item label="Access Token过期时间">
                <el-input-number v-model="tokenConfig.accessTokenExpiry" :min="1" :max="24"></el-input-number>
                <span style="margin-left: 10px;">小时</span>
              </el-form-item>
              
              <el-form-item label="Refresh Token过期时间">
                <el-input-number v-model="tokenConfig.refreshTokenExpiry" :min="1" :max="365"></el-input-number>
                <span style="margin-left: 10px;">天</span>
              </el-form-item>
              
              <el-form-item label="Token加密算法">
                <el-select v-model="tokenConfig.algorithm">
                  <el-option label="HS256" value="HS256"></el-option>
                  <el-option label="HS384" value="HS384"></el-option>
                  <el-option label="HS512" value="HS512"></el-option>
                  <el-option label="RS256" value="RS256"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="强制Token刷新">
                <el-switch v-model="tokenConfig.forceRefresh"></el-switch>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 保存按钮 -->
      <div class="save-actions">
        <el-button type="primary" @click="saveConfig" size="large">保存配置</el-button>
        <el-button @click="resetConfig" size="large">重置配置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Lock, Monitor, DocumentChecked, Timer, Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('ip')

// IP配置
const ipConfig = ref({
  enabled: true,
  defaultPolicy: 'allow'
})

// IP列表
const ipList = ref([
  { ip: '***********/24', type: 'allow', description: '内网IP段' },
  { ip: '10.0.0.0/8', type: 'allow', description: '私有网络' },
  { ip: '**********/12', type: 'deny', description: '受限网段' }
])

// SQL配置
const sqlConfig = ref({
  enabled: true,
  mode: 'whitelist'
})

// SQL规则
const sqlRules = ref([
  { pattern: 'SELECT * FROM users WHERE id = ?', type: 'allow', description: '用户查询' },
  { pattern: 'DROP TABLE *', type: 'deny', description: '禁止删除表' },
  { pattern: 'DELETE FROM *', type: 'deny', description: '禁止删除数据' }
])

// 流量控制配置
const rateConfig = ref({
  enabled: true,
  globalLimit: 1000,
  ipLimit: 100,
  apiLimit: 50,
  action: 'block'
})

// Token配置
const tokenConfig = ref({
  accessTokenExpiry: 2,
  refreshTokenExpiry: 30,
  algorithm: 'HS256',
  forceRefresh: false
})

// 显示添加IP对话框
const showAddIpDialog = () => {
  ElMessage.info('添加IP功能开发中...')
}

// 编辑IP
const editIp = (ip: any) => {
  ElMessage.info('编辑IP功能开发中...')
}

// 删除IP
const deleteIp = (index: number) => {
  ipList.value.splice(index, 1)
  ElMessage.success('IP删除成功')
}

// 显示添加SQL规则对话框
const showAddSqlDialog = () => {
  ElMessage.info('添加SQL规则功能开发中...')
}

// 编辑SQL规则
const editSqlRule = (rule: any) => {
  ElMessage.info('编辑SQL规则功能开发中...')
}

// 删除SQL规则
const deleteSqlRule = (index: number) => {
  sqlRules.value.splice(index, 1)
  ElMessage.success('SQL规则删除成功')
}

// 保存配置
const saveConfig = () => {
  ElMessage.success('安全配置保存成功')
}

// 重置配置
const resetConfig = () => {
  ElMessage.info('配置已重置')
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary);
  padding-left: 12px;
}

.config-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
}

.save-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.security-tabs {
  margin-bottom: 20px;
}
</style>
