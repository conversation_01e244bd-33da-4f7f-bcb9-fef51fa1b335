---
type: "agent_requested"
description: "Example description"
---
# 编码规范

## TypeScript/JavaScript 规范

### 1. 函数定义规范 ⚠️ 强制执行

**规则：禁止使用 `function` 关键字，必须使用箭头函数**

#### ❌ 错误示例
```typescript
// 禁止使用 function 关键字
export function formatDate(time: number): string {
  return new Date(time).toLocaleDateString();
}

function handleClick() {
  console.log('clicked');
}

// 禁止使用函数表达式
const myFunction = function(param: string) {
  return param.toUpperCase();
};
```

#### ✅ 正确示例
```typescript
// 使用箭头函数
export const formatDate = (time: number): string => {
  return new Date(time).toLocaleDateString();
};

const handleClick = () => {
  console.log('clicked');
};

// 简化的箭头函数
const myFunction = (param: string) => param.toUpperCase();

// 类方法保持原样（不受此规则影响）
class MyClass {
  async getData() {
    return await fetch('/api/data');
  }
}
```

#### 规则说明
- **适用范围**：所有独立函数、导出函数、变量赋值的函数
- **不适用**：类方法、接口方法定义、类型声明中的函数签名
- **原因**：
  - 保持代码风格一致性
  - 箭头函数具有词法作用域绑定
  - 更简洁的语法
  - 符合现代 JavaScript/TypeScript 最佳实践

### 2. ESLint 配置

项目已配置 ESLint 规则来强制执行此规范：

```javascript
rules: {
  // 强制使用箭头函数，禁止使用 function 关键字
  'func-style': ['error', 'expression', { 
    'allowArrowFunctions': true 
  }],
  
  // 优先使用箭头函数作为回调
  'prefer-arrow-callback': ['error', {
    'allowNamedFunctions': false,
    'allowUnboundThis': true
  }]
}
```

### 3. 检查命令

```bash
# 检查代码规范
npm run lint:check

# 自动修复可修复的问题
npm run lint
```

## Vue 组件规范

### 1. 模板语法规范

#### 模板字符串中的引号使用
```vue
<!-- ❌ 错误：模板字符串内使用双引号会导致语法冲突 -->
<EmptyState :description="`没有找到包含"${searchQuery}"的数据`" />

<!-- ✅ 正确：使用中文引号或单引号 -->
<EmptyState :description="`没有找到包含「${searchQuery}」的数据`" />
<EmptyState :description="`没有找到包含'${searchQuery}'的数据`" />
```

### 2. 空组件规范 ⚠️ 强制执行

**规则：所有数据列表页面必须添加空组件**

#### ✅ 标准实现
```vue
<el-table :data="tableData">
  <!-- 空状态 -->
  <template #empty>
    <el-empty
      description="暂无数据"
      :image-size="120"
    >
      <template #description>
        <p>描述性文字</p>
        <p>引导性提示</p>
      </template>
      <el-button type="primary" @click="handleAction">
        <el-icon><Plus /></el-icon>
        操作按钮
      </el-button>
    </el-empty>
  </template>
</el-table>
```

#### 必需元素
- **描述性文字**：说明当前状态
- **引导性提示**：告诉用户下一步操作
- **操作按钮**：提供快速操作入口（如适用）
- **图标导入**：确保导入所需图标

#### 图标导入示例
```typescript
import { Plus, User, Refresh } from '@element-plus/icons-vue'
```

### 3. 样式导入规范

```vue
<style lang="scss" scoped>
/* ❌ 禁止导入框架级样式 */
@import '@/assets/styles/framework.scss';

/* ✅ 正确：导入页面通用样式 */
@use '@/assets/styles/page-common.scss' as *;

/* ✅ 正确：导入组件专用样式 */
@import '@/assets/styles/component-name.scss';
</style>
```

## 工具类使用规范

### 1. 通用工具导入

```typescript
// 字段转换
import { convertToCamelCase, convertToSnakeCase } from '@/utils/common-utils';

// 密码处理
import { PasswordUtils } from '@/utils/common-utils';

// 完整导入
import {
  convertToCamelCase,
  convertToSnakeCase,
  PasswordUtils,
  apiRequest,
  convertArray
} from '@/utils/common-utils';
```

### 2. API 数据转换

```typescript
// API响应数据转换
return convertToCamelCase(data);

// API请求数据转换
body: JSON.stringify(convertToSnakeCase(data));

// 密码处理（安全方案）
const processedData = await PasswordUtils.processDataSourcePassword(data);
```

## 密码安全规范 🔒

### 1. 安全原则
- **编辑时不显示原密码**：出于安全考虑，编辑表单中密码字段始终为空
- **用户重新输入**：如需修改密码，用户必须重新输入
- **留空保持不变**：编辑时密码留空则保持原密码不变

### 2. 实现方式

```vue
<!-- 密码字段 -->
<el-form-item label="密码" prop="password" :required="!isEdit">
  <el-input
    v-model="formData.password"
    type="password"
    :placeholder="isEdit ? '如需修改密码请重新输入，留空保持原密码不变' : '请输入数据库密码'"
    show-password
  />
  <div v-if="isEdit" class="form-item-tip">
    出于安全考虑，编辑时不显示原密码。如需修改密码请重新输入，留空则保持原密码不变。
  </div>
</el-form-item>
```

### 3. 提交逻辑

```typescript
if (isEdit.value && editData.value) {
  const updateData: any = { ...formData };

  // 如果密码为空，则不更新密码字段（保持原密码不变）
  if (!updateData.password || updateData.password.trim() === '') {
    delete updateData.password;
  }

  await service.updateData(editData.value.id, updateData);
}
```

### 4. 验证规则

```typescript
// 动态验证规则：编辑模式下密码不是必填的
const formRules = computed(() => ({
  password: isEdit.value
    ? [] // 编辑模式下密码不是必填的
    : [{ required: true, message: '请输入密码', trigger: 'blur' }]
}));
```

## 文件命名规范

### 1. 组件文件
- 页面组件：`PascalCase.vue`
- 表单组件：`PascalCaseForm.vue`
- 抽屉组件：`PascalCaseDrawer.vue`

### 2. 工具文件
- 服务文件：`kebab-case.service.ts`
- 工具文件：`kebab-case.ts`
- 类型文件：`kebab-case.ts`

### 3. 样式文件
- 页面样式：`page-common.scss`
- 组件样式：`component-name.scss`
- 框架样式：`framework.scss`（禁止修改）

## 代码质量规范

### 1. 类型安全
- 所有函数参数和返回值必须有明确的类型注解
- 避免使用 `any` 类型，优先使用具体类型或泛型

### 2. 错误处理
- 所有异步操作必须有错误处理
- 使用统一的错误处理工具函数

### 3. 注释规范
- 所有导出的函数必须有 JSDoc 注释
- 复杂逻辑必须有行内注释说明

## 检查清单

在提交代码前，请确保：

### 基础规范
- [ ] 所有函数都使用箭头函数语法
- [ ] 运行 `npm run lint:check` 无错误
- [ ] 所有类型注解完整
- [ ] 错误处理完善
- [ ] 注释清晰完整

### 空组件规范
- [ ] 数据列表页面是否添加了空组件
- [ ] 是否使用了 `<template #empty>` 插槽
- [ ] 是否包含描述性文字和引导性提示
- [ ] 是否导入了所需图标
- [ ] 操作按钮是否与页面功能相关

### 安全规范
- [ ] 密码字段是否遵循安全原则
- [ ] 编辑模式下密码字段是否为空
- [ ] 是否有适当的安全提示文字
