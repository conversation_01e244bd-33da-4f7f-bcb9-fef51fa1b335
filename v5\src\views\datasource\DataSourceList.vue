<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><DataLine /></el-icon>
        <span class="title-text">数据源设置</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索数据源"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAdd">新增数据源</el-button>
        <el-button
          :icon="Refresh"
          @click="loadDataSources"
          class="refresh-btn"
        > 
        刷新
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="数据源列表管理" name="management">
        <!-- 数据源列表 -->
        <el-table
          v-loading="loading"
          :data="filteredDataSources"
          style="width: 100%; min-width: 1000px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
      <el-table-column label="数据源名称" prop="name" min-width="100">
        <template #default="{ row }">
          <div class="datasource-name">
            <el-icon v-if="row.dbType === 'mysql'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'postgres'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'sqlserver'"><Cpu /></el-icon>
            <el-icon v-else-if="row.dbType === 'oracle'"><Cpu /></el-icon>
            <el-icon v-else><Folder /></el-icon>
            {{ row.name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="描述信息" prop="description" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="description-text">{{ row.description || '暂无描述' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="数据库类型" prop="dbType" min-width="80">
        <template #default="{ row }">
          <el-tag :type="getDbTypeTag(row.dbType)">
            {{ getDbTypeName(row.dbType) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="连接信息" min-width="150">
        <template #default="{ row }">
          {{ row.host }}{{ row.port ? `:${row.port}` : '' }}
          <div class="db-name">{{ row.database }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" effect="dark">
            <el-icon v-if="row.status === 'active'"><CircleCheck /></el-icon>
            <el-icon v-else-if="row.status === 'inactive'"><InfoFilled /></el-icon>
            <el-icon v-else><CircleClose /></el-icon>
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="更新时间" min-width="160">
        <template #default="{ row }">
          {{ DateTimeUtils.formatStandardDateTime(row.updatedAt) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button size="small" type="info" @click="handleTest(row)">
            测试
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>

        <!-- 空状态插槽 -->
        <template #empty>
          <EmptyState
            :type="searchQuery ? 'search' : 'table'"
            :title="searchQuery ? '无搜索结果' : '暂无数据源'"
            :description="searchQuery ? `没有找到包含「${searchQuery}」的数据源，请尝试其他搜索条件` : '当前没有配置任何数据源，您可以点击上方按钮添加新的数据源'"
            :action-text="searchQuery ? '清除搜索' : '添加数据源'"
            @action="searchQuery ? clearSearch : handleAdd"
          />
        </template>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 测试连接结果对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="连接测试结果"
      width="800px"
      :close-on-click-modal="false"
      center
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <div v-if="testResult === null" class="test-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在测试连接...</span>
      </div>

      <div v-else class="test-result">
        <div :class="['result-header', testResult.success ? 'success' : 'error']">
          <el-icon v-if="testResult.success"><CircleCheck /></el-icon>
          <el-icon v-else><CircleClose /></el-icon>
          <span class="result-title">{{ testResult.success ? '连接成功' : '连接失败' }}</span>
        </div>

        <div class="result-content">
          <div class="result-message">
            {{ testResult.message }}
          </div>

          <div v-if="testResult.success && testResult.responseTime" class="result-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ testResult.responseTime }}ms</span>
            </div>
          </div>

          <div v-if="!testResult.success && testResult.errorDetail" class="result-error">
            <div class="error-title">错误详情:</div>
            <div class="error-content">{{ testResult.errorDetail }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="testDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>



    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="确认删除"
      :content="deleteConfirmContent"
      warning="此操作将永久删除该数据源，且无法恢复！"
      confirm-text="确认删除"
      confirm-type="danger"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { handleApiError, handleSuccessMessage } from '@/utils/error-handler';
import { DataLine, CircleCheck, CircleClose, InfoFilled, Cpu, Folder, Refresh, Loading } from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import dataSourceService from '@/services/datasource.service';
import type { DataSource, ConnectionTestResult } from '@/types/datasource';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';
import { DateTimeUtils } from '@/utils/common-utils';

// 页签状态
const activeTab = ref('management');

// 数据源列表相关状态（原 useDataSourceList 组合式函数内容）
const loading = ref(false);
const dataSources = ref<DataSource[]>([]);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const totalCount = ref(0);

// 过滤后的数据源列表
const filteredDataSources = computed(() => {
  let result = dataSources.value || []; // 确保result不为undefined

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(ds =>
      ds.name.toLowerCase().includes(query) ||
      ds.host.toLowerCase().includes(query) ||
      ds.database.toLowerCase().includes(query)
    );
  }

  totalCount.value = result.length;

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return result.slice(start, end);
});

// 加载数据源列表
const loadDataSources = async () => {
  loading.value = true;
  try {
    const response = await dataSourceService.getDataSources(currentPage.value, pageSize.value);
    dataSources.value = response?.items || []; // 确保items不为undefined
    total.value = response?.total || 0; // 确保total不为undefined
  } catch (error) {
    handleApiError(error, '加载数据源列表失败');
    // 确保错误时也有默认值
    dataSources.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 刷新到第一页的方法
const loadDataSourcesToFirstPage = async () => {
  currentPage.value = 1;
  await loadDataSources();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  currentPage.value = 1;
};

// 分页大小变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadDataSources();
};

// 当前页变化处理
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadDataSources();
};

// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();



// 测试连接相关
const testDialogVisible = ref(false);
const testResult = ref<ConnectionTestResult | null>(null);

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<DataSource | null>(null);

// 删除确认内容 - 使用计算属性避免undefined显示
const deleteConfirmContent = computed(() => {
  if (!deleteItem.value || !deleteItem.value.name) {
    return '确定要删除该数据源吗？';
  }
  return `确定要删除数据源「${deleteItem.value.name}」吗？`;
});



// 数据源工具函数（原 datasource-utils.ts 内容合并）
const getDbTypeName = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    mysql: 'MySQL',
    postgres: 'PostgreSQL',
    sqlserver: 'SQL Server',
    oracle: 'Oracle',
    sqlite: 'SQLite'
  };
  return typeMap[dbType] || dbType;
};

const getDbTypeTag = (dbType: string): string => {
  const typeMap: Record<string, string> = {
    mysql: 'primary',
    postgres: 'success',
    sqlserver: 'info',
    oracle: 'warning',
    sqlite: 'danger'
  };
  return typeMap[dbType] || '';
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '正常',
    inactive: '未启用',
    error: '异常'
  };
  return statusMap[status] || status;
};

const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    error: 'danger'
  };
  return statusMap[status] || 'info';
};



// 新增数据源
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增数据源',
    component: 'DataSourceForm',
    props: {
      isEdit: false
    },
    size: '28%'
  });
};

// 编辑数据源
const handleEdit = (row: DataSource) => {
  drawerMessenger.showDrawer({
    title: '编辑数据源',
    component: 'DataSourceForm',
    props: {
      isEdit: true,
      editData: {
        id: row.id,
        name: row.name,
        description: row.description,  // 添加缺失的描述字段
        dbType: row.dbType,
        host: row.host,
        port: row.port,
        database: row.database,
        username: row.username,
        password: row.password,
        maxConnections: row.maxConnections,
        connectionTimeout: row.connectionTimeout,  // 添加缺失的连接超时字段
        createdBy: row.createdBy  // 添加缺失的创建人字段
      }
    },
    size: '28%'
  });
};



// 测试连接（表格中的测试按钮）
const handleTest = async (row: DataSource) => {
  try {
      testResult.value = null;
      testDialogVisible.value = true;

      // 使用新的API：根据ID测试已保存的数据源连接
      // 这样后端会自动获取数据库中的连接参数并解密密码
      const result = await dataSourceService.testSavedDataSourceConnection(row.id);
      console.log('测试连接结果:', result); // 添加调试日志
      testResult.value = result;
    } catch (error) {
      // 只有在网络错误或其他异常情况下才会到这里
      // 正常的测试失败应该通过testResult.success=false来处理
      console.error('测试连接请求失败:', error);
      testResult.value = {
        success: false,
        message: '网络请求失败',
        errorDetail: error.message || '无法连接到后端服务，请检查网络连接'
      };
    }
};



// 删除数据源
const handleDelete = (row: DataSource) => {
  deleteItem.value = row;
  deleteDialogVisible.value = true;
};

// 取消删除
const cancelDelete = () => {
  deleteDialogVisible.value = false;
  deleteItem.value = null;
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;

  const itemName = deleteItem.value.name; // 保存名称，避免在异步操作中丢失

  try {
    const success = await dataSourceService.deleteDataSource(deleteItem.value.id);
    if (success) {
        handleSuccessMessage(`数据源「${itemName}」已删除`);
        // 删除操作：跳转第一页刷新（最高效）
        loadDataSourcesToFirstPage();
      } else {
        handleApiError(null, '删除数据源失败');
      }
  } catch (error) {
      handleApiError(error, '删除数据源失败');
    } finally {
    // 延迟清理，确保对话框完全关闭后再清理数据
    setTimeout(() => {
      deleteDialogVisible.value = false;
      deleteItem.value = null;
    }, 100);
  }
};

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.DATA_SOURCE);
});

// 页面加载时注册刷新机制
onMounted(() => {
  loadDataSources();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.DATA_SOURCE,
    loadDataSources,           // 保持当前页刷新
    loadDataSourcesToFirstPage // 跳转第一页刷新
  );
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 引入公共样式，但保留现有样式作为备份和覆盖 */

/* 数据源名称列样式 */
.datasource-name {
  display: flex;
  align-items: center;
  font-weight: 400;
}

.datasource-name .el-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #3FC8DD;
}

.db-name {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  font-weight: 400;
}

/* 状态标签 */
:deep(.el-tag) {
  font-weight: 400;
  padding: 2px 8px;
  border: 1px solid;
}

/* 测试结果样式 */
.test-result {
  padding: 20px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.result-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 20px;
}

.result-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.result-header.success {
  color: #67c23a;
}

.result-header.error {
  color: #f56c6c;
}

.result-details {
  background: white;
  padding: 15px 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.detail-item {
  display: flex;
  margin: 8px 0;
  align-items: center;
}

.detail-item .label {
  font-weight: 500;
  width: 120px;
  color: #303133;
}

.detail-item .value {
  color: #606266;
  font-weight: 400;
}

.result-error {
  color: #f56c6c;
  background: #fef0f0;
  padding: 15px 20px;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-weight: 400;
}

/* 测试连接对话框样式 */
.test-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #606266;
  font-size: 14px;
}

.test-result {
  /* 确保测试结果容器不会截断内容 */
  width: 100%;
  max-width: none;
  overflow: visible;

  .result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;

    &.success {
      background: #f0f9ff;
      border: 1px solid #bae6fd;
      color: #0369a1;

      .el-icon {
        color: #10b981;
      }
    }

    &.error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;

      .el-icon {
        color: #ef4444;
      }
    }

    .result-title {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .result-content {
    .result-message {
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      white-space: pre-wrap;
      /* 确保内容完全显示，不被截断 */
      width: 100%;
      max-width: none;
      overflow: visible;
      text-overflow: clip;
      /* 移除任何可能的省略号样式 */
      -webkit-line-clamp: unset;
      -webkit-box-orient: unset;
      display: block;
    }

    .result-details {
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 16px;
        background: #f0f9ff;
        border-radius: 4px;
        margin-bottom: 8px;

        .label {
          color: #6b7280;
          font-size: 14px;
        }

        .value {
          color: #059669;
          font-weight: 500;
          font-size: 14px;
        }
      }
    }

    .result-error {
      .error-title {
        color: #dc2626;
        font-weight: 500;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .error-content {
        padding: 12px;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 4px;
        color: #7f1d1d;
        font-size: 13px;
        line-height: 1.5;
        word-break: break-word;
      }
    }
  }
}

/* 响应式设计 - 2024-12-26: 部分已在page-common.scss中，保留页面特有的响应式样式 */
@media (max-width: 768px) {
  .page-container {
    margin: 3px;
    padding: 12px;
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .action-buttons {
    justify-content: center;
  }
}


</style>