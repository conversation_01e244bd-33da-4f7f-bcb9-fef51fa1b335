import type { MenuData, PageConfig } from '@/types/navigation';
import menuService from '@/services/menu.service';

// 定义返回类型接口
export interface MainNavigationReturn {
  initMenuData: () => Promise<{ menuData: MenuData | null; defaultPage: PageConfig | null }>;
}

/**
 * 组合式函数，用于管理主页面的菜单数据初始化
 * @returns 返回包含菜单数据初始化方法的对象，类型为 MainNavigationReturn
 */
export const useMainNavigation = (): MainNavigationReturn => {
  /**
   * 初始化菜单数据
   * @returns 返回包含菜单数据和默认页面配置的对象
   */
  const initMenuData = async (): Promise<{ menuData: MenuData | null; defaultPage: PageConfig | null }> => {
    let defaultPage: PageConfig | null = null;
    try {
      const menuData = await menuService.getMenuData();
      if (menuData?.pageConfigs?.length) {
        defaultPage = menuData.pageConfigs[0];
      }
      return { menuData, defaultPage };
    } catch (error) {
      console.error('加载菜单数据失败:', error);
      return { menuData: null, defaultPage: null };
    }
  };

  return {
    initMenuData
  };
}
