#!/usr/bin/env python3
"""
统一测试运行器
支持分层测试和打包时排除
"""

import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from app.shared.tests.shared.test_database import run_database_tests
from app.shared.tests.shared.test_crypto import run_crypto_tests
from app.shared.tests.shared.test_odbc_drivers import run_driver_tests
from app.shared.tests.config.test_data_source import run_data_source_tests
from app.shared.tests.test_api import run_api_tests

def run_shared_tests():
    """运行共享层测试"""
    print("🔧 运行共享层测试...")

    all_results = []

    # 数据库测试
    db_results = run_database_tests()
    all_results.extend(db_results)

    print()  # 空行分隔

    # 加密测试
    crypto_results = run_crypto_tests()
    all_results.extend(crypto_results)

    print()  # 空行分隔

    # 数据库驱动程序测试
    driver_results = run_driver_tests()
    all_results.extend(driver_results)

    return all_results

def run_config_tests():
    """运行配置层测试"""
    print("⚙️  运行配置层测试...")

    all_results = []

    # 数据源测试
    ds_results = run_data_source_tests()
    all_results.extend(ds_results)

    return all_results

def run_api_integration_tests():
    """运行API集成测试"""
    print("🌐 运行API集成测试...")

    # API测试
    api_results = run_api_tests()

    return api_results

def run_service_tests():
    """运行服务层测试"""
    print("🚀 运行服务层测试...")

    # TODO: 服务层测试待实现
    print("⚠️  服务层测试尚未实现")
    return []

def print_test_results(results, layer_name):
    """打印测试结果"""
    if not results:
        print(f"📊 {layer_name}测试结果: 无测试项目")
        return 0

    print(f"\n📊 {layer_name}测试结果:")
    print("-" * 30)

    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1

    print(f"小计: {success_count}/{len(results)} 项通过")
    return success_count

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Mapi-ConfigCore 测试运行器")
    parser.add_argument("--layer", choices=["shared", "config", "service", "all"],
                       default="all", help="指定测试层级")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    print("🚀 Mapi-ConfigCore 测试运行器启动...\n")

    all_results = []
    total_success = 0

    if args.layer in ["shared", "all"]:
        shared_results = run_shared_tests()
        all_results.extend(shared_results)
        total_success += print_test_results(shared_results, "共享层")
        print()

    if args.layer in ["config", "all"]:
        config_results = run_config_tests()
        all_results.extend(config_results)
        total_success += print_test_results(config_results, "配置层")
        print()

    if args.layer in ["service", "all"]:
        service_results = run_service_tests()
        all_results.extend(service_results)
        total_success += print_test_results(service_results, "服务层")
        print()

    # 总体结果
    print("=" * 50)
    print("🎯 总体测试结果:")
    print("=" * 50)
    print(f"总计: {total_success}/{len(all_results)} 项测试通过")

    if total_success == len(all_results) and len(all_results) > 0:
        print("🎉 所有测试通过！系统功能正常。")
        return True
    elif len(all_results) == 0:
        print("⚠️  没有运行任何测试。")
        return False
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
