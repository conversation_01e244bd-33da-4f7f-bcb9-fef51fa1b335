#!/usr/bin/env python3
"""
API接口测试脚本
测试后端API的完整功能
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_root_endpoint():
    """测试根路径"""
    print("🔍 测试根路径...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 根路径测试失败: {e}")
        return False

def test_health_endpoint():
    """测试健康检查"""
    print("\n🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False

def test_datasource_list():
    """测试数据源列表"""
    print("\n🔍 测试数据源列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/datasource/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"总数: {data['total']}")
            print(f"当前页: {data['page']}")
            print(f"每页大小: {data['size']}")
            print(f"数据源数量: {len(data['items'])}")
            if data['items']:
                print(f"第一个数据源: {data['items'][0]['name']}")
        else:
            print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 数据源列表测试失败: {e}")
        return False

def test_datasource_detail():
    """测试数据源详情"""
    print("\n🔍 测试数据源详情...")
    try:
        # 先获取列表，取第一个ID
        list_response = requests.get(f"{BASE_URL}/api/v1/datasource/")
        if list_response.status_code == 200:
            data = list_response.json()
            if data['items']:
                first_id = data['items'][0]['id']
                detail_response = requests.get(f"{BASE_URL}/api/v1/datasource/{first_id}")
                print(f"状态码: {detail_response.status_code}")
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"数据源名称: {detail_data['name']}")
                    print(f"数据库类型: {detail_data['db_type']}")
                    print(f"主机地址: {detail_data['host']}")
                    print(f"状态: {detail_data['status']}")
                else:
                    print(f"响应: {detail_response.text}")
                return detail_response.status_code == 200
            else:
                print("❌ 没有数据源可测试")
                return False
        else:
            print("❌ 无法获取数据源列表")
            return False
    except Exception as e:
        print(f"❌ 数据源详情测试失败: {e}")
        return False

def test_connection_test():
    """测试连接测试功能"""
    print("\n🔍 测试连接测试功能...")
    try:
        test_data = {
            "db_type": "sqlite",
            "host": "localhost",
            "port": 0,
            "database": "test.db",
            "username": "test",
            "password": "test123",
            "connection_timeout": 30
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/datasource/test-connection",
            json=test_data
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"连接成功: {result['success']}")
            print(f"消息: {result['message']}")
            if 'response_time' in result:
                print(f"响应时间: {result['response_time']}ms")
        else:
            print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def run_api_tests():
    """运行所有API测试"""
    print("🚀 开始API接口测试...\n")
    
    tests = [
        ("根路径", test_root_endpoint),
        ("健康检查", test_health_endpoint),
        ("数据源列表", test_datasource_list),
        ("数据源详情", test_datasource_detail),
        ("连接测试", test_connection_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    return results

if __name__ == "__main__":
    results = run_api_tests()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 API测试结果汇总:")
    print("="*50)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有API测试通过！")
        exit(0)
    else:
        print("⚠️  部分API测试失败。")
        exit(1)
