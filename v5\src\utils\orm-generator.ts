/**
 * ORM模型生成器
 * 根据数据库表结构自动生成ORM模型代码
 */

export interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  default: any;
  key: string;
  extra: string;
  comment: string;
  length?: number;
  precision?: number;
  scale?: number;
}

export interface TableStructure {
  success: boolean;
  message: string;
  columns: ColumnInfo[];
}

/**
 * 数据库类型到Python类型的映射
 */
const DB_TYPE_TO_PYTHON_TYPE: Record<string, string> = {
  // MySQL类型映射
  'int': 'Integer',
  'bigint': 'BigInteger',
  'smallint': 'SmallInteger',
  'tinyint': 'SmallInteger',
  'mediumint': 'Integer',
  'decimal': 'Numeric',
  'numeric': 'Numeric',
  'float': 'Float',
  'double': 'Float',
  'real': 'Float',
  'varchar': 'String',
  'char': 'String',
  'text': 'Text',
  'longtext': 'Text',
  'mediumtext': 'Text',
  'tinytext': 'String',
  'json': 'JSON',
  'date': 'Date',
  'datetime': 'DateTime',
  'timestamp': 'DateTime',
  'time': 'Time',
  'year': 'Integer',
  'boolean': 'Boolean',
  'bool': 'Boolean',
  'bit': 'Boolean',
  'binary': 'LargeBinary',
  'varbinary': 'LargeBinary',
  'blob': 'LargeBinary',
  'longblob': 'LargeBinary',
  'mediumblob': 'LargeBinary',
  'tinyblob': 'LargeBinary',
  
  // PostgreSQL类型映射
  'integer': 'Integer',
  'bigserial': 'BigInteger',
  'serial': 'Integer',
  'smallserial': 'SmallInteger',
  'money': 'Numeric',
  'character': 'String',
  'character varying': 'String',
  'uuid': 'String',
  'inet': 'String',
  'cidr': 'String',
  'macaddr': 'String',
  'bytea': 'LargeBinary',
  'interval': 'Interval',
  'array': 'ARRAY',
  
  // SQL Server类型映射
  'nvarchar': 'String',
  'nchar': 'String',
  'ntext': 'Text',
  'uniqueidentifier': 'String',
  'money': 'Numeric',
  'smallmoney': 'Numeric',
  'image': 'LargeBinary',
  'xml': 'Text',
  
  // Oracle类型映射
  'number': 'Numeric',
  'varchar2': 'String',
  'nvarchar2': 'String',
  'char': 'String',
  'nchar': 'String',
  'clob': 'Text',
  'nclob': 'Text',
  'blob': 'LargeBinary',
  'raw': 'LargeBinary',
  'long raw': 'LargeBinary',
  'rowid': 'String',
  'urowid': 'String',
  
  // SQLite类型映射
  'integer': 'Integer',
  'real': 'Float',
  'text': 'Text',
  'blob': 'LargeBinary'
};

/**
 * 将数据库字段类型转换为SQLAlchemy类型
 */
export function mapDbTypeToSqlAlchemy(dbType: string, length?: number, precision?: number, scale?: number): string {
  const lowerType = dbType.toLowerCase();
  
  // 处理带长度的类型
  if (lowerType.includes('varchar') || lowerType.includes('char')) {
    const baseType = DB_TYPE_TO_PYTHON_TYPE[lowerType] || 'String';
    return length ? `${baseType}(${length})` : baseType;
  }
  
  // 处理数值类型
  if (lowerType.includes('decimal') || lowerType.includes('numeric')) {
    if (precision && scale) {
      return `Numeric(${precision}, ${scale})`;
    } else if (precision) {
      return `Numeric(${precision})`;
    }
    return 'Numeric';
  }
  
  // 处理浮点类型
  if (lowerType.includes('float') || lowerType.includes('double')) {
    if (precision) {
      return `Float(${precision})`;
    }
    return 'Float';
  }
  
  // 默认映射
  return DB_TYPE_TO_PYTHON_TYPE[lowerType] || 'String';
}

/**
 * 生成字段名的Python属性名（转换为snake_case）
 */
export function generatePythonFieldName(columnName: string): string {
  // 如果已经是合理的snake_case格式（包含下划线且全大写或全小写），转为小写
  if (columnName.includes('_') && (columnName === columnName.toUpperCase() || columnName === columnName.toLowerCase())) {
    return columnName.toLowerCase();
  }

  // 如果已经是小写且不包含连字符，直接返回
  if (columnName.toLowerCase() === columnName && !columnName.includes('-')) {
    return columnName;
  }

  // 转换为snake_case
  return columnName
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '')
    .replace(/-/g, '_');
}

/**
 * 生成完整的ORM配置（包含查询能力）
 */
export function generateCompleteOrmConfig(
  tableName: string,
  tableStructure: TableStructure,
  httpMethods: string[] = ['GET'],
  datasourceInfo?: any
): any {
  if (!tableStructure.success || !tableStructure.columns.length) {
    return {
      error: `无法生成ORM模型：${tableStructure.message}`,
      sqlalchemy_model: null,
      query_mapping: null
    };
  }

  const modelClassName = toPascalCase(tableName);
  const columns = tableStructure.columns;

  // 分析字段类型，自动配置查询能力 - 使用原始字段名
  const textFields = columns.filter(col =>
    ['varchar', 'char', 'text', 'nvarchar', 'nchar', 'ntext'].some(type =>
      col.type.toLowerCase().includes(type)
    )
  ).map(col => col.name); // 使用原始字段名

  const numericFields = columns.filter(col =>
    ['int', 'bigint', 'decimal', 'numeric', 'float', 'double', 'money'].some(type =>
      col.type.toLowerCase().includes(type)
    )
  ).map(col => col.name); // 使用原始字段名

  const dateFields = columns.filter(col =>
    ['datetime', 'date', 'timestamp'].some(type =>
      col.type.toLowerCase().includes(type)
    )
  ).map(col => col.name); // 使用原始字段名

  const primaryKeyFields = columns.filter(col => col.key === 'PRI')
    .map(col => col.name); // 使用原始字段名

  // 生成查询映射配置 - 使用原始字段名
  const queryMapping = {
    fuzzy_search_fields: textFields.slice(0, 5), // 限制前5个文本字段支持模糊搜索
    exact_match_fields: [...primaryKeyFields, ...numericFields.slice(0, 3)], // 主键和数值字段精确匹配
    range_query_fields: [...dateFields, ...numericFields.slice(0, 2)], // 日期和数值字段支持范围查询
    default_sort: primaryKeyFields[0] || columns[0]?.name || 'F_ID', // 使用实际的主键字段名
    default_order: 'desc',
    allowed_sort_fields: [...primaryKeyFields, ...dateFields, ...textFields.slice(0, 3)]
  };

  // 生成字段定义 - 使用原始字段名作为主要字段名，移除冗余的查询能力配置
  const fields = columns.map(col => ({
    name: col.name, // 使用原始字段名作为主要字段名
    original_name: col.name, // 保持原始字段名
    python_name: generatePythonFieldName(col.name), // 保留Python风格名称作为备用
    type: col.type,
    sqlalchemy_type: mapDbTypeToSqlAlchemy(col.type, col.length, col.precision, col.scale),
    nullable: col.nullable,
    primary_key: col.key === 'PRI',
    default: col.default,
    comment: col.comment || '',
    length: col.length,
    precision: col.precision,
    scale: col.scale
    // 移除字段级别的查询能力配置，统一在 query_mapping 中管理
  }));

  return {
    model_name: modelClassName,
    table_name: tableName,
    description: `${tableName} 数据模型`,
    http_methods: httpMethods,
    datasource_info: datasourceInfo,
    sqlalchemy_model: {
      class_name: modelClassName,
      table_name: tableName,
      fields: fields
    },
    query_mapping: queryMapping,
    response_config: {
      exclude_fields: [], // 可以根据需要排除敏感字段
      date_format: "%Y-%m-%d %H:%M:%S",
      include_total_count: true,
      null_to_empty: false
    }
  };
}

/**
 * 生成SQLAlchemy ORM模型代码
 */
export function generateSqlAlchemyModel(
  tableName: string,
  tableStructure: TableStructure,
  className?: string
): string {
  if (!tableStructure.success || !tableStructure.columns.length) {
    return `# 无法生成ORM模型：${tableStructure.message}`;
  }

  const modelClassName = className || toPascalCase(tableName);
  const columns = tableStructure.columns;
  
  // 生成导入语句
  const imports = new Set(['Column', 'Integer', 'String']);
  const usedTypes = new Set<string>();
  
  columns.forEach(col => {
    const sqlType = mapDbTypeToSqlAlchemy(col.type, col.length, col.precision, col.scale);
    const baseType = sqlType.split('(')[0];
    usedTypes.add(baseType);
  });
  
  usedTypes.forEach(type => imports.add(type));
  
  const importList = Array.from(imports).sort().join(', ');
  
  // 生成模型代码
  let modelCode = `from sqlalchemy import ${importList}\nfrom sqlalchemy.ext.declarative import declarative_base\n\nBase = declarative_base()\n\nclass ${modelClassName}(Base):\n    __tablename__ = '${tableName}'\n\n`;
  
  // 生成字段定义
  columns.forEach(col => {
    const fieldName = generatePythonFieldName(col.name);
    const sqlType = mapDbTypeToSqlAlchemy(col.type, col.length, col.precision, col.scale);
    
    let fieldDef = `    ${fieldName} = Column(${sqlType}`;
    
    // 添加主键
    if (col.key === 'PRI') {
      fieldDef += ', primary_key=True';
    }
    
    // 添加自增
    if (col.extra && col.extra.toLowerCase().includes('auto_increment')) {
      fieldDef += ', autoincrement=True';
    }
    
    // 添加可空性
    if (!col.nullable && col.key !== 'PRI') {
      fieldDef += ', nullable=False';
    }
    
    // 添加默认值
    if (col.default !== null && col.default !== undefined && col.default !== '') {
      if (typeof col.default === 'string') {
        fieldDef += `, default='${col.default}'`;
      } else {
        fieldDef += `, default=${col.default}`;
      }
    }
    
    fieldDef += ')';
    
    // 添加注释
    if (col.comment) {
      fieldDef += `  # ${col.comment}`;
    }
    
    modelCode += fieldDef + '\n';
  });
  
  // 添加repr方法
  const primaryKeyField = columns.find(col => col.key === 'PRI');
  if (primaryKeyField) {
    const pkFieldName = generatePythonFieldName(primaryKeyField.name);
    modelCode += `\n    def __repr__(self):\n        return f"<${modelClassName}(${pkFieldName}={self.${pkFieldName}})>"\n`;
  }
  
  return modelCode;
}

/**
 * 转换为PascalCase
 */
function toPascalCase(str: string): string {
  return str
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}

/**
 * 生成简化的ORM模型（用于快速预览）
 */
export function generateSimpleModel(tableName: string, columns: ColumnInfo[]): string {
  if (!columns.length) {
    return `# 表 ${tableName} 无字段信息`;
  }
  
  let model = `# ${tableName} 表结构\n`;
  model += `class ${toPascalCase(tableName)}:\n`;
  
  columns.forEach(col => {
    const fieldName = generatePythonFieldName(col.name);
    const comment = col.comment ? ` # ${col.comment}` : '';
    const nullable = col.nullable ? ' (可空)' : ' (必填)';
    const key = col.key === 'PRI' ? ' [主键]' : '';
    
    model += `    ${fieldName}: ${col.type}${key}${nullable}${comment}\n`;
  });
  
  return model;
}
