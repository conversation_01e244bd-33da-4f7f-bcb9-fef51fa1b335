#!/usr/bin/env python3
"""
测试最终的日志格式 - 只有分隔线，没有空行
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.core import log_init
from app.shared.core.log_util import LogUtil

def test_final_format():
    """测试最终的日志格式"""
    print("🧪 测试最终日志格式（只有分隔线，无空行）")
    
    # 初始化日志系统
    log_init.setup_logging()
    
    # 记录一条测试日志
    LogUtil.info("系统启动完成", 
                 version="v1.0.0",
                 startup_time_ms=1500,
                 modules_loaded=["core", "service", "api"],
                 status="ready")
    
    print("✅ 测试日志已记录")
    print("🔍 请查看info_*.log文件验证格式：只有分隔线，没有空行")

if __name__ == "__main__":
    test_final_format()
