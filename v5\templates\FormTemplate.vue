<!-- 
  表单组件标准模板
  使用说明：
  1. 复制此模板创建新表单组件
  2. 替换所有 {ModuleName} 为实际模块名
  3. 替换所有 {moduleName} 为实际模块名（小写）
  4. 根据实际需求调整表单字段和验证规则
-->
<template>
  <div class="drawer-form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="drawer-form"
    >
      <el-form-item label="{ModuleName}名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入{ModuleName}名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入{ModuleName}描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="enabled">启用</el-radio>
          <el-radio value="disabled">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 抽屉底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'
import {moduleName}Service from '@/services/{module-name}.service'
import { PageRefresh } from '@/utils/pageRefreshUtil'
import { extractErrorMessage } from '@/utils/common-utils'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  status: 'enabled'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入{ModuleName}名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置表单 - 必须在 watch 之前定义
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    status: 'enabled'
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.editData) {
    // 编辑模式，填充表单数据
    Object.assign(formData, newProps.editData)
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}, { immediate: true, deep: true })

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value.validate()
    
    loading.value = true
    
    const isEdit = globalDrawerStore.props?.isEdit
    const editData = globalDrawerStore.props?.editData
    
    if (isEdit && editData) {
      // 编辑模式
      await {moduleName}Service.update{ModuleName}(editData.id, formData)
      ElMessage.success('编辑成功')
    } else {
      // 新增模式
      await {moduleName}Service.create{ModuleName}(formData)
      ElMessage.success('新增成功')
    }
    
    // 关闭抽屉并刷新列表
    drawerMessenger.hideDrawer()
    // 这里可以通过事件或其他方式通知父组件刷新列表
    
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(extractErrorMessage(error, '提交失败'))
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer()
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '取消',
      handler: handleCancel
    },
    {
      text: globalDrawerStore.props?.isEdit ? '保存' : '创建',
      type: 'primary',
      handler: handleSubmit,
      loading: loading.value,
      loadingText: '提交中...'
    }
  ]
}

// 监听loading状态，更新按钮状态
watch(loading, () => {
  updateDrawerButtons()
})

// 监听编辑状态，更新按钮文字
watch(() => globalDrawerStore.props?.isEdit, () => {
  updateDrawerButtons()
})

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 抽屉表单容器样式已在page-common.scss中定义 */

/* 表单标签对齐修复 */
.drawer-form {
  :deep(.el-form-item__label) {
    text-align: left !important;
    justify-content: flex-start !important;
    padding-left: 0 !important;
  }

  :deep(.el-form-item__label::before) {
    margin-right: 4px !important;
  }
}

/* 重要提示样式 */
.important-tip {
  color: #e6a23c !important;
  font-weight: 500 !important;
  background: #fdf6ec;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
  margin-top: 8px;
}
</style>
