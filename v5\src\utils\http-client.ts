/**
 * 统一HTTP客户端
 * 自动处理请求/响应数据格式转换
 * 统一错误处理和日志记录
 */

import { convertToCamelCase, convertToSnakeCase, DateTimeUtils } from './common-utils';

/**
 * HTTP请求配置
 */
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  timeout?: number;
  skipTransform?: boolean; // 跳过数据转换
}

/**
 * HTTP响应类型
 */
interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

/**
 * 统一HTTP客户端类
 */
class HttpClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private defaultTimeout: number;

  constructor(baseURL: string = '', options: {
    headers?: Record<string, string>;
    timeout?: number;
  } = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    this.defaultTimeout = options.timeout || 10000;
  }

  /**
   * 构建完整URL
   */
  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    if (!params || Object.keys(params).length === 0) {
      return fullURL;
    }

    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    const separator = fullURL.includes('?') ? '&' : '?';
    return `${fullURL}${separator}${searchParams.toString()}`;
  }

  /**
   * 自动格式化时间字段
   * 识别常见的时间字段名并格式化
   */
  private formatTimeFields(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // 如果是数组，递归处理每个元素
    if (Array.isArray(obj)) {
      return obj.map(item => this.formatTimeFields(item));
    }

    // 常见的时间字段名模式
    const timeFieldPatterns = [
      /.*_at$/i,           // created_at, updated_at, deleted_at
      /.*_time$/i,         // create_time, update_time
      /.*date.*$/i,        // date, start_date, end_date
      /^(created|updated|deleted)$/i,  // created, updated, deleted
      /^(createdAt|updatedAt|deletedAt)$/i,  // camelCase 版本
      /^(createTime|updateTime)$/i,    // camelCase 版本
    ];

    const result = { ...obj };

    for (const [key, value] of Object.entries(result)) {
      // 检查是否是时间字段
      const isTimeField = timeFieldPatterns.some(pattern => pattern.test(key));

      if (isTimeField && (typeof value === 'string' || value instanceof Date)) {
        // 格式化时间字段
        result[key] = DateTimeUtils.formatStandardDateTime(value);
      } else if (typeof value === 'object' && value !== null) {
        // 递归处理嵌套对象
        result[key] = this.formatTimeFields(value);
      }
    }

    return result;
  }

  /**
   * 处理请求体数据
   */
  private processRequestBody(body: any, skipTransform: boolean): string | undefined {
    console.log('🔍 processRequestBody 被调用:', { body, skipTransform });

    if (!body) {
      console.log('⚠️ body 为空，返回 undefined');
      return undefined;
    }

    // 如果跳过转换，直接序列化
    if (skipTransform) {
      console.log('⚠️ 跳过转换，直接序列化');
      return JSON.stringify(body);
    }

    // 自动转换为snake_case
    const transformedBody = convertToSnakeCase(body);
    console.log('🔍 发送前的原始数据:', body);
    console.log('🔍 转换后的数据:', transformedBody);
    console.log('🔍 最终JSON:', JSON.stringify(transformedBody));
    return JSON.stringify(transformedBody);
  }

  /**
   * 处理响应数据
   */
  private async processResponse<T>(response: Response, skipTransform: boolean): Promise<ApiResponse<T>> {
    let data: any;

    // 尝试解析JSON
    try {
      const text = await response.text();
      data = text ? JSON.parse(text) : null;
    } catch (error) {
      throw new Error(`响应解析失败: ${error}`);
    }

    // 如果跳过转换，直接返回
    if (skipTransform) {
      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      };
    }

    // 自动转换为camelCase
    const transformedData = convertToCamelCase(data);

    // 自动格式化时间字段
    const formattedData = this.formatTimeFields(transformedData);

    return {
      data: formattedData,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    };
  }

  /**
   * 通用请求方法
   */
  private async request<T = any>(url: string, config: RequestConfig = {}): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      timeout = this.defaultTimeout,
      skipTransform = false
    } = config;

    // 构建请求URL
    const requestURL = this.buildURL(url, params);

    // 构建请求头
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers
    };

    // 构建请求配置
    const fetchConfig: RequestInit = {
      method,
      headers: requestHeaders,
      body: this.processRequestBody(body, skipTransform)
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      // console.log(`🌐 HTTP ${method} ${requestURL}`, {
      //   headers: requestHeaders,
      //   body: fetchConfig.body ? JSON.parse(fetchConfig.body as string) : undefined
      // });

      const response = await fetch(requestURL, {
        ...fetchConfig,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 处理HTTP错误状态
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorDetail = null;

        try {
          const errorData = await response.json();
          console.log('🔍 后端错误响应:', errorData);

          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.detail) {
            // 如果 detail 是字符串，直接使用
            if (typeof errorData.detail === 'string') {
              errorMessage = errorData.detail;
            }
            // 如果 detail 是数组（FastAPI 验证错误格式）
            else if (Array.isArray(errorData.detail)) {
              console.log('🔍 FastAPI验证错误详情:', errorData.detail);
              // 显示所有错误的详细信息
              errorData.detail.forEach((error, index) => {
                console.log(`🔍 错误 ${index + 1}:`, {
                  字段路径: error.loc,
                  错误类型: error.type,
                  错误消息: error.msg,
                  输入值: error.input
                });
              });

              // 提取第一个错误的消息
              if (errorData.detail.length > 0) {
                const firstError = errorData.detail[0];
                const fieldPath = firstError.loc?.join('.') || '未知字段';
                if (firstError.msg) {
                  errorMessage = `${fieldPath}: ${firstError.msg}`;
                } else if (firstError.message) {
                  errorMessage = `${fieldPath}: ${firstError.message}`;
                } else {
                  errorMessage = `字段 ${fieldPath} 验证失败`;
                }
              }
            }
            // 如果 detail 是对象
            else if (typeof errorData.detail === 'object') {
              errorMessage = JSON.stringify(errorData.detail);
            }
          }

          // 保存完整的错误详情
          errorDetail = errorData;
        } catch (parseError) {
          console.log('⚠️ 错误响应JSON解析失败:', parseError);
          // 忽略JSON解析错误，使用默认错误消息
        }

        // 创建增强的错误对象
        const error = new Error(errorMessage);
        (error as any).response = {
          status: response.status,
          statusText: response.statusText,
          data: errorDetail
        };

        console.log('🚨 抛出错误对象:', error);
        throw error;
      }

      // 处理响应数据
      const result = await this.processResponse<T>(response, skipTransform);

      console.log(`✅ HTTP ${method} ${requestURL} 成功`, {
        status: response.status,
        statusText: response.statusText,
        data: result.data
      });

      // 特别关注接口配置的获取
      if (requestURL.includes('/interface/configs/')) {
        console.log('🔍 接口配置详情数据:', {
          url: requestURL,
          原始响应: result.data,
          group_id: result.data?.group_id,
          datasource_id: result.data?.datasource_id,
          groupId: result.data?.groupId,
          datasourceId: result.data?.datasourceId
        });
      }

      return result.data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`请求超时 (${timeout}ms): ${requestURL}`);
        }
        console.error(`❌ HTTP ${method} ${requestURL} 失败:`, error.message);
        throw error;
      }
      
      throw new Error(`未知错误: ${error}`);
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: Record<string, any>, config?: Omit<RequestConfig, 'method' | 'body' | 'params'>): Promise<T> {
    return this.request<T>(url, { ...config, method: 'GET', params });
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(url, { ...config, method: 'POST', body });
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(url, { ...config, method: 'PUT', body });
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(url, { ...config, method: 'DELETE' });
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, body?: any, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<T> {
    return this.request<T>(url, { ...config, method: 'PATCH', body });
  }
}

// 创建默认实例
export const httpClient = new HttpClient();

// 创建API实例（带基础路径）
export const apiClient = new HttpClient('/api/v1');

// 导出类供自定义使用
export { HttpClient };
export type { RequestConfig, ApiResponse };
