<template>
  <el-menu
    :default-active="activeIndex"
    class="header-menu"
    mode="horizontal"
    :ellipsis="false"
    @select="handleSelect"
  >
    <div class="header-logo">
      <i class="el-icon-data-analysis" style="font-size: 24px; color: #165DFF;"></i>
      <span class="logo-text">报表BI系统</span>
    </div>
    
    <div class="header-menu-items">
      <el-menu-item index="/components">组件管理</el-menu-item>
      <el-menu-item index="/canvas">画布管理</el-menu-item>
      <el-menu-item index="/reports">报表管理</el-menu-item>
    </div>
    
    <div class="header-actions">
      <el-button icon="Bell" link></el-button>
      <el-button icon="Setting" link></el-button>
      <el-dropdown>
        <div class="user-info">
          <el-avatar :size="32" src="https://picsum.photos/id/1005/200/200" />
          <span class="user-name">系统管理员</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-menu>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const activeIndex = ref(route.path)

// 监听路由变化
watch(() => route.path, (newPath) => {
  activeIndex.value = newPath
})

// 处理菜单选择
const handleSelect = (key: string) => {
  router.push(key)
}
</script>

<style scoped>
.header-menu {
  display: flex;
  align-items: center;
  height: 60px;
  border-bottom: none;
}

.header-logo {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 100%;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #165DFF;
  margin-left: 8px;
}

.header-menu-items {
  flex: 1;
  display: flex;
  margin-left: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-name {
  margin-left: 8px;
  font-size: 14px;
}
</style>