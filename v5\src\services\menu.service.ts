/// <reference types="vite/client" />
// src/services/menu.service.ts
//
// 重构记录 - 2024-12-27:
// 1. 数据分离：将菜单数据提取到独立的menu.mock.ts文件
// 2. Service职责单一：service只负责从mock获取数据，不包含数据本身
// 3. 为未来扩展做准备：菜单名称将来从"系统管理-菜单设置"中动态获取
//
// 优化记录 - 2024-01-20:
// 移除了topLevelMenus.children中的冗余path字段，统一使用pageConfigs中的path进行路由跳转
// 原因：一级菜单的path字段在实际使用中未被引用，所有导航逻辑都依赖pageConfigs中的path
// 影响：消除数据冗余，降低维护成本，避免路径不一致问题
//
import type { MenuData } from '@/types/navigation';
import { mockMenuData } from '@/mock/menu.mock';

// 抽象接口：定义获取菜单数据的方法
interface IMenuService {
  getMenuData(): Promise<MenuData>;
}

// Mock实现（开发阶段使用）
class MockMenuService implements IMenuService {
  async getMenuData(): Promise<MenuData> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    // 从mock文件获取数据
    return { ...mockMenuData };
  }
}

// 真实API实现（生产阶段使用）
class ApiMenuService implements IMenuService {
  async getMenuData(): Promise<MenuData> {
    // TODO: 未来从"系统管理-菜单设置"中动态获取菜单名称
    // 当前从静态API获取菜单结构，菜单名称可通过管理界面配置
    const response = await fetch('/api/menu');
    if (!response.ok) throw new Error('菜单数据获取失败');
    return response.json();
  }
}

// 根据环境变量切换实现（需在.env文件中配置）
// 临时修复：菜单服务暂时使用Mock模式，数据源服务使用API模式
const useMockForMenu = true; // 菜单暂时使用Mock，等后端菜单API完成后改为 import.meta.env.VITE_USE_MOCK === 'true'
const menuService: IMenuService = useMockForMenu ? new MockMenuService() : new ApiMenuService();

export default menuService;