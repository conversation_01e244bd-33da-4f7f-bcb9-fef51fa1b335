<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>{MODULE_NAME}管理</h2>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索{MODULE_NAME}"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAdd">新增{MODULE_NAME}</el-button>
        <el-button @click="load{MODULE_NAME}s">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="{module_name}s"
        :loading="loading"
        style="width: 100%; min-width: 800px;"
        :row-style="{ height: '60px' }"
        :cell-style="{ padding: '12px 0' }"
        :scroll-x="true"
      >
        <!-- 表格列定义 -->
        <el-table-column label="名称" prop="name" min-width="120" width="200">
          <template #default="{ row }">
            <div class="item-name">
              <el-icon><Folder /></el-icon>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="描述"
          prop="description"
          min-width="200"
          width="400"
          show-overflow-tooltip
        />

        <el-table-column label="状态" prop="isEnabled" width="150" align="center">
          <template #default="{ row }">
            <span :class="row.isEnabled ? 'status-enabled' : 'status-disabled'">
              {{ row.isEnabled ? '启用' : '禁用' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" prop="createdAt" min-width="160" />

        <el-table-column label="操作" min-width="180" fixed="right" width="150">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>

        <!-- 【重要】空状态插槽 - 所有列表页面必须添加 -->
        <template #empty>
          <EmptyState
            :type="searchQuery ? 'search' : 'table'"
            :title="searchQuery ? '无搜索结果' : '暂无{MODULE_NAME}'"
            :description="searchQuery ? `没有找到包含「${searchQuery}」的{MODULE_NAME}，请尝试其他搜索条件` : '当前没有配置任何{MODULE_NAME}，您可以点击上方按钮添加新的{MODULE_NAME}'"
            :action-text="searchQuery ? '清除搜索' : '新增{MODULE_NAME}'"
            @action="searchQuery ? clearSearch : handleAdd"
          />
        </template>
      </el-table>

      <!-- 分页组件 -->
      <PaginationComponent
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialog.visible"
      :title="deleteDialog.title"
      :content="deleteDialog.content"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Folder, Refresh } from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue'; // 【重要】必须导入空状态组件
import {module_name}Service from '@/services/{module_name}.service';
import type { {MODULE_NAME} } from '@/types/{module_name}';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { handleApiError, handleSuccessMessage } from '@/utils/error-handler';

// 响应式数据
const {module_name}s = ref<{MODULE_NAME}[]>([]);
const loading = ref(false);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);

// 删除对话框
const deleteDialog = ref({
  visible: false,
  title: '',
  content: '',
  id: 0
});

// 抽屉消息器
const drawerMessenger = useGlobalDrawerMessenger();

// 加载数据
const load{MODULE_NAME}s = async () => {
  loading.value = true;
  try {
    const response = await {module_name}Service.get{MODULE_NAME}s({
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value || undefined
    });
    
    // 【重要】使用response.items而不是response.data
    {module_name}s.value = response.items;
    totalCount.value = response.total;
  } catch (error) {
    handleApiError(error, '获取{MODULE_NAME}列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  load{MODULE_NAME}s();
};

// 【重要】清除搜索方法 - 空状态组件必需
const clearSearch = () => {
  searchQuery.value = '';
  handleSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  load{MODULE_NAME}s();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  load{MODULE_NAME}s();
};

// 新增
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增{MODULE_NAME}',
    component: '{MODULE_NAME}Form',
    props: {
      isEdit: false,
      editData: null
    },
    size: '33%'
  });
};

// 编辑
const handleEdit = (row: {MODULE_NAME}) => {
  drawerMessenger.showDrawer({
    title: '编辑{MODULE_NAME}',
    component: '{MODULE_NAME}Form',
    props: {
      isEdit: true,
      editData: {
        id: row.id,
        name: row.name,
        description: row.description || '',
        is_enabled: row.isEnabled  // 【重要】从驼峰数据转为下划线字段
      }
    },
    size: '38%'
  });
};

// 删除
const handleDelete = (row: {MODULE_NAME}) => {
  deleteDialog.value = {
    visible: true,
    title: '删除{MODULE_NAME}',
    content: `确定要删除{MODULE_NAME}「${row.name}」吗？此操作不可撤销。`,
    id: row.id
  };
};

const confirmDelete = async () => {
  try {
    await {module_name}Service.delete{MODULE_NAME}(deleteDialog.value.id);
    handleSuccessMessage('删除{MODULE_NAME}成功');
    await load{MODULE_NAME}s();
  } catch (error) {
    handleApiError(error, '删除{MODULE_NAME}失败');
  }
};

// 页面加载时获取数据
onMounted(() => {
  load{MODULE_NAME}s();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-title h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.item-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-name .el-icon {
  font-size: 16px;
  color: #3FC8DD;
}

.status-enabled {
  color: #67C23A;
  font-weight: 500;
}

.status-disabled {
  color: #F56C6C;
  font-weight: 500;
}
</style>

<!--
使用说明：
1. 替换所有 {MODULE_NAME} 为实际模块名（如：接口分组）
2. 替换所有 {module_name} 为实际模块名（如：interfaceGroup）
3. 根据实际需求调整表格列定义
4. 确保导入了EmptyState组件
5. 实现clearSearch方法
6. 使用response.items而不是response.data

重要规范：
- 所有列表页面必须添加EmptyState空状态组件
- 支持搜索和无数据两种状态
- 这是强制性的用户体验规范

【严格字段映射规范】：
1. 后端严格使用下划线命名(path_prefix, is_enabled)
2. 前端组件内部使用下划线命名与后端保持一致
3. 前端服务层使用convertToCamelCase/convertToSnakeCase进行转换
4. 表单组件的formData必须使用下划线命名
5. 传递给表单的editData必须转换为下划线命名
6. 绝不允许在组件内部混用驼峰和下划线命名

【editData字段映射示例】：
editData: {
  id: row.id,
  name: row.name,
  path_prefix: row.pathPrefix,    // 从驼峰转为下划线
  is_enabled: row.isEnabled       // 从驼峰转为下划线
}
-->
