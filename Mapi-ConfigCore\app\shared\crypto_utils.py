"""
AES加密工具
用于数据源密码的加密和解密
"""

import base64
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CryptoUtils:
    """AES加密工具类"""
    
    def __init__(self, password: str = None):
        """
        初始化加密工具
        
        Args:
            password: 加密密钥，如果不提供则从环境变量获取
        """
        if password is None:
            password = os.getenv("MAPI_CRYPTO_KEY", "mapi-default-secret-key-2024")
        
        self.password = password.encode()
        self.fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """创建Fernet加密对象"""
        # 使用PBKDF2从密码生成密钥
        salt = b'mapi_salt_2024'  # 固定盐值，生产环境应该使用随机盐值
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return Fernet(key)
    
    def encrypt(self, plaintext: str) -> str:
        """
        加密字符串
        
        Args:
            plaintext: 明文字符串
            
        Returns:
            加密后的base64字符串
        """
        if not plaintext:
            return ""
        
        try:
            encrypted_bytes = self.fernet.encrypt(plaintext.encode())
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
        except Exception as e:
            raise ValueError(f"加密失败: {e}")
    
    def decrypt(self, encrypted_text: str) -> str:
        """
        解密字符串
        
        Args:
            encrypted_text: 加密的base64字符串
            
        Returns:
            解密后的明文字符串
        """
        if not encrypted_text:
            return ""
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception as e:
            raise ValueError(f"解密失败: {e}")
    
    def is_encrypted(self, text: str) -> bool:
        """
        判断字符串是否已加密
        
        Args:
            text: 待判断的字符串
            
        Returns:
            True表示已加密，False表示未加密
        """
        if not text:
            return False
        
        try:
            # 尝试解密，如果成功说明是加密的
            self.decrypt(text)
            return True
        except:
            return False

# 全局加密工具实例
crypto_utils = CryptoUtils()

def encrypt_password(password: str) -> str:
    """加密密码的便捷函数"""
    return crypto_utils.encrypt(password)

def decrypt_password(encrypted_password: str) -> str:
    """解密密码的便捷函数"""
    return crypto_utils.decrypt(encrypted_password)

def is_password_encrypted(password: str) -> bool:
    """判断密码是否已加密的便捷函数"""
    return crypto_utils.is_encrypted(password)

# 测试函数
def test_crypto():
    """测试加密解密功能"""
    test_password = "test123456"
    
    print(f"原始密码: {test_password}")
    
    # 加密
    encrypted = encrypt_password(test_password)
    print(f"加密后: {encrypted}")
    
    # 解密
    decrypted = decrypt_password(encrypted)
    print(f"解密后: {decrypted}")
    
    # 验证
    print(f"加密解密是否一致: {test_password == decrypted}")
    print(f"是否已加密: {is_password_encrypted(encrypted)}")
    print(f"明文是否已加密: {is_password_encrypted(test_password)}")

if __name__ == "__main__":
    test_crypto()
