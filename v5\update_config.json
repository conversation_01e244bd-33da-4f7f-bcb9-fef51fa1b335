{"name": "测试接口名称", "path": "/api/v1/project/ht1", "method": "GET", "description": "ORM测试", "group_id": 6, "datasource_id": 4, "table_name": "ZDY_VW_GET_ZYHT_LIST", "is_enabled": true, "is_public": false, "orm_model_config": {"model_name": "ZdyVwGetZyhtListModel", "table_name": "ZDY_VW_GET_ZYHT_LIST", "sqlalchemy_model": {"fields": [{"name": "F_ID", "original_name": "F_ID", "type": "decimal", "primary_key": true, "nullable": false}, {"name": "F_NAME", "original_name": "F_NAME", "type": "n<PERSON><PERSON><PERSON>", "primary_key": false, "nullable": true}, {"name": "F_STATE", "original_name": "F_STATE", "type": "<PERSON><PERSON><PERSON>", "primary_key": false, "nullable": true}]}, "query_mapping": {"fuzzy_search_fields": ["F_NAME", "F_STATE"], "exact_match_fields": ["F_ID"], "default_sort": "F_ID", "default_order": "desc", "allowed_sort_fields": ["F_ID", "F_NAME", "F_STATE"]}}}