<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><User /></el-icon>
        <span class="title-text">客户端列表</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索客户端名称或ID"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-select v-model="filterStatus" placeholder="选择状态" clearable @change="handleSearch" style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="启用" value="enabled" />
          <el-option label="禁用" value="disabled" />
        </el-select>
        <el-button type="primary" @click="handleAdd">新增客户端</el-button>
        <el-button
          :icon="Refresh"
          @click="loadData"
          class="refresh-btn"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; min-width: 1000px;"
      :row-style="{ height: '60px' }"
      :cell-style="{ padding: '12px 0' }"
    >
      <!-- 空状态 -->
      <template #empty>
        <el-empty
          description="暂无客户端数据"
          :image-size="120"
        >
          <template #description>
            <p>还没有创建任何客户端</p>
            <p>点击上方"新增客户端"按钮开始创建</p>
          </template>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增客户端
          </el-button>
        </el-empty>
      </template>
        <el-table-column prop="clientName" label="客户端名称" min-width="150">
          <template #default="{ row }">
            <div class="client-name-cell">
              <el-button
                type="text"
                size="small"
                @click="copyToClipboard(row.clientId)"
                class="copy-icon-btn"
              >
                <el-icon><CopyDocument /></el-icon>
              </el-button>
              <span class="client-name">{{ row.clientName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="clientId" label="客户端ID" min-width="180">
          <template #default="{ row }">
            <span class="client-id">{{ row.clientId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="tokenExpiresIn" label="Token有效期" width="120">
          <template #default="{ row }">
            {{ formatTokenExpires(row.tokenExpiresIn) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'enabled' ? 'success' : 'danger'"
              @click="handleToggleStatus(row)"
              style="cursor: pointer;"
              title="点击切换状态"
            >
              {{ row.status === 'enabled' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleKeyManagement(row)">
              密钥管理
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              :disabled="!canDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <PaginationComponent
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, CopyDocument, User, Refresh } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import SearchComponent from '@/components/common/SearchComponent.vue'
import PaginationComponent from '@/components/common/PaginationComponent.vue'
import clientService from '@/services/client.service'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchQuery = ref('')
const filterStatus = ref('')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 生命周期
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      clientName: searchQuery.value,
      status: filterStatus.value
    }

    const response = await clientService.getClients(params)
    tableData.value = response.data
    totalCount.value = response.total
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error('加载客户端列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 新增客户端
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增客户端',
    component: 'ClientForm',
    props: {
      isEdit: false
    }
  })
}

// 编辑客户端
const handleEdit = (row) => {
  drawerMessenger.showDrawer({
    title: '编辑客户端',
    component: 'ClientForm',
    props: {
      isEdit: true,
      editData: { ...row }
    }
  })
}



// 密钥管理
const handleKeyManagement = (row) => {
  drawerMessenger.showDrawer({
    title: `密钥管理 - ${row.clientName}`,
    component: 'ClientKeyManagement',
    props: {
      clientData: { ...row }
    }
  })
}

// 切换状态
const handleToggleStatus = async (row) => {
  const action = row.status === 'enabled' ? '禁用' : '启用'
  const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
  try {
    await ElMessageBox.confirm(
      `确定要${action}客户端"${row.clientName}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await clientService.toggleClientStatus(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除客户端
const handleDelete = async (row) => {
  try {
    // 检查客户端是否有权限配置
    const permissions = await clientService.getClientPermissions(row.clientId)
    if (permissions.length > 0) {
      ElMessage.warning('该客户端已配置权限，请先清除权限配置后再删除')
      return
    }

    await ElMessageBox.confirm(
      `确定要删除客户端"${row.clientName}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await clientService.deleteClient(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}



// 分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 格式化Token有效期
const formatTokenExpires = (seconds) => {
  if (!seconds) return '永不过期'
  const days = Math.floor(seconds / 86400)
  if (days > 0) return `${days}天`
  const hours = Math.floor(seconds / 3600)
  if (hours > 0) return `${hours}小时`
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 检查是否可以删除客户端
const canDelete = (row) => {
  // 简单检查：如果客户端ID在mock权限数据中存在，则不允许删除
  // 实际项目中应该通过API实时检查
  try {
    // 这里先简单返回true，实际检查在删除时进行
    return true
  } catch (error) {
    console.error('检查删除权限失败:', error)
    return false
  }
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 客户端管理页面特有样式 */
.client-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}
.refresh-btn {
  height: 32px;
}
.copy-icon-btn {
  padding: 4px !important;
  min-height: auto !important;

  .el-icon {
    font-size: 14px;
    color: #409eff;
  }

  &:hover .el-icon {
    color: #66b1ff;
  }
}

.client-name {
  font-weight: 500;
}

.client-id {
  font-family: monospace;
  font-size: 13px;
  color: #606266;
}

/* 状态标签可点击样式 */
:deep(.el-tag) {
  transition: all 0.3s ease;
}

:deep(.el-tag:hover) {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
