/**
 * 接口分组管理相关类型定义
 * 用于：InterfaceGroup.vue 接口分组管理页面
 */

// 接口分组基本信息
export interface InterfaceGroup {
  id: number;
  name: string;                    // 分组名称
  pathPrefix: string;              // 分组别名（用于URL路径，如：glodon、hr、oa）- 只允许字母、数字、下划线、连字符
  description?: string;            // 分组描述
  isEnabled: boolean;              // 启用状态
  interfaceCount?: number;         // 包含的接口数量（用于列表显示）
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
}

// 接口分组创建/更新请求
export interface InterfaceGroupRequest {
  name: string;
  pathPrefix: string;
  description?: string;
  isEnabled?: boolean;             // 启用状态，默认为true
}

// 接口分组列表查询参数
export interface InterfaceGroupQuery {
  page?: number;
  pageSize?: number;
  search?: string;                 // 搜索关键词（分组名称或路径前缀）
}

// 接口分组列表响应
export interface InterfaceGroupListResponse {
  items: InterfaceGroup[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 路径前缀验证规则
export interface PathPrefixValidation {
  pattern: RegExp;                 // 验证正则表达式
  message: string;                 // 错误提示信息
  min_length: number;              // 最小长度
  max_length: number;              // 最大长度
}
