import { defineStore } from 'pinia';
import type { PageConfig } from '@/types/navigation';

// 定义Tab容器状态接口
interface TabContainerState {
  pendingTabActivation: PageConfig | null;
  openedTabs: PageConfig[]; // 已打开的页签列表
  modelActiveName: string; // 当前激活的标签页名称
  contextMenuVisible: boolean; // 右键菜单可见性
  contextMenuStyle: { left: string; top: string; }; // 右键菜单样式
  contextTab: PageConfig | null; // 当前右键点击的标签页
}

// 创建Pinia store
export const useTabContainerStore = defineStore('tabContainer', {
  state: (): TabContainerState => ({
    pendingTabActivation: null as PageConfig | null,
    openedTabs: [],
    modelActiveName: '',
    contextMenuVisible: false,
    contextMenuStyle: { left: '0px', top: '0px' },
    contextTab: null
  }),
  actions: {
    /**
     * 初始化默认页签
     * @param defaultPage - 默认页签配置
     * @description 2023-10-27 添加参数验证和空状态检查，防止覆盖已有页签
     */
    initDefaultTab(defaultPage: PageConfig) {
      // 验证默认页签配置是否有效
      if (!defaultPage || !defaultPage.name || !defaultPage.path) {
        console.error('初始化默认页签失败：无效的页签配置', defaultPage);
        return;
      }
      
      // 仅在没有打开的页签时初始化默认页签
      if (this.openedTabs.length === 0) {
        this.openedTabs = [{
          ...defaultPage,
          routePath: defaultPage.path,
          paneName: defaultPage.name
        }];
        this.modelActiveName = defaultPage.name;
      }
    },

    /**
     * 关闭指定页签
     * @param targetName - 要关闭的页签名称
     */
    closeCurrentTab(targetName: string) {
      this.openedTabs = this.openedTabs.filter(tab => tab.name !== targetName);
      if (this.modelActiveName === targetName && this.openedTabs.length > 0) {
        this.modelActiveName = this.openedTabs[0].name;
      }
    },

    /**
     * 关闭其他页签
     * @param name - 当前保留的页签名称
     */
    closeOtherTabs(name: string) {
      const currentTab = this.openedTabs.find(tab => tab.name === name);
      if (currentTab) {
        this.openedTabs = [currentTab];
        this.modelActiveName = name;
      }
    },

    /**
     * 关闭所有页签
     */
    closeAllTabs() {
      this.openedTabs = [];
      this.modelActiveName = '';
    },

    /**
     * 关闭左侧页签
     * @param name - 当前页签名称
     */
    closeLeftTabs(name: string) {
      const idx = this.openedTabs.findIndex(tab => tab.name === name);
      if (idx > 0) {
        this.openedTabs.splice(0, idx);
      }
    },

    /**
     * 关闭右侧页签
     * @param name - 当前页签名称
     */
    closeRightTabs(name: string) {
      const idx = this.openedTabs.findIndex(tab => tab.name === name);
      if (idx < this.openedTabs.length - 1) {
        this.openedTabs.splice(idx + 1);
      }
    },

    /**
     * 添加新页签
     * @param pageInfo - 新页签配置
     */
    addTab(pageInfo: PageConfig) {
      // 添加参数验证
      if (!pageInfo || !pageInfo.name || !pageInfo.path) {
        console.error('无效的页签配置:', pageInfo);
        return;
      }

      // 检查是否已存在相同name的页签
      const existingTab = this.openedTabs.find(tab => tab.name === pageInfo.name);
      if (existingTab) {
        this.pendingTabActivation = existingTab;
      } else {
        this.openedTabs.push({
          ...pageInfo,
          routePath: pageInfo.path,
          paneName: pageInfo.name
        });
      }
      // 激活已存在或新添加的页签
      // console.log('添加页签后，激活页签77777:', this.modelActiveName);
      // this.modelActiveName = pageInfo.name;
    },

    /**
     * 显示右键菜单
     * @param event - 鼠标事件
     * @param tab - 目标页签
     */
    showContextMenu(event: MouseEvent, tab: PageConfig) {
      this.contextTab = tab;
      this.contextMenuVisible = true;
      this.contextMenuStyle = {
        left: event.clientX + 'px',
        top: event.clientY + 'px',
      };
    },

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
      this.contextMenuVisible = false;
    }
  },
  getters: {
    /**
     * 获取当前激活的页签
     */
    // activeTab(): PageConfig | undefined {
    //   // return this.openedTabs.find(tab => tab.name === this.modelActiveName);
    // }
  }
});