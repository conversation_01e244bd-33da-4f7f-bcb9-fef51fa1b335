"""
接口配置标签关联数据模型
"""

from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, func, UniqueConstraint
from sqlalchemy.orm import relationship
from app.shared.database import Base


class InterfaceConfigTagModel(Base):
    """接口配置标签关联表模型"""
    
    __tablename__ = "interface_config_tags"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 关联字段
    interface_config_id = Column(Integer, ForeignKey('interface_configs.id', ondelete='CASCADE'), nullable=False, comment="接口配置ID")
    interface_tag_id = Column(Integer, ForeignKey('interface_tags.id', ondelete='CASCADE'), nullable=False, comment="接口标签ID")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    # 关联关系
    interface_config = relationship("InterfaceConfigModel", backref="tag_relations")
    interface_tag = relationship("InterfaceTagModel", backref="config_relations")
    
    # 唯一约束：同一个接口配置不能重复关联同一个标签
    __table_args__ = (
        UniqueConstraint('interface_config_id', 'interface_tag_id', name='uk_interface_config_tag'),
    )
    
    def __repr__(self):
        return f"<InterfaceConfigTag(interface_config_id={self.interface_config_id}, interface_tag_id={self.interface_tag_id})>"
    
    def to_dict(self):
        """转换为字典格式（驼峰命名，与前端保持一致）"""
        return {
            'id': self.id,
            'interfaceConfigId': self.interface_config_id,  # 转换为驼峰格式
            'interfaceTagId': self.interface_tag_id,  # 转换为驼峰格式
            'createdAt': self.created_at.isoformat() if self.created_at else None,  # 转换为驼峰格式
            'createdBy': self.created_by  # 转换为驼峰格式
        }
