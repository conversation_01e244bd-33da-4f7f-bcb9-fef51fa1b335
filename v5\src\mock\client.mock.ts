import type { 
  Client, 
  ClientPermission
} from '../types/client';

/**
 * 客户端Mock数据
 */
export const mockClients: Client[] = [
  {
    id: 1,
    clientId: 'AbCdEfGhIjKlMnOp',
    clientName: '内部管理系统',
    clientSecret: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6',
    description: '用于内部管理系统的API访问，包含用户管理、数据统计等功能',
    tokenExpiresIn: 2592000, // 30天
    status: 'enabled',
    createdAt: '2024-12-20 10:00:00',
    updatedAt: '2024-12-27 15:30:00',
    // 权限统计信息
    permissionCount: 8,
    groupCount: 3,
    permissionGroups: ['用户管理', '数据源管理', '接口管理'],
    permissionUpdatedAt: '2024-12-27 15:30:00'
  },
  {
    id: 2,
    clientId: 'QrStUvWxYzAbCdEf',
    clientName: '移动端应用',
    clientSecret: 'q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6',
    description: '移动端应用的API访问客户端，支持iOS和Android平台',
    tokenExpiresIn: 604800, // 7天
    status: 'enabled',
    createdAt: '2024-12-25 15:30:00',
    updatedAt: '2024-12-26 09:15:00',
    // 权限统计信息
    permissionCount: 5,
    groupCount: 2,
    permissionGroups: ['用户管理', '数据源管理'],
    permissionUpdatedAt: '2024-12-26 09:15:00'
  },
  {
    id: 3,
    clientId: 'GhIjKlMnOpQrStUv',
    clientName: '第三方集成系统',
    clientSecret: 'g1h2i3j4k5l6m7n8o9p0q1r2s3t4u5v6',
    description: '与第三方系统集成使用的客户端',
    tokenExpiresIn: 86400, // 1天
    status: 'disabled',
    createdAt: '2024-12-15 08:20:00',
    updatedAt: '2024-12-20 14:45:00',
    // 权限统计信息
    permissionCount: 0,
    groupCount: 0,
    permissionGroups: [],
    permissionUpdatedAt: null
  },
  {
    id: 4,
    clientId: 'WxYzAbCdEfGhIjKl',
    clientName: '数据分析平台',
    clientSecret: 'w1x2y3z4a5b6c7d8e9f0g1h2i3j4k5l6',
    description: '专用于数据分析和报表生成的客户端',
    tokenExpiresIn: 7776000, // 90天
    status: 'enabled',
    createdAt: '2024-12-10 16:45:00',
    updatedAt: '2024-12-22 11:20:00',
    // 权限统计信息
    permissionCount: 12,
    groupCount: 4,
    permissionGroups: ['用户管理', '数据源管理', '接口管理', '系统管理'],
    permissionUpdatedAt: '2024-12-22 11:20:00'
  },
  {
    id: 5,
    clientId: 'MnOpQrStUvWxYzAb',
    clientName: '测试环境客户端',
    clientSecret: 'm1n2o3p4q5r6s7t8u9v0w1x2y3z4a5b6',
    description: '用于测试环境的API调试和验证',
    tokenExpiresIn: 0, // 永不过期
    status: 'enabled',
    createdAt: '2024-12-01 12:00:00',
    updatedAt: '2024-12-15 10:30:00',
    // 权限统计信息
    permissionCount: 3,
    groupCount: 1,
    permissionGroups: ['接口管理'],
    permissionUpdatedAt: '2024-12-15 10:30:00'
  }
];

export const mockClientPermissions: ClientPermission[] = [
  { id: 1, clientId: 'AbCdEfGhIjKlMnOp', resourceType: 'group', resourceId: 'group_1', createdAt: '2024-12-20 10:00:00' },
  { id: 2, clientId: 'AbCdEfGhIjKlMnOp', resourceType: 'interface', resourceId: 'interface_5', createdAt: '2024-12-20 10:00:00' },
  { id: 3, clientId: 'QrStUvWxYzAbCdEf', resourceType: 'interface', resourceId: 'interface_1', createdAt: '2024-12-25 15:30:00' },
  { id: 4, clientId: 'QrStUvWxYzAbCdEf', resourceType: 'interface', resourceId: 'interface_2', createdAt: '2024-12-25 15:30:00' }
];

export const mockInterfaceTree = [
  {
    id: 'group_1',
    name: '用户管理',
    type: 'group',
    children: [
      {
        id: 'interface_1',
        name: '获取用户列表',
        path: '/api/users',
        method: 'GET',
        description: '获取系统用户列表，支持分页和搜索',
        accessType: 'public',
        status: 'enabled',
        groupId: 'group_1',
        type: 'interface'
      },
      {
        id: 'interface_2',
        name: '创建用户',
        path: '/api/users',
        method: 'POST',
        description: '创建新用户账号',
        accessType: 'private',
        status: 'enabled',
        groupId: 'group_1',
        type: 'interface'
      },
      {
        id: 'interface_3',
        name: '更新用户信息',
        path: '/api/users/{id}',
        method: 'PUT',
        description: '更新指定用户的信息',
        accessType: 'private',
        status: 'enabled',
        groupId: 'group_1',
        type: 'interface'
      }
    ]
  },
  {
    id: 'group_2',
    name: '数据源管理',
    type: 'group',
    children: [
      {
        id: 'interface_4',
        name: '获取数据源列表',
        path: '/api/datasources',
        method: 'GET',
        description: '获取所有数据源配置',
        accessType: 'public',
        status: 'enabled',
        groupId: 'group_2',
        type: 'interface'
      },
      {
        id: 'interface_5',
        name: '测试数据源连接',
        path: '/api/datasources/{id}/test',
        method: 'POST',
        description: '测试数据源连接是否正常',
        accessType: 'private',
        status: 'enabled',
        groupId: 'group_2',
        type: 'interface'
      }
    ]
  },
  {
    id: 'group_3',
    name: '接口管理',
    type: 'group',
    children: [
      {
        id: 'interface_6',
        name: '获取接口列表',
        path: '/api/interfaces',
        method: 'GET',
        description: '获取系统中所有接口配置',
        accessType: 'public',
        status: 'enabled',
        groupId: 'group_3',
        type: 'interface'
      },
      {
        id: 'interface_7',
        name: '创建接口',
        path: '/api/interfaces',
        method: 'POST',
        description: '创建新的接口配置',
        accessType: 'private',
        status: 'enabled',
        groupId: 'group_3',
        type: 'interface'
      }
    ]
  }
];
