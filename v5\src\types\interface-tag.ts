/**
 * 接口标签管理相关类型定义
 * 用于：InterfaceTag.vue 接口标签管理页面
 */

// 接口标签基本信息
export interface InterfaceTag {
  id: number;
  name: string;                    // 标签名称
  color: string;                   // 标签颜色（十六进制）
  description?: string;            // 标签描述
  isEnabled: boolean;              // 启用状态
  interfaceCount?: number;         // 使用该标签的接口数量（用于列表显示）
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
}

// 接口标签创建/更新请求
export interface InterfaceTagRequest {
  name: string;
  color?: string;                  // 可选，如果不提供则自动分配
  description?: string;
  isEnabled?: boolean;             // 启用状态，默认为true
}

// 接口标签列表查询参数
export interface InterfaceTagQuery {
  page?: number;
  page_size?: number;              // 注意：这个字段发送给后端，保持 snake_case
  search?: string;                 // 搜索关键词（标签名称或描述）
}

// 接口标签列表响应
export interface InterfaceTagListResponse {
  items: InterfaceTag[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 预设标签颜色
export interface TagColorOption {
  name: string;                    // 颜色名称
  value: string;                   // 颜色值（十六进制）
  description?: string;            // 颜色描述
}

// 标签颜色预设
export const TAG_COLOR_PRESETS: TagColorOption[] = [
  { name: '蓝色', value: '#3FC8DD', description: '主题蓝色' },
  { name: '绿色', value: '#67C23A', description: '成功绿色' },
  { name: '橙色', value: '#E6A23C', description: '警告橙色' },
  { name: '红色', value: '#F56C6C', description: '危险红色' },
  { name: '紫色', value: '#9C27B0', description: '紫色' },
  { name: '青色', value: '#17A2B8', description: '信息青色' },
  { name: '灰色', value: '#909399', description: '次要灰色' },
  { name: '深蓝', value: '#409EFF', description: '深蓝色' }
];
