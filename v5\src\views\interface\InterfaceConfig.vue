<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Setting /></el-icon>
        <span class="title-text">接口配置管理</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索接口名称或路径"
          width="400px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-select v-model="filterGroup" placeholder="选择分组" clearable @change="handleSearch" style="width: 200px;">
          <el-option label="全部" :value="''" />
          <el-option
            v-for="group in interfaceGroups"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
        <el-select v-model="filterMethod" placeholder="HTTP方法" clearable @change="handleSearch" style="width: 150px;">
          <el-option label="全部" :value="''" />
          <el-option
            v-for="method in httpMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          />
        </el-select>
        <el-button type="primary" @click="handleAdd">新增接口</el-button>
        <el-button type="success" @click="handleTestAll" :loading="testingAll">全部测试</el-button>
        <el-button @click="loadInterfaceConfigs">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <el-tabs v-model="mainActiveTab" class="page-tabs">
      <el-tab-pane label="接口列表管理" name="config">
        <!-- 接口配置列表 -->
        <el-table
          v-loading="loading"
          :data="interfaceConfigs"
          style="width: 100%;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
          <!-- 空状态 -->
          <template #empty>
            <el-empty
              description="暂无接口配置数据"
              :image-size="120"
            >
              <template #description>
                <p>还没有创建任何接口配置</p>
                <p>点击上方"新增接口"按钮开始创建</p>
              </template>
              <el-button type="primary" @click="drawerMessenger.openDrawer()">
                <el-icon><Plus /></el-icon>
                新增接口
              </el-button>
            </el-empty>
          </template>
          <el-table-column label="接口名称" prop="name" width="150" align="left" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="interface-name">
                <span class="name-text">{{ row.name }}</span>
                <div class="interface-tags" v-if="row.tagNames && row.tagNames.length > 0">
                  <el-tag
                    v-for="tagName in row.tagNames"
                    :key="tagName"
                    size="small"
                    type="info"
                  >
                    {{ tagName }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="路由信息" width="250" align="left">
            <template #default="{ row }">
              <div class="route-info">
                <el-tag
                  :color="getMethodColor(row.method)"
                  size="small"
                  style="color: white; border: none; margin-right: 8px;"
                >
                  {{ row.method }}
                </el-tag>
                <code class="path-text">{{ row.path }}</code>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分组/数据源" width="150" align="left">
            <template #default="{ row }">
              <div class="group-datasource-info">
                <div class="group-name">
                  <el-icon><Coin /></el-icon>
                  <span class="group-text">{{ row.groupName || '未分组' }}</span>
                </div>
                <div class="datasource-name">
                  <el-icon><Setting /></el-icon>
                  <span class="datasource-text">{{ row.datasourceName }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态/权限" width="100" align="center">
            <template #default="{ row }">
              <div class="status-permission-info">
                <div class="status-row">
                  <el-tag :type="row.isEnabled ? 'success' : 'danger'" size="small">
                    {{ row.isEnabled ? '启用' : '禁用' }}
                  </el-tag>
                </div>
                <div class="permission-row">
                  <el-tag :type="row.isPublic ? 'warning' : 'info'" size="small">
                    {{ row.isPublic ? '公开' : '私有' }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="测试状态" width="100" align="center">
            <template #default="{ row }">
              <div class="test-status">
                <el-tag
                  v-if="row.testStatus === 'success'"
                  type="success"
                  size="small"
                >
                  成功
                </el-tag>
                <el-tag
                  v-else-if="row.testStatus === 'failed'"
                  type="danger"
                  size="small"
                >
                  失败
                </el-tag>
                <el-tag
                  v-else-if="row.testStatus === 'pending'"
                  type="warning"
                  size="small"
                >
                  测试中
                </el-tag>
                <span v-else class="no-test">未测试</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" prop="updatedAt" width="120" align="center">
            <template #default="{ row }">
              <span class="time-text">{{ row.updatedAt }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button size="small" type="info" @click="handleTest(row)">测试</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="删除接口"
      :content="`确定要删除接口 '${deleteItem?.name}' 吗？删除后相关的测试记录也将被清除。`"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Setting, Coin, Refresh, Plus } from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import interfaceConfigService from '@/services/interface-config.service';
import interfaceGroupService from '@/services/interface-group.service';
import interfaceTagService from '@/services/interface-tag.service';
import dataSourceService from '@/services/datasource.service';
import type { InterfaceConfig, InterfaceConfigRequest, HttpMethodOption } from '@/types/interface-config';
import type { InterfaceGroup } from '@/types/interface-group';
import type { InterfaceTag } from '@/types/interface-tag';
import type { DataSource } from '@/types/datasource';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { HTTP_METHODS } from '@/types/interface-config';
import { extractErrorMessage } from '@/utils/common-utils';
import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';

// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();

// 页签状态 - 用于主页面的tab
const mainActiveTab = ref('config');

// 列表数据
const loading = ref(false);
const interfaceConfigs = ref<InterfaceConfig[]>([]);
const searchQuery = ref('');
const filterGroup = ref<number | undefined>();
const filterMethod = ref<string | undefined>();
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);

// 测试相关
const testingAll = ref(false);
const testingItems = ref<Set<number>>(new Set());

// 下拉选项数据
const interfaceGroups = ref<InterfaceGroup[]>([]);
const interfaceTags = ref<InterfaceTag[]>([]);
const dataSources = ref<DataSource[]>([]);
const httpMethods: HttpMethodOption[] = HTTP_METHODS;

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<InterfaceConfig | null>(null);

// 加载接口配置列表
const loadInterfaceConfigs = async () => {
  loading.value = true;
  try {
    const response = await interfaceConfigService.getInterfaceConfigs({
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value || undefined,
      groupId: filterGroup.value || undefined,
      method: filterMethod.value || undefined
    });

    interfaceConfigs.value = response.items;
    totalCount.value = response.total;
  } catch (error) {
    console.error('加载接口配置列表失败:', error);
    ElMessage.error('加载接口配置列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新到第一页的方法
const loadInterfaceConfigsToFirstPage = async () => {
  currentPage.value = 1;
  await loadInterfaceConfigs();
};

// 获取HTTP方法颜色
const getMethodColor = (method: string): string => {
  const methodConfig = httpMethods.find(m => m.value === method);
  return methodConfig?.color || '#909399';
};

// 测试单个接口
const handleTest = (row: InterfaceConfig) => {
  // 跳转到接口测试页面，并传递接口ID
  window.open(`/interface-test?id=${row.id}`, '_blank');
  ElMessage.info(`开始测试接口: ${row.name}`);
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;

  try {
    await interfaceConfigService.deleteInterfaceConfig(deleteItem.value.id);
    ElMessage.success('删除成功');

    // 刷新列表到第一页
    loadInterfaceConfigsToFirstPage();
  } catch (error) {
    console.error('删除失败:', error);
    ElMessage.error('删除失败');
  } finally {
    deleteDialogVisible.value = false;
    deleteItem.value = null;
  }
};


// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadInterfaceConfigs();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadInterfaceConfigs();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadInterfaceConfigs();
};


// 新增接口
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增接口',
    component: 'InterfaceConfigForm',
    props: {
      isEdit: false,
      editData: null
    },
    size: '40%'
  });
};

// 编辑接口
const handleEdit = async (row: InterfaceConfig) => {
  console.log('🔍 handleEdit 接收到的 row 数据:', row);
  console.log('🔍 row 中的关键字段:', {
    id: row.id,
    groupId: row.groupId,
    datasourceId: row.datasourceId
  });

  try {
    // 通过API获取完整的接口配置数据
    console.log('🔄 通过API获取完整数据，ID:', row.id);
    const fullData = await interfaceConfigService.getInterfaceConfigById(row.id);
    console.log('🔍 API返回的完整数据:', fullData);

    if (!fullData) {
      ElMessage.error('获取接口配置详情失败');
      return;
    }

    drawerMessenger.showDrawer({
      title: '编辑接口',
      component: 'InterfaceConfigForm',
      props: {
        isEdit: true,
        editId: row.id  // ✅ 只传递ID，让抽屉内部获取数据
      },
      size: '40%'
    });

  } catch (error) {
    console.error('获取接口配置详情失败:', error);
    ElMessage.error('获取接口配置详情失败');
  }
};

// 全部测试
const handleTestAll = async () => {
  if (interfaceConfigs.value.length === 0) {
    ElMessage.warning('没有可测试的接口');
    return;
  }

  testingAll.value = true;

  try {
    // 只测试启用的接口
    const enabledInterfaces = interfaceConfigs.value.filter(item => item.isEnabled);

    if (enabledInterfaces.length === 0) {
      ElMessage.warning('没有启用的接口可以测试');
      return;
    }

    ElMessage.info(`开始测试 ${enabledInterfaces.length} 个启用的接口`);

    // 模拟批量测试过程
    for (let i = 0; i < enabledInterfaces.length; i++) {
      const item = enabledInterfaces[i];
      testingItems.value.add(item.id);

      // 模拟每个接口测试时间
      await new Promise(resolve => setTimeout(resolve, 1000));

      testingItems.value.delete(item.id);
    }

    ElMessage.success(`全部测试完成，共测试 ${enabledInterfaces.length} 个接口`);
  } catch (error: any) {
    console.error('批量测试失败:', error);
    ElMessage.error(error.message || '批量测试失败');
  } finally {
    testingAll.value = false;
    testingItems.value.clear();
  }
};


// 删除接口
const handleDelete = (row: InterfaceConfig) => {
  deleteItem.value = row;
  deleteDialogVisible.value = true;
};




// 加载下拉选项数据
const loadOptions = async () => {
  try {
    // 并行加载所有选项数据（限制在100以内）
    const [groupsResponse, tagsResponse, dataSourcesResponse] = await Promise.all([
      interfaceGroupService.getInterfaceGroups({ page: 1, pageSize: 100 }),
      interfaceTagService.getInterfaceTags({ page: 1, page_size: 100 }),
      dataSourceService.getDataSources(1, 100)
    ]);

    interfaceGroups.value = groupsResponse.items;
    interfaceTags.value = tagsResponse.items;
    dataSources.value = dataSourcesResponse.items;
  } catch (error) {
    console.error('加载选项数据失败:', error);
  }
};

// 页面加载时注册刷新机制
onMounted(() => {
  loadOptions();
  loadInterfaceConfigs();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.INTERFACE_CONFIG,
    loadInterfaceConfigs,           // 保持当前页刷新
    loadInterfaceConfigsToFirstPage // 跳转第一页刷新
  );
});

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.INTERFACE_CONFIG);
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;


/* 接口名称样式 */
.interface-name {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

.name-text {
  font-weight: 500;
  color: #303133;
  word-break: break-word;
}

.interface-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}



.path-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}



/* 分组和数据源样式 */
.group-text,
.datasource-text {
  color: #606266;
  font-size: 13px;
}



/* 测试状态样式 */
.test-status .no-test {
  color: #C0C4CC;
  font-size: 12px;
}

/* 时间文本样式 */
.time-text {
  color: #909399;
  font-size: 13px;
}



/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
  align-items: center;
}








/* 合并列样式 */
.route-info {
  display: flex;
  align-items: center;
  gap: 0;
}



.group-datasource-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.group-name,
.datasource-name {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.group-name .el-icon {
  font-size: 12px;
  color: #3FC8DD;
}

.datasource-name .el-icon {
  font-size: 12px;
  color: #909399;
}

.group-text,
.datasource-text {
  color: #606266;
  font-size: 12px;
}

.status-permission-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.status-row,
.permission-row {
  display: flex;
  justify-content: center;
}
</style>
