"""
接口配置数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, func
from sqlalchemy.orm import relationship
from app.shared.database import Base


class InterfaceConfigModel(Base):
    """接口配置表模型"""
    
    __tablename__ = "interface_configs"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="接口名称")
    path = Column(String(200), nullable=False, unique=True, comment="接口路径")
    method = Column(String(10), nullable=False, comment="HTTP方法")
    description = Column(Text, nullable=True, comment="接口描述")
    
    # 关联信息
    group_id = Column(Integer, ForeignKey('interface_groups.id'), nullable=False, comment="所属分组ID")
    datasource_id = Column(Integer, ForeignKey('data_sources.id'), nullable=False, comment="数据源ID")
    table_name = Column(String(100), nullable=False, comment="数据表名")
    table_type = Column(String(20), nullable=False, default='table', comment="数据表类型(table/view/procedure)")
    
    # 配置信息
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    is_public = Column(Boolean, nullable=False, default=False, comment="是否公开(无需认证)")
    query_fields = Column(Text, nullable=True, comment="可查询字段(JSON格式)")
    required_fields = Column(Text, nullable=True, comment="必填字段(JSON格式)")
    response_fields = Column(Text, nullable=True, comment="响应字段(JSON格式)")

    # ORM模型配置
    orm_model_config = Column(Text, nullable=True, comment="ORM模型配置(JSON格式)")
    orm_model_name = Column(String(100), nullable=True, comment="ORM模型名称")
    orm_relationships = Column(Text, nullable=True, comment="ORM关联关系配置(JSON格式)")

    # 参数配置
    parameter_config = Column(Text, nullable=True, comment="参数配置(JSON格式)")
    visual_config = Column(Text, nullable=True, comment="可视化配置(JSON格式)")
    validation_rules = Column(Text, nullable=True, comment="验证规则配置(JSON格式)")
    
    # 性能配置
    cache_duration = Column(Integer, nullable=False, default=300, comment="缓存时长(秒)")
    rate_limit = Column(Integer, nullable=False, default=100, comment="速率限制(次/分钟)")
    
    # 测试信息
    last_test_at = Column(DateTime, nullable=True, comment="最后测试时间")
    test_status = Column(String(20), nullable=True, comment="测试状态(success/failed)")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    # 关联关系 - 使用字符串引用避免循环导入
    group = relationship("InterfaceGroupModel", backref="interfaces", lazy="select")
    datasource = relationship("DataSourceModel", backref="interfaces", lazy="select")
    
    def __repr__(self):
        return f"<InterfaceConfig(id={self.id}, name='{self.name}', path='{self.path}', method='{self.method}')>"
    
    def to_dict(self):
        """转换为字典格式（驼峰命名，与前端保持一致）"""
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            'method': self.method,
            'description': self.description,
            'groupId': self.group_id,  # 转换为驼峰格式
            'datasourceId': self.datasource_id,  # 转换为驼峰格式
            'tableName': self.table_name,  # 转换为驼峰格式
            'tableType': self.table_type,  # 转换为驼峰格式
            'isEnabled': self.is_enabled,  # 转换为驼峰格式
            'isPublic': self.is_public,  # 转换为驼峰格式
            'queryFields': self.query_fields,  # 转换为驼峰格式
            'requiredFields': self.required_fields,  # 转换为驼峰格式
            'responseFields': self.response_fields,  # 转换为驼峰格式
            'ormModelConfig': self.orm_model_config,  # ORM模型配置
            'ormModelName': self.orm_model_name,  # ORM模型名称
            'ormRelationships': self.orm_relationships,  # ORM关联关系
            'parameterConfig': self.parameter_config,  # 参数配置
            'visualConfig': self.visual_config,  # 可视化配置
            'validationRules': self.validation_rules,  # 验证规则
            'cacheDuration': self.cache_duration,  # 转换为驼峰格式
            'rateLimit': self.rate_limit,  # 转换为驼峰格式
            'lastTestAt': self.last_test_at.isoformat() if self.last_test_at else None,  # 转换为驼峰格式
            'testStatus': self.test_status,  # 转换为驼峰格式
            'createdAt': self.created_at.isoformat() if self.created_at else None,  # 转换为驼峰格式
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,  # 转换为驼峰格式
            'createdBy': self.created_by  # 转换为驼峰格式
        }
