#!/usr/bin/env python3
"""
测试日志格式 - 验证没有空行
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.core import log_init
from app.shared.core.log_util import LogUtil

def test_no_empty_lines():
    """测试日志格式没有空行"""
    print("🧪 测试日志格式（验证没有空行）")
    
    # 初始化日志系统
    log_init.setup_logging()
    
    # 记录测试日志
    LogUtil.warning("测试警告日志格式", 
                    test_id="NO_EMPTY_LINES_001",
                    format_check=True,
                    expected_result="只有分隔线，没有空行")
    
    print("✅ 测试日志已记录到warning_*.log")
    print("🔍 请检查最新的日志条目格式")

if __name__ == "__main__":
    test_no_empty_lines()
