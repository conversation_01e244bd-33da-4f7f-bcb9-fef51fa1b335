<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Cpu /></el-icon>
        <span class="title-text">接口测试</span>
      </div>
      <div class="header-actions">
        <el-select
          v-model="filterStatus"
          placeholder="--请选择测试结果--"
          clearable
          @change="handleStatusChange"
          style="width: 200px;"
        >
          <el-option label="全部" value="" />
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
        </el-select>
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索接口名称、路径、分组"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-button
          type="success"
          @click="handleBatchTest"
          :loading="batchTesting"
        >
          测试全部
        </el-button>
        <el-button @click="loadTestRecords">刷新</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <!-- 接口测试页签 -->
      <el-tab-pane label="接口测试分析" name="test">
        <div class="test-section">
          <!-- 测试统计卡片 -->
          <div class="stats-cards">
            <!-- 总体测试统计 -->
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ testStats.total_tests }}</div>
                <div class="stat-label">总测试次数</div>
                <div class="stat-extra">
                  <span class="extra-item">今日: {{ testStats.today_tests }}</span>
                  <span class="extra-item">本周: {{ testStats.this_week_tests }}</span>
                </div>
              </div>
            </el-card>

            <!-- 成功率统计 -->
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number success">{{ testStats.success_rate }}%</div>
                <div class="stat-label">测试成功率</div>
                <div class="stat-extra">
                  <span class="extra-item success">成功: {{ testStats.success_tests }}</span>
                  <span class="extra-item danger">失败: {{ testStats.failed_tests }}</span>
                </div>
              </div>
            </el-card>

            <!-- 接口统计 -->
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ testStats.total_interfaces }}</div>
                <div class="stat-label">总接口数量</div>
                <div class="stat-extra">
                  <span class="extra-item">已测: {{ testStats.tested_interfaces }}</span>
                  <span class="extra-item warning">未测: {{ testStats.untested_interfaces }}</span>
                </div>
              </div>
            </el-card>

            <!-- 分组统计 -->
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ testStats.total_groups }}</div>
                <div class="stat-label">接口分组数</div>
                <div class="stat-extra">
                  <span class="extra-item">覆盖: {{ Math.round((testStats.tested_interfaces / testStats.total_interfaces) * 100) }}%</span>
                  <span :class="['extra-item', getTrendClass(testStats.recent_test_trend)]">
                    {{ getTrendText(testStats.recent_test_trend) }}
                  </span>
                </div>
              </div>
            </el-card>

            <!-- 响应时间统计 -->
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ testStats.avg_response_time }}ms</div>
                <div class="stat-label">平均响应时间</div>
                <div class="stat-extra">
                  <span class="extra-item success">最快: {{ testStats.fastest_response_time }}ms</span>
                  <span class="extra-item warning">最慢: {{ testStats.slowest_response_time }}ms</span>
                </div>
              </div>
            </el-card>

            <!-- 热门接口 -->
            <el-card class="stat-card" v-if="testStats.most_tested_interface">
              <div class="stat-content">
                <div class="stat-interface-name">{{ testStats.most_tested_interface }}</div>
                <div class="stat-label">测试最多的接口</div>
                <div class="stat-extra">
                  <span class="extra-item">{{ formatLastTestTime(testStats.last_test_time) }}</span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 测试表单 -->
          <el-card class="test-form-card" v-if="selectedInterfaceId">
            <template #header>
              <div class="card-header">
                <span>测试配置</span>
                <el-button type="primary" @click="executeTest" :loading="testing">
                  执行测试
                </el-button>
              </div>
            </template>

            <el-form :model="testForm" label-width="100px">
              <el-form-item label="测试名称">
                <el-input
                  v-model="testForm.test_name"
                  placeholder="请输入测试名称（可选）"
                  maxlength="100"
                />
              </el-form-item>

              <el-form-item label="请求参数">
                <div class="json-editor">
                  <el-input
                    v-model="testParamsJson"
                    type="textarea"
                    :rows="6"
                    placeholder='请输入JSON格式的请求参数，例如：{"page": 1, "page_size": 10}'
                  />
                </div>
              </el-form-item>

              <el-form-item label="请求头">
                <div class="json-editor">
                  <el-input
                    v-model="testHeadersJson"
                    type="textarea"
                    :rows="4"
                    placeholder='请输入JSON格式的请求头，例如：{"Authorization": "Bearer token"}'
                  />
                </div>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 测试结果 -->
          <el-card class="test-result-card" v-if="testResult">
            <template #header>
              <div class="card-header">
                <span>测试结果</span>
                <el-tag
                  :type="testResult.success ? 'success' : 'danger'"
                  size="large"
                >
                  {{ testResult.success ? '成功' : '失败' }}
                </el-tag>
              </div>
            </template>

            <div class="test-result-content">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="result-item">
                    <div class="result-label">状态码</div>
                    <div class="result-value">{{ testResult.status_code }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item">
                    <div class="result-label">响应时间</div>
                    <div class="result-value">{{ testResult.response_time }}ms</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item">
                    <div class="result-label">测试时间</div>
                    <div class="result-value">{{ testResult.test_time }}</div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="result-item">
                    <div class="result-label">数据大小</div>
                    <div class="result-value">{{ getDataSize(testResult.response_data) }}</div>
                  </div>
                </el-col>
              </el-row>

              <div class="response-section" v-if="testResult.error_message">
                <div class="section-title">错误信息</div>
                <div class="error-message">{{ testResult.error_message }}</div>
              </div>

              <div class="response-section">
                <div class="section-title">响应数据</div>
                <div class="json-viewer">
                  <pre>{{ JSON.stringify(testResult.response_data, null, 2) }}</pre>
                </div>
              </div>

              <div class="response-section">
                <div class="section-title">响应头</div>
                <div class="json-viewer">
                  <pre>{{ JSON.stringify(testResult.response_headers, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 测试记录页签 -->
      <el-tab-pane label="测试记录列表" name="records">
        <!-- 测试记录列表 -->
        <el-table
          v-loading="loading"
          :data="testRecords"
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
          <el-table-column label="接口信息" width="180">
            <template #default="{ row }">
              <div class="interface-info">
                <div class="interface-name">{{ row.interface_name }}</div>
                <div class="interface-path">
                  <el-tag
                    :color="getMethodColor(row.interface_method)"
                    size="small"
                    style="color: white; border: none; margin-right: 6px;"
                  >
                    {{ row.interface_method }}
                  </el-tag>
                  <code class="path-text">{{ row.interface_path }}</code>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="测试名称" prop="test_name" width="150">
            <template #default="{ row }">
              <span class="test-name">{{ row.test_name || '快速测试' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="测试结果" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.success ? 'success' : 'danger'"
                size="small"
              >
                {{ row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态码" prop="response_status" width="100" align="center">
            <template #default="{ row }">
              <span class="status-code" :class="getStatusClass(row.response_status)">
                {{ row.response_status }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="响应时间" prop="response_time" width="120" align="center">
            <template #default="{ row }">
              <span class="response-time">{{ row.response_time }}ms</span>
            </template>
          </el-table-column>

          <el-table-column label="测试时间" prop="test_time" width="160">
            <template #default="{ row }">
              <span class="time-text">{{ row.test_time }}</span>
            </template>
          </el-table-column>

          <el-table-column label="测试人" prop="tester" width="100">
            <template #default="{ row }">
              <span class="tester-text">{{ row.tester || '系统' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="handleViewDetail(row)">
                  查看详情
                </el-button>
                <el-button type="warning" size="small" @click="handleRetestInterface(row)">
                  重新测试
                </el-button>
                <el-button type="success" size="small" @click="handleDebugInterface(row)">
                  接口调试
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-tab-pane>

      <!-- 接口调试页面 -->
      <el-tab-pane label="接口调试" name="debug">
        <!-- 提示信息 -->
        <div v-if="!debugInterface" class="debug-empty-state">
          <el-empty description='请从测试记录列表中点击"接口调试"按钮来调试接口'>
            <el-button type="primary" @click="activeTab = 'records'">
              返回测试记录列表
            </el-button>
          </el-empty>
        </div>

        <!-- 调试界面 -->
        <div v-else class="debug-container">
          <!-- 左侧：请求配置 -->
          <div class="request-panel">
            <div class="panel-header">
              <div class="debug-interface-info">
                <div class="interface-main-info">
                  <span class="interface-group" v-if="debugInterface.group_name">
                    【{{ debugInterface.group_name }}】
                  </span>
                  <span class="interface-name-text">{{ debugInterface.name }}</span>
                  <span class="interface-separator">•</span>
                  <el-tag
                    :color="getMethodColor(debugInterface.method)"
                    size="small"
                    class="method-tag-debug"
                  >
                    {{ debugInterface.method }}
                  </el-tag>
                  <code class="path-text-debug">{{ debugInterface.path }}</code>
                </div>
              </div>
              <el-button type="primary" @click="executeDebugTest" :loading="debugTesting" class="send-request-btn">
                <el-icon><CaretRight /></el-icon>
                发送请求
              </el-button>
            </div>

            <!-- 请求配置标签页 -->
            <el-tabs v-model="debugRequestTab" class="request-tabs">
              <!-- 请求参数 -->
              <el-tab-pane label="Params" name="params">
                <div class="params-section">
                  <div class="params-header">
                    <span>Query Parameters</span>
                    <el-button size="small" @click="addDebugParam">添加参数</el-button>
                  </div>
                  <div class="params-table">
                    <div class="param-row header-row">
                      <div class="param-enabled">启用</div>
                      <div class="param-key">参数名</div>
                      <div class="param-value">参数值</div>
                      <div class="param-desc">描述</div>
                      <div class="param-actions">操作</div>
                    </div>
                    <div
                      v-for="(param, index) in debugParams"
                      :key="index"
                      class="param-row"
                    >
                      <el-checkbox v-model="param.enabled" class="param-enabled" />
                      <el-input
                        v-model="param.key"
                        placeholder="参数名"
                        size="small"
                        class="param-key"
                      />
                      <el-input
                        v-model="param.value"
                        placeholder="参数值"
                        size="small"
                        class="param-value"
                      />
                      <el-input
                        v-model="param.description"
                        placeholder="描述"
                        size="small"
                        class="param-desc"
                      />
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeDebugParam(index)"
                        class="param-actions"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 请求头 -->
              <el-tab-pane label="Headers" name="headers">
                <div class="headers-section">
                  <div class="headers-header">
                    <span>Request Headers</span>
                    <el-button size="small" @click="addDebugHeader">添加请求头</el-button>
                  </div>
                  <div class="headers-table">
                    <div class="headers-row headers-header-row">
                      <div class="header-enabled">启用</div>
                      <div class="header-key">Header名</div>
                      <div class="header-value">Header值</div>
                      <div class="header-actions">操作</div>
                    </div>
                    <div
                      v-for="(header, index) in debugHeaders"
                      :key="index"
                      class="headers-row"
                    >
                      <el-checkbox v-model="header.enabled" class="header-enabled" />
                      <el-input
                        v-model="header.key"
                        placeholder="Header名"
                        size="small"
                        class="header-key"
                      />
                      <el-input
                        v-model="header.value"
                        placeholder="Header值"
                        size="small"
                        class="header-value"
                      />
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeDebugHeader(index)"
                        class="header-actions"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>



              <!-- 请求体 -->
              <el-tab-pane label="Body" name="body" v-if="debugNeedsBody">
                <div class="body-section">
                  <div class="body-type-selector">
                    <el-radio-group v-model="debugBodyType">
                      <el-radio value="json">JSON</el-radio>
                      <el-radio value="form">Form Data</el-radio>
                      <el-radio value="raw">Raw</el-radio>
                    </el-radio-group>
                  </div>
                  <div class="body-content">
                    <el-input
                      v-if="debugBodyType === 'json' || debugBodyType === 'raw'"
                      v-model="debugRequestBody"
                      type="textarea"
                      :rows="12"
                      placeholder="请输入请求体内容"
                      class="body-textarea"
                    />
                    <div v-else-if="debugBodyType === 'form'" class="form-data">
                      <div class="form-data-table">
                        <div class="form-row header-row">
                          <div class="form-enabled">启用</div>
                          <div class="form-key">字段名</div>
                          <div class="form-value">字段值</div>
                          <div class="form-type">类型</div>
                          <div class="form-actions">操作</div>
                        </div>
                        <div
                          v-for="(field, index) in debugFormData"
                          :key="index"
                          class="form-row"
                        >
                          <el-checkbox v-model="field.enabled" class="form-enabled" />
                          <el-input
                            v-model="field.key"
                            placeholder="字段名"
                            size="small"
                            class="form-key"
                          />
                          <el-input
                            v-model="field.value"
                            placeholder="字段值"
                            size="small"
                            class="form-value"
                          />
                          <el-select
                            v-model="field.type"
                            size="small"
                            class="form-type"
                          >
                            <el-option label="Text" value="text" />
                            <el-option label="File" value="file" />
                          </el-select>
                          <el-button
                            size="small"
                            type="danger"
                            @click="removeDebugFormField(index)"
                            class="form-actions"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                      <el-button size="small" @click="addDebugFormField">添加字段</el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 右侧：响应结果 -->
          <div class="response-panel">
            <div class="panel-header">
              <h3>响应结果</h3>
              <div class="response-status" v-if="debugResult">
                <el-tag
                  :type="debugResult.success ? 'success' : 'danger'"
                  size="large"
                >
                  {{ debugResult.status_code }}
                </el-tag>
                <span class="response-time">{{ debugResult.response_time }}ms</span>
                <span class="response-size">{{ getResponseSize(debugResult.response_data) }}</span>
              </div>
            </div>

            <!-- 响应内容 -->
            <div class="response-content" v-if="debugResult">
              <el-tabs v-model="debugResponseTab" class="response-tabs">
                <!-- 响应体 -->
                <el-tab-pane label="Response" name="response">
                  <div class="response-body">
                    <div class="response-toolbar">
                      <el-button size="small" @click="formatDebugJson">格式化JSON</el-button>
                      <el-button size="small" @click="copyDebugResponse">复制响应</el-button>
                      <el-button size="small" @click="downloadDebugResponse">下载响应</el-button>
                    </div>
                    <el-input
                      v-model="debugFormattedResponse"
                      type="textarea"
                      readonly
                      class="response-textarea"
                    />
                  </div>
                </el-tab-pane>

                <!-- 响应头 -->
                <el-tab-pane label="Headers" name="response-headers">
                  <div class="response-headers">
                    <div
                      v-for="(value, key) in debugResult.response_headers"
                      :key="key"
                      class="response-header-item"
                    >
                      <span class="header-name">{{ key }}:</span>
                      <span class="header-value">{{ value }}</span>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 测试信息 -->
                <el-tab-pane label="Test Info" name="test-info">
                  <div class="test-info">
                    <div class="info-item">
                      <span class="info-label">状态码:</span>
                      <span class="info-value">{{ debugResult.status_code }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">响应时间:</span>
                      <span class="info-value">{{ debugResult.response_time }}ms</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">响应大小:</span>
                      <span class="info-value">{{ getResponseSize(debugResult.response_data) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">测试时间:</span>
                      <span class="info-value">{{ debugResult.test_time }}</span>
                    </div>
                    <div class="info-item" v-if="debugResult.error_message">
                      <span class="info-label">错误信息:</span>
                      <span class="info-value error">{{ debugResult.error_message }}</span>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-response">
              <el-empty description="点击发送请求查看响应结果">
                <template #image>
                  <el-icon size="60"><Connection /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 测试详情已重构为抽屉组件 InterfaceTestDetailForm.vue -->




  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Cpu, CaretRight, Connection } from '@element-plus/icons-vue';

import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import interfaceTestService from '@/services/interface-test.service';
import interfaceConfigService from '@/services/interface-config.service';
import type { InterfaceTestRecord, InterfaceTestRequest, InterfaceTestResponse, TestStatistics } from '@/types/interface-test';
import type { InterfaceConfig } from '@/types/interface-config';
import { HTTP_METHODS } from '@/types/interface-config';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { extractErrorMessage } from '@/utils/common-utils';

// 抽屉通信
const drawerMessenger = useGlobalDrawerMessenger();

// 页签状态
const activeTab = ref('test');

// 列表数据
const loading = ref(false);
const testRecords = ref<InterfaceTestRecord[]>([]);
const searchQuery = ref('');
const filterStatus = ref<string | undefined>();
const selectedInterfaceId = ref<number | undefined>();
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);

// 测试相关变量
const batchTesting = ref(false);

// 下拉选项数据
const interfaceConfigs = ref<InterfaceConfig[]>([]);

// 测试统计数据
const testStats = ref<TestStatistics>({
  total_tests: 0,
  success_tests: 0,
  failed_tests: 0,
  success_rate: 0,
  avg_response_time: 0,
  last_test_time: undefined,
  today_tests: 0,
  this_week_tests: 0,

  // 新增统计信息
  total_interfaces: 0,
  total_groups: 0,
  tested_interfaces: 0,
  untested_interfaces: 0,
  fastest_response_time: 0,
  slowest_response_time: 0,
  most_tested_interface: undefined,
  recent_test_trend: 'stable'
});

// 测试表单数据
const testForm = ref({
  test_name: '',
  test_params: {},
  test_headers: {}
});

const testParamsJson = ref('{\n  "page": 1,\n  "page_size": 10\n}');
const testHeadersJson = ref('{\n  "Content-Type": "application/json"\n}');

// 测试相关状态
const testing = ref(false);
const testResult = ref<InterfaceTestResponse | null>(null);

// 调试相关状态
const debugTesting = ref(false);
const debugInterfaceId = ref<number | undefined>();
const debugInterface = ref<InterfaceConfig | null>(null);
const debugRequestUrl = ref('');
const debugRequestTab = ref('params');
const debugResponseTab = ref('response');
const debugResult = ref<InterfaceTestResponse | null>(null);
const debugFormattedResponse = ref('');

// 调试请求配置
const debugParams = ref<Array<{enabled: boolean, key: string, value: string, description: string}>>([]);
const debugHeaders = ref<Array<{enabled: boolean, key: string, value: string}>>([]);
const debugBodyType = ref('json');
const debugRequestBody = ref('');
const debugFormData = ref<Array<{enabled: boolean, key: string, value: string, type: string}>>([]);
const debugNeedsBody = computed(() => {
  return debugInterface.value && ['POST', 'PUT', 'PATCH'].includes(debugInterface.value.method);
});





// 测试详情相关变量已移至InterfaceTestDetailForm.vue

// 获取HTTP方法颜色
const getMethodColor = (method: string): string => {
  const methodConfig = HTTP_METHODS.find(m => m.value === method);
  return methodConfig?.color || '#909399';
};

// 获取状态码样式类
const getStatusClass = (status: number): string => {
  if (status >= 200 && status < 300) return 'status-success';
  if (status >= 400 && status < 500) return 'status-warning';
  if (status >= 500) return 'status-danger';
  return 'status-info';
};

// 获取数据大小
const getDataSize = (data: any): string => {
  const size = JSON.stringify(data).length;
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
  return `${(size / (1024 * 1024)).toFixed(1)}MB`;
};

// 加载测试记录列表
const loadTestRecords = async () => {
  loading.value = true;
  try {
    const response = await interfaceTestService.getTestRecords({
      page: currentPage.value,
      page_size: pageSize.value,
      interface_id: selectedInterfaceId.value,
      search: searchQuery.value || undefined
    });

    testRecords.value = response.data;
    totalCount.value = response.total;
  } catch (error) {
    console.error('加载测试记录列表失败:', error);
    ElMessage.error('加载测试记录列表失败');
  } finally {
    loading.value = false;
  }
};

// 加载接口配置列表
const loadInterfaceConfigs = async () => {
  try {
    const response = await interfaceConfigService.getInterfaceConfigs({
      page: 1,
      page_size: 1000,
      is_enabled: true // 只加载启用的接口
    });
    interfaceConfigs.value = response.items;
  } catch (error) {
    console.error('加载接口配置列表失败:', error);
    ElMessage.error('加载接口配置列表失败');
  }
};

// 加载测试统计数据
const loadTestStatistics = async () => {
  try {
    const stats = await interfaceTestService.getTestStatistics();
    testStats.value = stats;
  } catch (error) {
    console.error('加载测试统计失败:', error);
    ElMessage.error('加载测试统计失败');
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadTestRecords();
};

// 状态筛选处理
const handleStatusChange = () => {
  currentPage.value = 1;
  loadTestRecords();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadTestRecords();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadTestRecords();
};

// 解析JSON字符串
const parseJsonSafely = (jsonStr: string): any => {
  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    throw new Error('JSON格式错误，请检查语法');
  }
};

// 执行测试
const executeTest = async () => {
  if (!selectedInterfaceId.value) {
    ElMessage.warning('请先选择接口');
    return;
  }

  testing.value = true;
  testResult.value = null;

  try {
    // 解析JSON参数
    const testParams = parseJsonSafely(testParamsJson.value);
    const testHeaders = parseJsonSafely(testHeadersJson.value);

    const request: InterfaceTestRequest = {
      interface_id: selectedInterfaceId.value,
      test_name: testForm.value.test_name || undefined,
      test_params: testParams,
      test_headers: testHeaders
    };

    const result = await interfaceTestService.executeTest(request);
    testResult.value = result;

    if (result.success) {
      ElMessage.success('测试执行成功');
    } else {
      ElMessage.error(`测试执行失败: ${result.error_message}`);
    }

    // 刷新测试记录和统计数据
    loadTestRecords();
    loadTestStatistics();

  } catch (error: any) {
    console.error('执行测试失败:', error);
    ElMessage.error(extractErrorMessage(error, '执行测试失败'));
  } finally {
    testing.value = false;
  }
};

// 查看测试详情
const handleViewDetail = (row: InterfaceTestRecord) => {
  drawerMessenger.showDrawer({
    title: '测试详情',
    component: 'InterfaceTestDetailForm',
    props: {
      isEdit: true,
      editData: row
    },
    size: '35%'  // 使用第一层抽屉，去掉isSecond参数
  });
};

// 格式化参数值

// 获取参数类型

// 格式化详情JSON和复制详情响应方法已移至InterfaceTestDetailForm.vue

// 获取趋势样式类
const getTrendClass = (trend: 'up' | 'down' | 'stable'): string => {
  switch (trend) {
    case 'up': return 'trend-up';
    case 'down': return 'trend-down';
    default: return 'trend-stable';
  }
};

// 获取趋势文本
const getTrendText = (trend: 'up' | 'down' | 'stable'): string => {
  switch (trend) {
    case 'up': return '↗ 上升';
    case 'down': return '↘ 下降';
    default: return '→ 稳定';
  }
};

// 格式化最后测试时间
const formatLastTestTime = (time?: string): string => {
  if (!time) return '暂无';
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString();
};

// 处理接口调试
const handleDebugInterface = (row: InterfaceTestRecord) => {
  // 切换到调试页面
  activeTab.value = 'debug';

  // 设置调试接口
  debugInterfaceId.value = row.interface_id;
  handleDebugInterfaceChange();

  ElMessage.success(`已切换到调试模式: ${row.interface_name}`);
};





// 调试相关方法
const handleDebugInterfaceChange = () => {
  const selected = interfaceConfigs.value.find(config => config.id === debugInterfaceId.value);
  debugInterface.value = selected || null;

  if (selected) {
    debugRequestUrl.value = `http://localhost:8000${selected.path}`;
    // 初始化默认参数和请求头
    initDebugDefaults();
  }
};

const initDebugDefaults = () => {
  // 添加默认参数
  debugParams.value = [
    { enabled: true, key: 'page', value: '1', description: '页码' },
    { enabled: true, key: 'page_size', value: '10', description: '每页数量' }
  ];

  // 添加默认请求头
  debugHeaders.value = [
    { enabled: true, key: 'Content-Type', value: 'application/json' },
    { enabled: false, key: 'Authorization', value: 'Bearer your-token' }
  ];

  // 初始化请求体
  if (debugNeedsBody.value) {
    debugRequestBody.value = '{\n  "name": "test",\n  "description": "test description"\n}';
  }
};

const addDebugParam = () => {
  debugParams.value.push({ enabled: true, key: '', value: '', description: '' });
};

const removeDebugParam = (index: number) => {
  debugParams.value.splice(index, 1);
};

const addDebugHeader = () => {
  debugHeaders.value.push({ enabled: true, key: '', value: '' });
};

const removeDebugHeader = (index: number) => {
  debugHeaders.value.splice(index, 1);
};

const addDebugFormField = () => {
  debugFormData.value.push({ enabled: true, key: '', value: '', type: 'text' });
};

const removeDebugFormField = (index: number) => {
  debugFormData.value.splice(index, 1);
};

const executeDebugTest = async () => {
  if (!debugInterfaceId.value) {
    ElMessage.warning('请先选择接口');
    return;
  }

  debugTesting.value = true;
  debugResult.value = null;

  try {
    // 构建请求参数
    const enabledParams = debugParams.value.filter(p => p.enabled && p.key);
    const testParams: Record<string, any> = {};
    enabledParams.forEach(param => {
      testParams[param.key] = param.value;
    });

    // 构建请求头
    const enabledHeaders = debugHeaders.value.filter(h => h.enabled && h.key);
    const testHeaders: Record<string, string> = {};
    enabledHeaders.forEach(header => {
      testHeaders[header.key] = header.value;
    });

    const request: InterfaceTestRequest = {
      interface_id: debugInterfaceId.value,
      test_name: '调试测试',
      test_params: testParams,
      test_headers: testHeaders
    };

    const result = await interfaceTestService.executeTest(request);
    debugResult.value = result;
    debugFormattedResponse.value = JSON.stringify(result.response_data, null, 2);

    if (result.success) {
      ElMessage.success('测试执行成功');
    } else {
      ElMessage.error(`测试执行失败: ${result.error_message}`);
    }

    // 刷新测试记录和统计数据
    loadTestRecords();
    loadTestStatistics();
  } catch (error: any) {
    console.error('测试执行失败:', error);
    ElMessage.error(extractErrorMessage(error, '测试执行失败'));
  } finally {
    debugTesting.value = false;
  }
};

const formatDebugJson = () => {
  try {
    if (debugResult.value) {
      debugFormattedResponse.value = JSON.stringify(debugResult.value.response_data, null, 2);
    }
  } catch (error) {
    ElMessage.error('JSON格式化失败');
  }
};

const copyDebugResponse = async () => {
  try {
    await navigator.clipboard.writeText(debugFormattedResponse.value);
    ElMessage.success('响应内容已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

const downloadDebugResponse = () => {
  if (!debugResult.value) return;

  const blob = new Blob([debugFormattedResponse.value], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `response_${Date.now()}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  ElMessage.success('响应文件下载成功');
};

const getResponseSize = (data: any): string => {
  const size = JSON.stringify(data).length;
  if (size < 1024) return `${size}B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
  return `${(size / (1024 * 1024)).toFixed(1)}MB`;
};



const handleBatchTest = async () => {
  const enabledInterfaces = interfaceConfigs.value.filter(config => config.is_enabled);

  if (enabledInterfaces.length === 0) {
    ElMessage.warning('没有启用的接口可以测试');
    return;
  }

  batchTesting.value = true;

  try {
    ElMessage.info(`开始批量测试 ${enabledInterfaces.length} 个接口`);

    let successCount = 0;
    let failedCount = 0;

    // 逐个测试接口
    for (const interfaceConfig of enabledInterfaces) {
      try {
        const request: InterfaceTestRequest = {
          interface_id: interfaceConfig.id,
          test_name: `批量测试 - ${interfaceConfig.name}`,
          test_params: {
            page: 1,
            page_size: 5
          },
          test_headers: {
            'Content-Type': 'application/json'
          }
        };

        const result = await interfaceTestService.executeTest(request);

        if (result.success) {
          successCount++;
        } else {
          failedCount++;
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        failedCount++;
        console.error(`接口 ${interfaceConfig.name} 测试失败:`, error);
      }
    }

    ElMessage.success(`批量测试完成 - 成功: ${successCount}, 失败: ${failedCount}`);

    // 刷新测试记录和统计数据
    loadTestRecords();
    loadTestStatistics();
  } catch (error: any) {
    console.error('批量测试失败:', error);
    ElMessage.error(error.message || '批量测试失败');
  } finally {
    batchTesting.value = false;
  }
};

const handleRetestInterface = async (row: InterfaceTestRecord) => {
  try {
    ElMessage.info(`开始重新测试接口: ${row.interface_name}`);

    // 使用原来的测试参数重新测试
    const request: InterfaceTestRequest = {
      interface_id: row.interface_id,
      test_name: row.test_name || `重新测试 - ${row.interface_name}`,
      test_params: row.test_params,
      test_headers: row.test_headers
    };

    const result = await interfaceTestService.executeTest(request);

    if (result.success) {
      ElMessage.success(`接口重新测试成功 - 状态码: ${result.status_code}, 响应时间: ${result.response_time}ms`);
    } else {
      ElMessage.error(`接口重新测试失败: ${result.error_message}`);
    }

    // 刷新测试记录和统计数据
    loadTestRecords();
    loadTestStatistics();
  } catch (error: any) {
    console.error('重新测试失败:', error);
    ElMessage.error(error.message || '重新测试失败');
  }
};



// 页面加载时获取数据
onMounted(async () => {
  await loadInterfaceConfigs();
  loadTestRecords();
  loadTestStatistics();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 引入公共样式，但保留现有样式作为备份和覆盖 */

/* 测试统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 180px;
}

.stat-content {
  text-align: center;
  padding: 16px 12px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 6px;
}

.stat-number.success {
  color: #67C23A;
}

.stat-number.danger {
  color: #F56C6C;
}

.stat-number.warning {
  color: #E6A23C;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-interface-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-extra {
  display: flex;
  justify-content: space-around;
  gap: 8px;
  margin-top: 8px;
}

.extra-item {
  font-size: 12px;
  color: #909399;
  padding: 2px 6px;
  background: #f5f7fa;
  border-radius: 3px;
  white-space: nowrap;
}

.extra-item.success {
  color: #67C23A;
  background: #f0f9ff;
}

.extra-item.danger {
  color: #F56C6C;
  background: #fef0f0;
}

.extra-item.warning {
  color: #E6A23C;
  background: #fdf6ec;
}

/* 趋势样式 */
.trend-up {
  color: #67C23A !important;
  background: #f0f9ff !important;
}

.trend-down {
  color: #F56C6C !important;
  background: #fef0f0 !important;
}

.trend-stable {
  color: #909399 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }

  .stat-card {
    min-width: auto;
  }

  .stat-extra {
    flex-direction: column;
    gap: 4px;
  }
}

/* 测试表单卡片样式 */
.test-form-card,
.test-result-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
}

/* JSON编辑器样式 */
.json-editor textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
}

/* 测试结果样式 */
.test-result-content {
  padding: 0;
}

.result-item {
  text-align: center;
  padding: 16px;
  border-right: 1px solid #EBEEF5;
}

.result-item:last-child {
  border-right: none;
}

.result-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.result-value {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.response-section {
  margin-top: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
}

.json-viewer {
  background: #f5f7fa;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.json-viewer pre {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-message {
  color: #F56C6C;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
}

/* 接口信息样式 */
.interface-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.interface-name {
  font-weight: 500;
  color: #303133;
}

.interface-path {
  display: flex;
  align-items: center;
}

.path-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 测试名称样式 */
.test-name {
  color: #606266;
  font-size: 13px;
}

/* 状态码样式 */
.status-code {
  font-weight: 500;
  font-family: monospace;
}

.status-success {
  color: #67C23A;
}

.status-warning {
  color: #E6A23C;
}

.status-danger {
  color: #F56C6C;
}

/* 表格宽度和布局修复 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-table__header) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__body) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-scrollbar__view) {
  display: block !important;
  width: 100% !important;
}

:deep(.el-table) {
  width: 100% !important;
  table-layout: auto !important;
} */

/* 表格滚动条样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-table__header-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__header-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  height: 6px;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__header-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
} */

/* :deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__body-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__body-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
} */

.status-info {
  color: #909399;
}

/* 响应时间样式 */
.response-time {
  font-family: monospace;
  color: #606266;
}

/* 时间文本样式 */
.time-text {
  color: #909399;
  font-size: 13px;
}

/* 测试人样式 */
.tester-text {
  color: #606266;
  font-size: 13px;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  gap: 6px;
}

/* 操作按钮样式 - 2024-12-26: 按钮样式已在page-common.scss中定义，注释重复定义 */
/* .action-buttons .el-button {
  padding: 5px 10px;
  font-size: 12px;
} */

/* 测试详情样式 */
.test-detail {
  padding: 0;
}

.detail-section {
  margin-top: 20px;
}

/* 测试详情样式已移至InterfaceTestDetailForm.vue */



/* 接口基本信息区域 */
.api-info-section {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

.api-header {
  margin-bottom: 15px;
}

.api-method-path {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.method-tag {
  color: white !important;
  border: none !important;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

.api-path {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.api-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 测试状态栏 */
.test-status-bar {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

.status-code {
  font-family: monospace;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
}

.status-code.status-success {
  color: #67c23a;
  background: #f0f9ff;
}

.status-code.status-danger {
  color: #f56c6c;
  background: #fef0f0;
}

.status-code.status-warning {
  color: #e6a23c;
  background: #fdf6ec;
}

.status-code.status-info {
  color: #909399;
  background: #f4f4f5;
}

.response-time {
  font-family: monospace;
  color: #67c23a;
  font-weight: 500;
}

.test-time {
  color: #606266;
  font-size: 13px;
}

/* 详情内容区域 */
.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.detail-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow-y: hidden; /* 隐藏滚动条，避免双滚动条 */
}

/* 请求信息区域 */
.request-section {
  padding: 20px;
  height: 100%;
  overflow: hidden; /* 移除外层滚动条 */
  display: flex;
  flex-direction: column;
}

/* 参数区域 */
.param-section {
  margin-bottom: 24px;
}

.param-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3FC8DD;
}

.param-table {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.param-header {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.param-name-col,
.param-value-col,
.param-type-col {
  padding: 12px;
  font-weight: 600;
  color: #303133;
  font-size: 13px;
  border-right: 1px solid #ebeef5;
}

.param-type-col {
  border-right: none;
}

.param-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  border-bottom: 1px solid #f0f2f5;
}

.param-row:last-child {
  border-bottom: none;
}

.param-name,
.param-value,
.param-type {
  padding: 12px;
  font-size: 13px;
  border-right: 1px solid #f0f2f5;
  word-break: break-all;
}

.param-name {
  font-weight: 500;
  color: #303133;
  background: #fafbfc;
}

.param-value {
  color: #606266;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.param-type {
  color: #909399;
  font-size: 12px;
  border-right: none;
  text-align: center;
}

/* 请求头区域 */
.header-section {
  margin-bottom: 24px;
}

.header-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3FC8DD;
}

.header-table {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.header-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  border-bottom: 1px solid #f0f2f5;
}

.header-row:last-child {
  border-bottom: none;
}

.header-name,
.header-value {
  padding: 12px;
  font-size: 13px;
  border-right: 1px solid #f0f2f5;
  word-break: break-all;
}

.header-name {
  font-weight: 500;
  color: #303133;
  background: #fafbfc;
}

.header-value {
  color: #606266;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  border-right: none;
}

/* 响应信息区域 */
.response-section {
  padding: 20px;
  height: 100%;
  overflow: hidden; /* 移除外层滚动条 */
  display: flex;
  flex-direction: column;
}

/* 错误信息区域 */
.error-section {
  margin-bottom: 24px;
}

.error-title {
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f56c6c;
}

.error-content {
  margin-bottom: 16px;
}

/* 响应体区域 */
.response-body-section {
  margin-bottom: 24px;
}

.response-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3FC8DD;
}

.response-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.response-content .response-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
}

.response-content .response-textarea :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fafbfc;
  height: calc(100vh - 430px) !important; /* 调试页面：增加减去的值，让textarea高度变小，底部框线才能显示 */
  min-height: 200px !important;
  resize: none;
  overflow-y: auto;
  /* 确保底部边框可见 */
  box-sizing: border-box;
}

/* 详细页面的textarea样式 */
.detail-tabs .response-content .response-textarea :deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fafbfc;
  height: calc(100vh - 390px) !important; /* 详细页面：调整高度计算 */
  min-height: 200px !important;
  resize: none;
  overflow-y: auto;
  /* 确保底部边框可见 */
  box-sizing: border-box;
}

/* 响应头区域 */
.response-headers-section {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.headers-table {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.headers-header {
  display: grid;
  grid-template-columns: 1fr 2fr;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.header-name-col,
.header-value-col {
  padding: 12px;
  font-weight: 600;
  color: #303133;
  font-size: 13px;
  border-right: 1px solid #ebeef5;
}

.header-value-col {
  border-right: none;
}

/* 统一滚动条样式 */
.params-table::-webkit-scrollbar,
.headers-table::-webkit-scrollbar,
.response-textarea :deep(.el-textarea__inner)::-webkit-scrollbar {
  width: 4px;
}

.params-table::-webkit-scrollbar-track,
.headers-table::-webkit-scrollbar-track,
.response-textarea :deep(.el-textarea__inner)::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.params-table::-webkit-scrollbar-thumb,
.headers-table::-webkit-scrollbar-thumb,
.response-textarea :deep(.el-textarea__inner)::-webkit-scrollbar-thumb {
  background: #9db7bd;
  border-radius: 2px;
  transition: background 0.3s ease;
}

.params-table::-webkit-scrollbar-thumb:hover,
.headers-table::-webkit-scrollbar-thumb:hover,
.response-textarea :deep(.el-textarea__inner)::-webkit-scrollbar-thumb:hover {
  background: #7a9ca3;
}

/* 应用公共滚动条样式 - 2024-12-27: 使用统一滚动条规范 */
.params-table,
.headers-table,
.response-textarea :deep(.el-textarea__inner) {
  /* 继承公共样式中的滚动条规范 */
  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
}

/* 移除响应面板的竖向滚动条 */
.response-panel {
  overflow-y: hidden !important;
  overflow-x: hidden !important;
}

.response-content {
  overflow-y: hidden !important;
  overflow-x: hidden !important;
}

/* 分页容器样式 - 2024-12-26: 已改用PaginationComponent，无需自定义样式 */

/* 对话框底部样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
} */

/* 页面标题响应式样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .page-title {
  flex-wrap: wrap;
  min-width: 0;
}

.title-text {
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

@media (max-width: 1200px) {
  .page-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 900px) {
  .title-text {
    white-space: normal;
    word-break: keep-all;
    overflow-wrap: break-word;
    line-height: 1.3;
  }
}

@media (max-width: 600px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
} */

/* 调试页面样式 */
.debug-container {
  display: flex;
  height: calc(100vh - 200px);
  gap: 15px;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
}

.request-panel {
  width: 50%;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.response-panel {
  width: 50%;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  height: 70px; /* 减少高度 */
  box-sizing: border-box;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

/* 调试接口信息样式 */
.debug-interface-info {
  flex: 1;
  margin-right: 20px;
}

.interface-main-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.interface-name-text {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.interface-group {
  font-size: 11px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: normal;
  white-space: nowrap;
}

.interface-separator {
  color: #dcdfe6;
  font-weight: bold;
  margin: 0 4px;
}

.method-tag-debug {
  color: white !important;
  border: none !important;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
  white-space: nowrap;
}

.path-text-debug {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #e4e7ed;
  white-space: nowrap;
}



.send-request-btn {
  padding: 8px 16px;
  font-size: 14px;
  height: auto;
  white-space: nowrap;
}

.response-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.response-time {
  color: #67c23a;
  font-weight: 500;
}

.response-size {
  color: #909399;
  font-size: 12px;
}



.url-section {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.url-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.method-tag {
  min-width: 60px;
  text-align: center;
  color: white !important;
  border: none !important;
}

.url-input {
  flex: 1;
}

.request-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 15px; /* 左侧留白 */
}

.request-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
}

.params-section,
.headers-section,
.body-section {
  padding: 15px 20px 15px 0; /* 调整padding，左侧不需要额外padding */
  height: 100%;
  overflow-y: auto;
}

.params-header,
.headers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* 添加参数按钮样式 */
.params-header .el-button,
.headers-header .el-button {
  background: #3FC8DD;
  border-color: #3FC8DD;
  color: white;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.params-header .el-button:hover,
.headers-header .el-button:hover {
  background: #2ba8c4;
  border-color: #2ba8c4;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(63, 200, 221, 0.3);
}

.params-table,
.headers-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px; /* 设置最大高度 */
  overflow-y: auto; /* 添加垂直滚动条 */
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 8px;
  background: #fafbfc;
}

.param-row {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 1fr 80px;
  gap: 8px;
  align-items: center;
  min-height: 32px; /* 确保行高一致 */
}

/* 请求头表格样式 */
.headers-row {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 80px; /* 请求头只有4列 */
  gap: 8px;
  align-items: center;
  min-height: 32px; /* 确保行高一致 */
}

.headers-header-row {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

/* 确保列头和内容对齐 */
.param-row,
.headers-row {
  padding: 4px 0;
}

/* 列头样式 */
.param-row.header-row > div,
.headers-row.headers-header-row > div {
  display: flex;
  align-items: center;
  justify-content: center; /* 列头标题居中 */
  height: 32px;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

/* Element Plus 组件对齐 */
.param-row .el-checkbox,
.headers-row .el-checkbox {
  height: 32px;
  margin: 0;
}

.param-row .el-input,
.headers-row .el-input {
  height: 32px;
}

.param-row .el-input :deep(.el-input__wrapper),
.headers-row .el-input :deep(.el-input__wrapper) {
  height: 32px;
  padding: 0 8px;
}

.param-row .el-button,
.headers-row .el-button {
  height: 32px;
  margin: 0;
}

/* 启用列居中对齐 */
.param-enabled,
.header-enabled {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 操作列居中对齐 */
.param-actions,
.header-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 强制所有列内容垂直对齐 */
.param-row > *,
.headers-row > * {
  box-sizing: border-box;
  height: 32px;
  display: flex;
  align-items: center;
}

/* 确保输入框内部对齐 */
.param-row .el-input :deep(.el-input__inner),
.headers-row .el-input :deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
}

/* 确保复选框对齐 */
.param-row .el-checkbox :deep(.el-checkbox__input),
.headers-row .el-checkbox :deep(.el-checkbox__input) {
  line-height: 32px;
}

.body-type-selector {
  margin-bottom: 15px;
}

.body-content {
  flex: 1;
}

.body-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.form-data-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.form-row {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 100px 80px;
  gap: 8px;
  align-items: center;
}

.form-enabled {
  display: flex;
  justify-content: center;
}

.response-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.response-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 15px; /* 左侧留白 */
}

.response-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
}

.response-body {
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.response-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
}

.response-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  flex: 1;
}

.response-headers {
  padding: 15px 20px;
  height: 100%;
  overflow-y: auto;
}

.response-header-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.header-name {
  font-weight: 500;
  color: #303133;
  min-width: 150px;
}

.header-value {
  color: #606266;
  word-break: break-all;
}

.test-info {
  padding: 15px 20px;
}

.info-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label {
  font-weight: 500;
  color: #303133;
  min-width: 100px;
}

.info-value {
  color: #606266;
}

.info-value.error {
  color: #f56c6c;
}

.empty-response {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

/* 调试页面空状态样式 */
.debug-empty-state {
  padding: 60px 20px;
  text-align: center;
}




</style>
