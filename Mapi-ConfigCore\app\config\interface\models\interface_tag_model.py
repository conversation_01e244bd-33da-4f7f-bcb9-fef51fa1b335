"""
接口标签数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, func
from app.shared.database import Base


class InterfaceTagModel(Base):
    """接口标签表模型"""
    
    __tablename__ = "interface_tags"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    
    # 基本信息
    name = Column(String(50), nullable=False, unique=True, comment="标签名称")
    color = Column(String(7), nullable=False, comment="标签颜色(十六进制)")
    description = Column(String(200), nullable=True, comment="标签描述")
    
    # 状态信息
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    
    def __repr__(self):
        return f"<InterfaceTag(id={self.id}, name='{self.name}', color='{self.color}')>"
    
    def to_dict(self):
        """转换为字典格式（驼峰命名，与前端保持一致）"""
        return {
            'id': self.id,
            'name': self.name,
            'color': self.color,
            'description': self.description,
            'isEnabled': self.is_enabled,  # 转换为驼峰格式
            'createdAt': self.created_at.isoformat() if self.created_at else None,  # 转换为驼峰格式
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,  # 转换为驼峰格式
            'createdBy': self.created_by  # 转换为驼峰格式
        }
