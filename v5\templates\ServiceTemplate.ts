// Service层标准模板
// 使用说明：
// 1. 复制此模板创建新service
// 2. 替换所有 {ModuleName} 为实际模块名
// 3. 替换所有 {moduleName} 为实际模块名（小写）
// 4. 替换所有 {module-name} 为实际模块名（短横线）
// 5. 根据实际需求调整接口方法

import type {
  {ModuleName},
  {ModuleName}FormData,
  {ModuleName}Query,
  {ModuleName}ListResponse
} from '../types/{module-name}';
import { mock{ModuleName}s } from '../mock/{module-name}.mock';
import { apiClient } from '@/utils/http-client';

/**
 * {ModuleName}服务接口
 */
interface I{ModuleName}Service {
  get{ModuleName}List(params: {ModuleName}Query): Promise<{ModuleName}ListResponse>;
  get{ModuleName}ById(id: number): Promise<{ModuleName} | undefined>;
  create{ModuleName}(data: {ModuleName}FormData): Promise<{ModuleName}>;
  update{ModuleName}(id: number, data: {ModuleName}FormData): Promise<{ModuleName}>;
  delete{ModuleName}(id: number): Promise<boolean>;
  toggle{ModuleName}Status(id: number, status: 'enabled' | 'disabled'): Promise<{ModuleName}>;
}

/**
 * Mock{ModuleName}服务实现
 */
class Mock{ModuleName}Service implements I{ModuleName}Service {
  private {moduleName}s: {ModuleName}[] = [...mock{ModuleName}s];
  
  async get{ModuleName}List(params: {ModuleName}Query): Promise<{ModuleName}ListResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredData = [...this.{moduleName}s];
    
    // 搜索过滤
    if (params.name) {
      const keyword = params.name.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    }
    
    // 状态过滤
    if (params.status) {
      filteredData = filteredData.filter(item => item.status === params.status);
    }
    
    // 分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 20;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);
    
    return {
      data: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    };
  }
  
  async get{ModuleName}ById(id: number): Promise<{ModuleName} | undefined> {
    const item = this.{moduleName}s.find(item => item.id === id);
    return Promise.resolve(item ? { ...item } : undefined);
  }
  
  async create{ModuleName}(data: {ModuleName}FormData): Promise<{ModuleName}> {
    const newId = Math.max(...this.{moduleName}s.map(c => c.id), 0) + 1;
    const now = new Date().toLocaleString('zh-CN');
    
    const newItem: {ModuleName} = {
      id: newId,
      name: data.name,
      description: data.description || '',
      status: data.status,
      createdAt: now,
      updatedAt: now
    };
    
    this.{moduleName}s.unshift(newItem);
    return Promise.resolve({ ...newItem });
  }
  
  async update{ModuleName}(id: number, data: {ModuleName}FormData): Promise<{ModuleName}> {
    const index = this.{moduleName}s.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`{ModuleName}ID ${id} 不存在`));
    }
    
    const now = new Date().toLocaleString('zh-CN');
    const updatedItem: {ModuleName} = {
      ...this.{moduleName}s[index],
      name: data.name,
      description: data.description || '',
      status: data.status,
      updatedAt: now
    };
    
    this.{moduleName}s[index] = updatedItem;
    return Promise.resolve({ ...updatedItem });
  }
  
  async delete{ModuleName}(id: number): Promise<boolean> {
    const index = this.{moduleName}s.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }
    
    this.{moduleName}s.splice(index, 1);
    return Promise.resolve(true);
  }
  
  async toggle{ModuleName}Status(id: number, status: 'enabled' | 'disabled'): Promise<{ModuleName}> {
    const index = this.{moduleName}s.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`{ModuleName}ID ${id} 不存在`));
    }
    
    this.{moduleName}s[index].status = status;
    this.{moduleName}s[index].updatedAt = new Date().toLocaleString('zh-CN');
    
    return Promise.resolve({ ...this.{moduleName}s[index] });
  }
}

/**
 * API{ModuleName}服务实现
 */
class Api{ModuleName}Service implements I{ModuleName}Service {
  async get{ModuleName}List(params: {ModuleName}Query): Promise<{ModuleName}ListResponse> {
    return await apiClient.get<{ModuleName}ListResponse>('/{module-name}s', params);
  }

  async get{ModuleName}ById(id: number): Promise<{ModuleName} | undefined> {
    try {
      return await apiClient.get<{ModuleName}>(`/{module-name}s/${id}`);
    } catch (error) {
      return undefined;
    }
  }

  async create{ModuleName}(data: {ModuleName}FormData): Promise<{ModuleName}> {
    return await apiClient.post<{ModuleName}>('/{module-name}s', data);
  }

  async update{ModuleName}(id: number, data: {ModuleName}FormData): Promise<{ModuleName}> {
    return await apiClient.put<{ModuleName}>(`/{module-name}s/${id}`, data);
  }

  async delete{ModuleName}(id: number): Promise<boolean> {
    await apiClient.delete(`/{module-name}s/${id}`);
    return true;
  }
  
  async delete{ModuleName}(id: number): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    });
    return response.ok;
  }
  
  async toggle{ModuleName}Status(id: number, status: 'enabled' | 'disabled'): Promise<{ModuleName}> {
    const response = await fetch(`${this.baseUrl}/${id}/status`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status })
    });
    if (!response.ok) throw new Error(`切换{ModuleName}状态失败`);
    return response.json();
  }
}

// 根据环境变量选择服务实现
const useMock = import.meta.env.VITE_USE_MOCK === 'true';
const {moduleName}Service: I{ModuleName}Service = useMock 
  ? new Mock{ModuleName}Service() 
  : new Api{ModuleName}Service();

export default {moduleName}Service;
