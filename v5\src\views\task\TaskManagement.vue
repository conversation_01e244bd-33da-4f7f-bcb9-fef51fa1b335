<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Operation /></el-icon>
      任务管理
    </h2>

    <div class="content-container">
      <el-tabs v-model="activeTab" class="task-tabs">
        <!-- 定时任务 -->
        <el-tab-pane label="定时任务" name="scheduled">
          <div class="section-title">
            <el-icon><Timer /></el-icon>
            定时任务管理
          </div>

          <div class="task-summary">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="summary-card">
                  <div class="summary-number">{{ taskStats.total }}</div>
                  <div class="summary-label">总任务数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-card">
                  <div class="summary-number active">{{ taskStats.active }}</div>
                  <div class="summary-label">运行中</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-card">
                  <div class="summary-number paused">{{ taskStats.paused }}</div>
                  <div class="summary-label">已暂停</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-card">
                  <div class="summary-number failed">{{ taskStats.failed }}</div>
                  <div class="summary-label">执行失败</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="task-actions">
            <el-button type="primary" @click="createTask">创建任务</el-button>
            <el-button @click="batchStart">批量启动</el-button>
            <el-button @click="batchStop">批量停止</el-button>
            <el-button type="danger" @click="batchDelete">批量删除</el-button>
          </div>

          <div class="task-list">
            <el-table :data="scheduledTasks" style="width: 100%" @selection-change="handleSelectionChange">
              <!-- 空状态 -->
              <template #empty>
                <el-empty
                  description="暂无定时任务"
                  :image-size="120"
                >
                  <template #description>
                    <p>还没有创建任何定时任务</p>
                    <p>点击上方"创建任务"按钮开始创建</p>
                  </template>
                  <el-button type="primary" @click="createTask">
                    <el-icon><Plus /></el-icon>
                    创建任务
                  </el-button>
                </el-empty>
              </template>
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="name" label="任务名称" width="200"></el-table-column>
              <el-table-column prop="type" label="任务类型" width="120">
                <template #default="scope">
                  <el-tag size="small">{{ scope.row.type }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="cron" label="执行周期" width="150"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastExecution" label="最后执行" width="180"></el-table-column>
              <el-table-column prop="nextExecution" label="下次执行" width="180"></el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" @click="executeTask(scope.row)">立即执行</el-button>
                  <el-button type="primary" size="small" @click="editTask(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteTask(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 数据刷新 -->
        <el-tab-pane label="数据刷新" name="refresh">
          <div class="section-title">
            <el-icon><Refresh /></el-icon>
            ORM数据刷新
          </div>

          <div class="refresh-config">
            <el-form :model="refreshConfig" label-width="150px">
              <el-form-item label="自动刷新">
                <el-switch v-model="refreshConfig.autoRefresh"></el-switch>
              </el-form-item>

              <el-form-item label="刷新间隔">
                <el-select v-model="refreshConfig.interval">
                  <el-option label="5分钟" value="5"></el-option>
                  <el-option label="10分钟" value="10"></el-option>
                  <el-option label="30分钟" value="30"></el-option>
                  <el-option label="1小时" value="60"></el-option>
                  <el-option label="6小时" value="360"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="刷新范围">
                <el-checkbox-group v-model="refreshConfig.scope">
                  <el-checkbox label="users">用户表</el-checkbox>
                  <el-checkbox label="orders">订单表</el-checkbox>
                  <el-checkbox label="products">商品表</el-checkbox>
                  <el-checkbox label="logs">日志表</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveRefreshConfig">保存配置</el-button>
                <el-button @click="manualRefresh">立即刷新</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="refresh-history">
            <h4>刷新历史</h4>
            <el-table :data="refreshHistory" style="width: 100%">
              <el-table-column prop="time" label="刷新时间" width="180"></el-table-column>
              <el-table-column prop="scope" label="刷新范围" width="200"></el-table-column>
              <el-table-column prop="duration" label="耗时" width="100"></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="records" label="影响记录数" width="120"></el-table-column>
              <el-table-column prop="message" label="备注" min-width="200"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 日志清理 -->
        <el-tab-pane label="日志清理" name="cleanup">
          <div class="section-title">
            <el-icon><Delete /></el-icon>
            日志清理管理
          </div>

          <div class="cleanup-config">
            <el-form :model="cleanupConfig" label-width="150px">
              <el-form-item label="自动清理">
                <el-switch v-model="cleanupConfig.autoCleanup"></el-switch>
              </el-form-item>

              <el-form-item label="保留天数">
                <el-input-number v-model="cleanupConfig.retentionDays" :min="1" :max="365"></el-input-number>
                <span style="margin-left: 10px;">天</span>
              </el-form-item>

              <el-form-item label="清理时间">
                <el-time-picker v-model="cleanupConfig.cleanupTime" placeholder="选择清理时间"></el-time-picker>
              </el-form-item>

              <el-form-item label="清理类型">
                <el-checkbox-group v-model="cleanupConfig.types">
                  <el-checkbox label="access">访问日志</el-checkbox>
                  <el-checkbox label="error">错误日志</el-checkbox>
                  <el-checkbox label="system">系统日志</el-checkbox>
                  <el-checkbox label="audit">审计日志</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveCleanupConfig">保存配置</el-button>
                <el-button type="warning" @click="previewCleanup">预览清理</el-button>
                <el-button type="danger" @click="manualCleanup">立即清理</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="cleanup-stats">
            <h4>存储统计</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-value">{{ storageStats.total }}</div>
                  <div class="stat-label">总日志大小</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-value">{{ storageStats.access }}</div>
                  <div class="stat-label">访问日志</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-value">{{ storageStats.error }}</div>
                  <div class="stat-label">错误日志</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-card">
                  <div class="stat-value">{{ storageStats.system }}</div>
                  <div class="stat-label">系统日志</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Operation, Timer, Refresh, Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('scheduled')

// 选中的任务
const selectedTasks = ref([])

// 任务统计
const taskStats = ref({
  total: 8,
  active: 5,
  paused: 2,
  failed: 1
})

// 定时任务列表
const scheduledTasks = ref([
  { id: 1, name: '数据同步', type: '同步', cron: '0 0 2 * * ?', status: '运行中', lastExecution: '2023-12-15 02:00:00', nextExecution: '2023-12-16 02:00:00' },
  { id: 2, name: '日志清理', type: '清理', cron: '0 0 3 * * ?', status: '运行中', lastExecution: '2023-12-15 03:00:00', nextExecution: '2023-12-16 03:00:00' },
  { id: 3, name: '备份任务', type: '备份', cron: '0 0 1 * * ?', status: '已暂停', lastExecution: '2023-12-14 01:00:00', nextExecution: '-' }
])

// 刷新配置
const refreshConfig = ref({
  autoRefresh: true,
  interval: '30',
  scope: ['users', 'orders']
})

// 刷新历史
const refreshHistory = ref([
  { time: '2023-12-15 14:30:00', scope: 'users, orders', duration: '2.3s', status: 'success', records: 1250, message: '数据刷新成功' },
  { time: '2023-12-15 14:00:00', scope: 'products', duration: '1.8s', status: 'success', records: 856, message: '商品数据更新完成' },
  { time: '2023-12-15 13:30:00', scope: 'logs', duration: '0.5s', status: 'failed', records: 0, message: '连接超时' }
])

// 清理配置
const cleanupConfig = ref({
  autoCleanup: true,
  retentionDays: 30,
  cleanupTime: null,
  types: ['access', 'error']
})

// 存储统计
const storageStats = ref({
  total: '15.6GB',
  access: '8.2GB',
  error: '3.1GB',
  system: '4.3GB'
})

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '运行中': 'success',
    '已暂停': 'warning',
    '执行失败': 'danger'
  }
  return types[status] || 'info'
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 创建任务
const createTask = () => {
  ElMessage.info('创建任务功能开发中...')
}

// 批量启动
const batchStart = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要启动的任务')
    return
  }
  ElMessage.success(`已启动 ${selectedTasks.value.length} 个任务`)
}

// 批量停止
const batchStop = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要停止的任务')
    return
  }
  ElMessage.success(`已停止 ${selectedTasks.value.length} 个任务`)
}

// 批量删除
const batchDelete = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要删除的任务')
    return
  }
  ElMessageBox.confirm(`确定要删除选中的 ${selectedTasks.value.length} 个任务吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`已删除 ${selectedTasks.value.length} 个任务`)
  })
}

// 执行任务
const executeTask = (task: any) => {
  ElMessage.success(`任务 "${task.name}" 开始执行`)
}

// 编辑任务
const editTask = (task: any) => {
  ElMessage.info('编辑任务功能开发中...')
}

// 删除任务
const deleteTask = (task: any) => {
  ElMessageBox.confirm(`确定要删除任务 "${task.name}" 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('任务删除成功')
  })
}

// 保存刷新配置
const saveRefreshConfig = () => {
  ElMessage.success('刷新配置保存成功')
}

// 手动刷新
const manualRefresh = () => {
  ElMessage.success('数据刷新已开始')
}

// 保存清理配置
const saveCleanupConfig = () => {
  ElMessage.success('清理配置保存成功')
}

// 预览清理
const previewCleanup = () => {
  ElMessage.info('预计清理 2.3GB 日志文件')
}

// 手动清理
const manualCleanup = () => {
  ElMessageBox.confirm('确定要立即清理日志吗？此操作不可恢复！', '确认清理', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('日志清理已开始')
  })
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary);
  padding-left: 12px;
}

.task-summary {
  margin-bottom: 25px;
}

.summary-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: white;
}

.summary-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.summary-number.active { color: #67c23a; }
.summary-number.paused { color: #e6a23c; }
.summary-number.failed { color: #f56c6c; }

.summary-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.task-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.task-list, .refresh-config, .refresh-history, .cleanup-config, .cleanup-stats {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.stat-card {
  text-align: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: white;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.task-tabs {
  margin-bottom: 20px;
}

/* 修复表格宽度计算问题 */
:deep(.el-table__header) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__body) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

/* 修复滚动条视图导致的列对齐问题 */
:deep(.el-scrollbar__view) {
  display: block !important;
  width: 100% !important;
}

/* 确保表格容器正确显示 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: auto !important;
}

/* 自定义滚动条样式 */
:deep(.el-table__header-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__header-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  height: 6px;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__header-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
}

:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__body-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__body-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
}
</style>