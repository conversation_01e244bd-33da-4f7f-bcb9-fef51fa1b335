// 类型定义标准模板
// 使用说明：
// 1. 复制此模板创建新类型文件
// 2. 替换所有 {ModuleName} 为实际模块名
// 3. 根据实际需求调整字段和类型

/**
 * {ModuleName}基础接口
 */
export interface {ModuleName} {
  id: number;
  name: string;
  description?: string;
  status: 'enabled' | 'disabled';
  createdAt: string;
  updatedAt: string;
}

/**
 * {ModuleName}表单数据接口
 */
export interface {ModuleName}FormData {
  name: string;
  description?: string;
  status: 'enabled' | 'disabled';
}

/**
 * {ModuleName}查询参数接口
 */
export interface {ModuleName}Query {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: 'enabled' | 'disabled' | '';
}

/**
 * {ModuleName}列表响应接口
 */
export interface {ModuleName}ListResponse {
  data: {ModuleName}[];
  total: number;
  page: number;
  pageSize: number;
}
