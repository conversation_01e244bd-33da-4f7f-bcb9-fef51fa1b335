/**
 * 接口标签服务
 *
 * 注意：Mock 服务已禁用，强制使用 API 服务
 * 所有数据通过 http-client 自动处理字段转换和时间格式化
 */

import type {
  InterfaceTag,
  InterfaceTagRequest,
  InterfaceTagListResponse,
  InterfaceTagQuery
} from '@/types/interface-tag';
import { apiClient } from '@/utils/http-client';
// Mock 相关导入已禁用，强制使用 API 服务
// import {
//   mockGetInterfaceTags,
//   mockCreateInterfaceTag,
//   mockUpdateInterfaceTag,
//   mockDeleteInterfaceTag
// } from '@/mock/interface-tag.mock';

/**
 * 接口标签服务接口
 */
interface IInterfaceTagService {
  getInterfaceTags(query?: InterfaceTagQuery): Promise<InterfaceTagListResponse>;
  getInterfaceTagById(id: number): Promise<InterfaceTag | undefined>;
  createInterfaceTag(data: InterfaceTagRequest): Promise<InterfaceTag>;
  updateInterfaceTag(id: number, data: InterfaceTagRequest): Promise<InterfaceTag>;
  deleteInterfaceTag(id: number): Promise<boolean>;
  checkTagNameExists(name: string, excludeId?: number): Promise<boolean>;
}

/**
 * Mock接口标签服务实现（已禁用，强制使用API服务）
 */
// class MockInterfaceTagService implements IInterfaceTagService {
//   async getInterfaceTags(query: InterfaceTagQuery = {}): Promise<InterfaceTagListResponse> {
//     const { page = 1, page_size = 10, search } = query;
//     return mockGetInterfaceTags(page, page_size, search);
//   }
//
//   async getInterfaceTagById(id: number): Promise<InterfaceTag | undefined> {
//     const response = await mockGetInterfaceTags(1, 1000); // 获取所有数据
//     return response.items.find(tag => tag.id === id);
//   }
//
//   async createInterfaceTag(data: InterfaceTagRequest): Promise<InterfaceTag> {
//     return mockCreateInterfaceTag(data);
//   }
//
//   async updateInterfaceTag(id: number, data: InterfaceTagRequest): Promise<InterfaceTag> {
//     return mockUpdateInterfaceTag(id, data);
//   }
//
//   async deleteInterfaceTag(id: number): Promise<boolean> {
//     return mockDeleteInterfaceTag(id);
//   }
//
//   async checkTagNameExists(name: string, excludeId?: number): Promise<boolean> {
//     const response = await mockGetInterfaceTags(1, 1000);
//     return response.data.some(tag =>
//       tag.name === name && (!excludeId || tag.id !== excludeId)
//     );
//   }
// }

/**
 * API接口标签服务实现
 */
class ApiInterfaceTagService implements IInterfaceTagService {
  async getInterfaceTags(query: InterfaceTagQuery = {}): Promise<InterfaceTagListResponse> {
    const { page = 1, page_size = 10, search } = query;

    const params: Record<string, any> = {
      page,
      size: page_size
    };

    if (search) {
      params.search = search;
    }

    return await apiClient.get<InterfaceTagListResponse>('/interface/tags', { params });
  }
  
  async getInterfaceTagById(id: number): Promise<InterfaceTag | undefined> {
    try {
      return await apiClient.get<InterfaceTag>(`/interface/tags/${id}`);
    } catch (error) {
      return undefined;
    }
  }

  async createInterfaceTag(data: InterfaceTagRequest): Promise<InterfaceTag> {
    return await apiClient.post<InterfaceTag>('/interface/tags', data);
  }

  async updateInterfaceTag(id: number, data: InterfaceTagRequest): Promise<InterfaceTag> {
    return await apiClient.put<InterfaceTag>(`/interface/tags/${id}`, data);
  }

  async deleteInterfaceTag(id: number): Promise<boolean> {
    await apiClient.delete(`/interface/tags/${id}`);
    return true;
  }
  
  async checkTagNameExists(name: string, excludeId?: number): Promise<boolean> {
    const params: Record<string, any> = {
      page: 1,
      size: 1000,
      search: name
    };

    const response = await this.getInterfaceTags(params);
    return response.items.some(tag =>
      tag.name === name && (!excludeId || tag.id !== excludeId)
    );
  }
}

// 接口标签服务强制使用API模式，Mock已禁用
const interfaceTagService: IInterfaceTagService = new ApiInterfaceTagService();

export default interfaceTagService;
