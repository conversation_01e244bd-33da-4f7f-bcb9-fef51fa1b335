[tool.poetry]
name = "mapi-configcore"
version = "1.0.0"
description = "Mapi配置管理核心服务 - FastAPI后端项目"
authors = ["Mapi Team <<EMAIL>>"]
license = "MIT"
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.8.1"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
sqlalchemy = "^1.4.0"
alembic = "^1.13.0"
python-multipart = "^0.0.6"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-dotenv = "^1.0.0"
httpx = "^0.25.2"
celery = "^5.3.4"
redis = "^5.0.1"
pymysql = "^1.1.0"
pymssql = "^2.2.8"
psycopg2-binary = "^2.9.9"
# cx-Oracle = "^8.3.0"  # 暂时注释掉，避免编译问题
cryptography = "^41.0.7"
schedule = "^1.2.0"
requests = "^2.32.4"
pyodbc = "^5.2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
# flake8 = {version = "^6.1.0", python = "^3.8.1"}
# mypy = "^1.7.1"
# pre-commit = "^3.6.0"

[tool.poetry.scripts]
start = "uvicorn app.main:app --host 0.0.0.0 --port 8000"
dev = "uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
