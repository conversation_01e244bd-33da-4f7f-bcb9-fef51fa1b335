"""
接口标签路由层
定义API路由和端点
"""

from fastapi import APIRouter, Depends, Query, Path
from typing import Optional, List
from app.config.interface.controllers.interface_tag_controller import InterfaceTagController
from app.config.interface.schemas.interface_tag_schema import (
    InterfaceTagCreate,
    InterfaceTagUpdate,
    InterfaceTagResponse,
    InterfaceTagListResponse
)

# 创建路由器
router = APIRouter()

@router.get("/", response_model=InterfaceTagListResponse, summary="获取接口标签列表")
async def get_interface_tags(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    controller: InterfaceTagController = Depends()
):
    """
    获取接口标签列表（分页）

    支持的查询参数：
    - **page**: 页码，从1开始
    - **size**: 每页大小，1-100之间
    - **search**: 搜索关键词，支持名称、描述模糊搜索

    返回分页的接口标签列表，包含总数、页数等信息
    """
    return await controller.get_interface_tags(page, size, search)


@router.get("/enabled", response_model=List[InterfaceTagResponse], summary="获取所有启用的接口标签")
async def get_all_enabled_tags(
    controller: InterfaceTagController = Depends()
):
    """
    获取所有启用的接口标签（用于下拉选择）

    返回所有启用状态的接口标签列表，不分页
    主要用于接口配置时的标签选择
    """
    return await controller.get_all_enabled_tags()


@router.post("/", response_model=InterfaceTagResponse, summary="创建接口标签")
async def create_interface_tag(
    tag_data: InterfaceTagCreate,
    controller: InterfaceTagController = Depends()
):
    """
    创建新的接口标签

    请求体参数：
    - **name**: 标签名称，必填，1-50字符
    - **color**: 标签颜色，必填，十六进制格式如#FF0000
    - **description**: 标签描述，可选，最多200字符
    - **is_enabled**: 是否启用，默认true
    - **created_by**: 创建人，可选

    返回创建的接口标签信息
    """
    return await controller.create_interface_tag(tag_data)


@router.get("/{tag_id}", response_model=InterfaceTagResponse, summary="获取单个接口标签")
async def get_interface_tag(
    tag_id: int = Path(..., description="接口标签ID"),
    controller: InterfaceTagController = Depends()
):
    """
    根据ID获取单个接口标签详情

    路径参数：
    - **tag_id**: 接口标签ID

    返回接口标签详细信息，包含使用该标签的接口数量
    """
    return await controller.get_interface_tag(tag_id)


@router.put("/{tag_id}", response_model=InterfaceTagResponse, summary="更新接口标签")
async def update_interface_tag(
    tag_data: InterfaceTagUpdate,
    tag_id: int = Path(..., description="接口标签ID"),
    controller: InterfaceTagController = Depends()
):
    """
    更新接口标签信息

    路径参数：
    - **tag_id**: 要更新的接口标签ID

    请求体参数（所有参数都是可选的）：
    - **name**: 标签名称，1-50字符
    - **color**: 标签颜色，十六进制格式
    - **description**: 标签描述，最多200字符
    - **is_enabled**: 是否启用

    返回更新后的接口标签信息
    """
    return await controller.update_interface_tag(tag_id, tag_data)


@router.delete("/{tag_id}", summary="删除接口标签")
async def delete_interface_tag(
    tag_id: int = Path(..., description="接口标签ID"),
    controller: InterfaceTagController = Depends()
):
    """
    删除接口标签

    路径参数：
    - **tag_id**: 要删除的接口标签ID

    注意：删除前会检查是否有关联的接口配置，如有关联则不允许删除
    """
    return await controller.delete_interface_tag(tag_id)
