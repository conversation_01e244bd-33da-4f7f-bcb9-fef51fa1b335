/**
 * 统一错误处理工具
 */
import { ElMessage } from 'element-plus';

/**
 * 处理API请求错误
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @returns 是否成功处理错误
 */
export const handleApiError = (error: any, defaultMessage: string = '操作失败'): boolean => {
  if (!error) return false;

  // 打印错误详情
  console.error('API Error:', error);

  // 尝试提取错误消息
  let errorMessage = defaultMessage;

  try {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.message) {
      errorMessage = error.message;

      // 如果有详细的错误信息，尝试提取更多上下文
      if (error.data?.detail) {
        const detail = error.data.detail;
        if (detail.suggestion) {
          errorMessage += `\n建议：${detail.suggestion}`;
        }
        if (detail.related_items && Array.isArray(detail.related_items) && detail.related_items.length > 0) {
          errorMessage += `\n相关项目：${detail.related_items.join(', ')}`;
        }
      }
    } else if (error?.data?.message) {
      errorMessage = error.data.message;
    }
  } catch (parseError) {
    // 如果解析错误信息时出错，使用默认消息
    console.error('Error parsing error message:', parseError);
    errorMessage = defaultMessage;
  }

  // 显示错误消息
  ElMessage.error(errorMessage);
  return true;
};

/**
 * 处理成功消息
 * @param message 成功消息
 */
export const handleSuccessMessage = (message: string = '操作成功'): void => {
  ElMessage.success(message);
};