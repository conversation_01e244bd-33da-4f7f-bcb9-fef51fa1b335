<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Collection /></el-icon>
        <span class="title-text">接口分组管理</span>
      </div>
      <div class="header-actions">
        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索分组名称或分组别名"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAdd">新增分组</el-button>
        <el-button @click="loadInterfaceGroups">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 2024-12-27: 注释掉页签功能，类似客户端管理页面，直接显示表格 -->
    <!-- <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="分组列表管理" name="management"> -->
        <!-- 分组列表 -->
        <el-table
          v-loading="loading"
          :data="interfaceGroups"
          style="width: 100%; min-width: 800px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
          <el-table-column label="分组名称" prop="name" min-width="120" width="200">
            <template #default="{ row }">
              <div class="group-name">
                <el-icon><Folder /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分组别名" prop="pathPrefix" min-width="100" width="150">
            <template #default="{ row }">
              <el-tag :type="getPathPrefixTagType(row.pathPrefix)">{{ row.pathPrefix }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            label="描述"
            prop="description"
            min-width="200"
            width="400"
            show-overflow-tooltip
          />

          <el-table-column label="状态" prop="isEnabled" width="150" align="center">
            <template #default="{ row }">
              <span :class="row.isEnabled ? 'status-enabled' : 'status-disabled'">
                {{ row.isEnabled ? '启用' : '禁用' }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" prop="createdAt" min-width="160" />
          <el-table-column label="更新时间" prop="updatedAt" min-width="160" />

          <el-table-column label="操作" min-width="180" fixed="right" width="150">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>

          <!-- 空状态插槽 -->
          <template #empty>
            <EmptyState
              :type="searchQuery ? 'search' : 'table'"
              :title="searchQuery ? '无搜索结果' : '暂无接口分组'"
              :description="searchQuery ? `没有找到包含「${searchQuery}」的接口分组，请尝试其他搜索条件` : '当前没有配置任何接口分组，您可以点击上方按钮添加新的接口分组'"
              :action-text="searchQuery ? '清除搜索' : '新增分组'"
              @action="searchQuery ? clearSearch : handleAdd"
            />
          </template>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      <!-- </el-tab-pane>
    </el-tabs> -->



    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="确认删除"
      :content="`确定要删除分组 ${deleteItem?.name} 吗？`"
      warning="删除后无法恢复，请确认该分组下没有关联的接口！"
      confirm-text="确认删除"
      confirm-type="danger"
      @confirm="confirmDelete"
      @cancel="deleteDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Collection, Folder, Refresh } from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import interfaceGroupService from '@/services/interface-group.service';
import type { InterfaceGroup } from '@/types/interface-group';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';
import { extractErrorMessage } from '@/utils/common-utils';


// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();

// 2024-12-27: 注释掉页签状态，不再需要页签功能
// const activeTab = ref('management');

// 列表数据
const loading = ref(false);
const interfaceGroups = ref<InterfaceGroup[]>([]);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<InterfaceGroup | null>(null);



// 加载分组列表
const loadInterfaceGroups = async () => {
  loading.value = true;
  try {
    const response = await interfaceGroupService.getInterfaceGroups({
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value || undefined
    });

    interfaceGroups.value = response.items;
    totalCount.value = response.total;
  } catch (error) {
    ElMessage.error('加载分组列表失败');
    console.error('Load interface groups error:', error);
  } finally {
    loading.value = false;
  }
};



// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadInterfaceGroups();
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  handleSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadInterfaceGroups();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadInterfaceGroups();
};

// 新增分组
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增分组',
    component: 'InterfaceGroupForm',
    props: {
      isEdit: false,
      editData: null
    },
    size: '38%'
  });
};

// 编辑分组
const handleEdit = (row: InterfaceGroup) => {
  drawerMessenger.showDrawer({
    title: '编辑分组',
    component: 'InterfaceGroupForm',
    props: {
      isEdit: true,
      editData: {
        id: row.id,
        name: row.name,
        pathPrefix: row.pathPrefix,
        description: row.description || '',
        isEnabled: row.isEnabled
      }
    },
    size: '38%'
  });
};



// 删除分组
const handleDelete = (row: InterfaceGroup) => {
  deleteItem.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;

  try {
    await interfaceGroupService.deleteInterfaceGroup(deleteItem.value.id);
    ElMessage.success(`分组 ${deleteItem.value.name} 已删除`);

    // 删除操作：跳转第一页刷新（最高效）
    loadInterfaceGroupsToFirstPage();
  } catch (error: any) {
    ElMessage.error(extractErrorMessage(error, '删除分组失败'));
    console.error('Delete group error:', error);
  } finally {
    deleteDialogVisible.value = false;
    deleteItem.value = null;
  }
};

// 获取分组别名标签类型（不同颜色）
const getPathPrefixTagType = (pathPrefix: string) => {
  const tagTypeMap: Record<string, string> = {
    'project': 'primary',    // 蓝色 - 项目管理
    'finance': 'success',    // 绿色 - 财务系统
    'seal': 'warning',       // 橙色 - 印章系统
    'office': 'info',        // 灰色 - 办公系统
    'portal': 'danger',      // 红色 - 门户系统
    'hr': '',                // 默认色 - 人力资源
    'contract': 'success'    // 绿色 - 合同系统
  };
  return tagTypeMap[pathPrefix] || 'info';
};



// 刷新到第一页的方法
const loadInterfaceGroupsToFirstPage = async () => {
  currentPage.value = 1;
  await loadInterfaceGroups();
};

// 页面加载时注册刷新机制
onMounted(() => {
  loadInterfaceGroups();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.INTERFACE_GROUP,
    loadInterfaceGroups,           // 保持当前页刷新
    loadInterfaceGroupsToFirstPage // 跳转第一页刷新
  );


});

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.INTERFACE_GROUP);


});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 引入公共样式，但保留现有样式作为备份和覆盖 */

/* 接口分组管理页面特有样式 */
/* 注意：以下样式会覆盖公共样式，确保页面效果不变 */

/* 分组名称样式 */
.group-name {
  display: flex;
  align-items: center;
  font-weight: 400;
}

.group-name .el-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #3FC8DD;
}

/* 描述文本样式已由 show-overflow-tooltip 自动处理 */


/* 状态标签样式 */
:deep(.el-tag) {
  font-weight: 400;
  padding: 2px 8px;
  border: 1px solid;
}
</style>