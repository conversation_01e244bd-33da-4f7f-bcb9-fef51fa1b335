---
type: "agent_requested"
description: "Example description"
---
# Mapi项目开发历史记录

## 项目概述
- **前端项目**：Mapi-Console（原v5）- 管理控制台
- **后端项目**：Mapi-ConfigCore - 配置管理核心服务  
- **报表项目**：Mapi-Viewer - 报表展示系统（预留）

## 已完成的模块

### 1. 数据源管理
- **页面**：`/src/views/datasource/DataSourceManagement.vue`
- **服务**：`/src/services/datasource.service.ts`
- **Mock**：`/src/mock/datasource.mock.ts`
- **类型**：`/src/types/datasource.ts`
- **功能**：数据源的增删改查、连接测试

### 2. 接口管理
#### 接口分组管理
- **页面**：`/src/views/interface/InterfaceGroup.vue`
- **服务**：`/src/services/interface-group.service.ts`
- **Mock**：`/src/mock/interface-group.mock.ts`
- **类型**：`/src/types/interface-group.ts`
- **修改**：2024-12-27 去掉页签功能，直接显示表格

#### 接口标签管理
- **页面**：`/src/views/interface/InterfaceTag.vue`
- **服务**：`/src/services/interface-tag.service.ts`
- **Mock**：`/src/mock/interface-tag.mock.ts`
- **类型**：`/src/types/interface-tag.ts`
- **修改**：2024-12-27 去掉页签功能，直接显示表格

#### 接口配置管理
- **页面**：`/src/views/interface/InterfaceConfig.vue`
- **功能**：接口的详细配置管理

### 3. 客户端管理
#### 客户端管理
- **页面**：`/src/views/client/ClientManagement.vue`
- **服务**：`/src/services/client.service.ts`
- **Mock**：`/src/mock/client.mock.ts`
- **类型**：`/src/types/client.ts`
- **功能**：客户端的增删改查、状态管理

#### 权限管理
- **页面**：`/src/views/client/PermissionManagement.vue`
- **权限查看**：`/src/views/client/ClientPermissionViewForm.vue`
- **权限设置**：`/src/views/client/ClientPermissionSettingForm.vue`
- **功能**：客户端权限的查看和设置，支持表格树展示

## 重要设计决策

### 数据分离原则
- **Mock数据分离**：所有mock数据独立存储在`/src/mock/`目录
- **Service职责单一**：service只负责数据获取，不包含数据本身
- **菜单数据分离**：2024-12-27 将menu.service.ts中的数据提取到menu.mock.ts

### 抽屉使用规则
- **命名规则**：所有抽屉组件文件名带Form后缀
- **打开方式**：使用`useGlobalDrawerMessenger`的`showDrawer`方法
- **关闭方式**：使用`drawerMessenger.hideDrawer()`

### 页面样式规范
- **容器**：使用`<div class="container">`作为根容器
- **页面头部**：`<div class="page-header">`包含标题和操作按钮
- **表格样式**：统一的行高和单元格样式
- **公共组件**：SearchComponent和PaginationComponent

### 权限设置特色功能
- **表格树结构**：支持分组和接口的层级显示
- **复选框缩进**：接口行复选框缩进30px，分组行10px
- **路径显示**：接口路径在名称下方显示，蓝色背景
- **防抖搜索**：300ms防抖优化搜索性能
- **抽屉宽度**：权限设置40%，权限查看40%

## 技术栈
- **前端框架**：Vue 3 + TypeScript
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **构建工具**：Vite
- **样式**：SCSS

### 4. 后端架构（Mapi-ConfigCore）
#### 动态API架构
- **路由层**：`/app/service/dynamic_router.py` - 动态路由解析
- **控制器层**：`/app/service/dynamic_controller.py` - 请求处理
- **服务层**：`/app/service/dynamic_service.py` - 业务逻辑
- **模型层**：`/app/service/dynamic_model.py` - 数据模型
- **数据访问层**：`/app/service/dynamic_repository.py` - 数据库操作
- **验证层**：`/app/service/dynamic_schema.py` - 数据验证
- **功能**：支持通过配置路径直接访问API（如 `/api/v1/project/ht1`）

#### 接口配置管理
- **模型**：`/app/config/interface/models/interface_config_model.py`
- **仓储**：`/app/config/interface/repositories/interface_config_repository.py`
- **服务**：`/app/config/interface/services/interface_config_service.py`
- **控制器**：`/app/config/interface/controllers/interface_config_controller.py`
- **路由**：`/app/config/interface/routers/interface_config_router.py`
- **功能**：接口配置的CRUD操作，ORM配置生成

### 5. 空组件规范（2024-12-28）
#### 已添加空组件的页面
- **接口配置管理**：`InterfaceConfig.vue` - 包含"新增接口"按钮
- **客户端管理**：`ClientManagement.vue` - 包含"新增客户端"按钮
- **权限管理**：`PermissionManagement.vue` - 包含"前往客户端管理"按钮
- **日志管理**：`LogManagement.vue` - 包含"重置查询条件"按钮
- **任务管理**：`TaskManagement.vue` - 包含"创建任务"按钮
- **操作日志**：`OperationLogs.vue` - 简洁提示信息

#### 已有空组件的页面
- **数据源列表**：`DataSourceList.vue` - 使用EmptyState组件
- **接口分组**：`InterfaceGroup.vue` - 使用EmptyState组件
- **接口标签**：`InterfaceTag.vue` - 使用EmptyState组件
- **接口测试**：`InterfaceTest.vue` - 多个空状态

## 下一步计划
1. ✅ 搭建后端架构（Mapi-ConfigCore）
2. ✅ 实现动态API核心功能
3. 完善数据库连接和权限验证
4. 逐步替换Mock数据为真实API
5. 完成剩余前端模块

## 重要提醒
- 新建页面必须遵循已记录的规范
- 抽屉组件必须使用正确的命名和调用方式
- 样式导入使用@use而不是@import
- 所有新功能都要有对应的Service和Mock实现
- **所有数据列表页面必须添加空组件**，使用`<template #empty>`插槽
- 空组件必须包含描述性文字、引导性提示和操作按钮
