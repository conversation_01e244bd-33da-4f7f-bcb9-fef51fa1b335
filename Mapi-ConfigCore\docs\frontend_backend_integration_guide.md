# 前后端API对接调试规范

## 🎯 核心原则

### 1. 命名规范
- **后端：** 严格使用下划线命名 (`orm_model_config`, `parameter_config`)
- **前端：** 使用驼峰命名 (`ormModelConfig`, `parameterConfig`)
- **转换：** 前端通过 `common-utils` 工具自动转换
- **禁止：** 后端Schema中添加alias进行驼峰转换

### 2. 响应格式规范
```json
{
  "success": true/false,
  "message": "操作结果描述",
  "error_code": "RESOURCE_CONFLICT/RESOURCE_NOT_FOUND/VALIDATION_ERROR",
  "detail": {
    "具体的错误详情": "包含建议和相关信息"
  }
}
```

### 3. HTTP状态码规范
- **200 OK：** 操作成功
- **404 Not Found：** 资源不存在
- **409 Conflict：** 资源冲突（如删除时有关联）
- **422 Unprocessable Entity：** 验证失败
- **500 Internal Server Error：** 服务器内部错误

## 🔧 常见问题及解决方案

### 问题1：前端收到500错误
**症状：** 前端显示"删除数据源失败: 500 Internal Server Error"

**排查步骤：**
1. 检查前端请求地址是否正确
2. 检查Vite代理配置端口是否匹配后端端口
3. 检查后端控制台是否有异常日志
4. 验证API直接调用是否正常

**解决方案：**
```typescript
// vite.config.ts
proxy: {
  '/api': {
    target: 'http://127.0.0.1:8002', // 确保端口正确
    changeOrigin: true,
    secure: false
  }
}
```

### 问题2：前端错误处理异常
**症状：** 前端显示"发生意外错误"或JSON解析失败

**排查步骤：**
1. 检查后端返回的JSON格式是否正确
2. 检查前端错误处理逻辑
3. 添加调试日志查看实际响应内容

**解决方案：**
```typescript
// 正确的错误处理
async deleteDataSource(id: number): Promise<boolean> {
  const response = await fetch(`${this.baseUrl}/${id}`, {
    method: 'DELETE'
  });

  if (response.ok) {
    return true;
  } else {
    const errorData = await response.json();
    const errorMessage = errorData.message || '删除数据源失败';
    const error = new Error(errorMessage);
    (error as any).data = errorData;
    (error as any).status = response.status;
    throw error;
  }
}
```

### 问题3：业务错误信息不完整
**症状：** 前端只显示简单错误信息，缺少详细建议

**解决方案：**
```typescript
// error-handler.ts
if (error.data?.detail) {
  const detail = error.data.detail;
  if (detail.suggestion) {
    errorMessage += `\n建议：${detail.suggestion}`;
  }
  if (detail.related_items && Array.isArray(detail.related_items)) {
    errorMessage += `\n相关项目：${detail.related_items.join(', ')}`;
  }
}
```

## 📋 调试检查清单

### 后端检查
- [ ] 业务逻辑返回正确的响应格式
- [ ] Router层正确设置HTTP状态码
- [ ] 业务错误已记录到业务日志
- [ ] 字段命名使用下划线格式
- [ ] 没有添加不必要的alias

### 前端检查
- [ ] 使用相对路径调用API
- [ ] Vite代理配置端口正确
- [ ] 错误处理逻辑完整
- [ ] 错误信息提取包含详细信息
- [ ] 修改配置后已重启开发服务器

### 联调检查
- [ ] 直接API调用返回正确状态码和格式
- [ ] 前端能正确解析错误响应
- [ ] 用户看到完整的错误信息和建议
- [ ] 业务日志正确记录操作详情

## 🚀 最佳实践

### 1. 开发流程
1. **后端优先：** 先确保后端API返回正确格式
2. **直接测试：** 使用工具直接测试API
3. **前端对接：** 确认代理配置后进行前端对接
4. **Mock代码处理：** API接管后立即注释掉Mock相关代码
5. **错误处理：** 完善前端错误处理逻辑
6. **用户体验：** 确保错误信息对用户友好

### 2. Mock代码管理规范
**重要：API接管Mock后，必须注释掉Mock代码，避免混淆！**

```typescript
// ❌ 错误：保留Mock导入和选择逻辑
import { mockGetData } from '@/mock/data.mock';
const useAPI = true;
const service = useAPI ? new ApiService() : new MockService();

// ✅ 正确：注释Mock，强制使用API
/**
 * 数据服务 - 已禁用Mock，强制使用API
 */
// Mock导入已注释 - 数据服务已切换到API模式
// import { mockGetData } from '@/mock/data.mock';
const service = new ApiService();
```

### 2. 调试技巧
- 使用浏览器开发者工具查看网络请求
- 添加临时调试日志查看响应内容
- 分别测试成功和失败场景
- 验证业务日志记录是否完整

### 3. 代码规范
- 后端专注业务逻辑，不处理前端格式
- 前端负责格式转换和用户体验
- 错误信息要包含原因、建议和相关信息
- 保持代码整洁，及时清理调试代码

## 📝 模板代码

### 后端Router错误处理模板
```python
result = await controller.delete_resource(resource_id)

if isinstance(result, dict) and result.get("success") is False:
    from fastapi.responses import JSONResponse
    
    if result.get("error_code") == "RESOURCE_CONFLICT":
        return JSONResponse(status_code=409, content=result)
    elif result.get("error_code") == "RESOURCE_NOT_FOUND":
        return JSONResponse(status_code=404, content=result)

return result
```

### 前端错误处理模板
```typescript
if (response.ok) {
  return true;
} else {
  const errorData = await response.json();
  const errorMessage = errorData.message || '操作失败';
  const error = new Error(errorMessage);
  (error as any).data = errorData;
  (error as any).status = response.status;
  throw error;
}
```

---

**记住：执行规范是第一选择，避免重复调试同样的问题！**
