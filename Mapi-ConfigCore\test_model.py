#!/usr/bin/env python3
"""
测试InterfaceConfig模型的table_type字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.shared.database import SessionLocal
from app.config.interface.models.interface_config_model import InterfaceConfigModel

def test_model():
    """测试模型"""
    try:
        # 获取数据库会话
        db = SessionLocal()
        
        # 查询接口配置5
        config = db.query(InterfaceConfigModel).filter(InterfaceConfigModel.id == 5).first()
        
        if config:
            print(f"ID: {config.id}")
            print(f"Name: {config.name}")
            print(f"Table Name: {config.table_name}")
            print(f"Table Type (属性): {getattr(config, 'table_type', 'NOT_FOUND')}")
            print(f"Has table_type attr: {hasattr(config, 'table_type')}")
            
            # 测试to_dict方法
            config_dict = config.to_dict()
            print(f"Dict tableType: {config_dict.get('tableType', 'NOT_FOUND')}")
            print(f"Dict keys: {list(config_dict.keys())}")
            
        else:
            print("❌ 未找到接口配置5")
            
        db.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_model()
