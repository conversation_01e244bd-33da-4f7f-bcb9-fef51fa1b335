<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Timer /></el-icon>
      定时任务
    </h2>

    <div class="content-container">
      <!-- 任务列表 -->
      <div class="section-title">
        <el-icon><List /></el-icon>
        任务列表
      </div>

      <div class="task-actions">
        <el-button type="primary" @click="showAddTaskDialog">
          <el-icon><Plus /></el-icon>
          添加任务
        </el-button>
        <el-button @click="refreshTasks">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="task-card">
        <el-table :data="tasks" style="width: 100%" v-loading="loading">
          <el-table-column prop="name" label="任务名称" width="200"></el-table-column>
          <el-table-column prop="description" label="任务描述" min-width="250"></el-table-column>
          <el-table-column prop="cron" label="Cron表达式" width="150"></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastRun" label="最后执行" width="180"></el-table-column>
          <el-table-column prop="nextRun" label="下次执行" width="180"></el-table-column>
          <el-table-column value="操作" width="250">
            <template #default="scope">
              <el-button size="small" @click="runTask(scope.row)" :disabled="scope.row.status === 'running'">
                立即执行
              </el-button>
              <el-button size="small" @click="toggleTask(scope.row)" :type="scope.row.status === 'active' ? 'warning' : 'success'">
                {{ scope.row.status === 'active' ? '暂停' : '启用' }}
              </el-button>
              <el-button type="primary" size="small" @click="editTask(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteTask(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 执行日志 -->
      <div class="section-title">
        <el-icon><Document /></el-icon>
        执行日志
      </div>

      <div class="log-card">
        <el-table :data="taskLogs" style="width: 100%">
          <el-table-column prop="taskName" value="任务名称" width="200"></el-table-column>
          <el-table-column prop="startTime" value="开始时间" width="180"></el-table-column>
          <el-table-column prop="endTime" value="结束时间" width="180"></el-table-column>
          <el-table-column prop="duration" value="执行时长" width="120"></el-table-column>
          <el-table-column prop="status" value="执行状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" value="执行结果" min-width="200"></el-table-column>
          <el-table-column value="操作" width="100">
            <template #default="scope">
              <el-button size="small" @click="viewLogDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 添加任务对话框 -->
      <el-dialog v-model="addTaskVisible" title="添加定时任务" width="600px">
        <el-form :model="newTask" label-width="120px">
          <el-form-item value="任务名称">
            <el-input v-model="newTask.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>

          <el-form-item value="任务描述">
            <el-input v-model="newTask.description" type="textarea" placeholder="请输入任务描述"></el-input>
          </el-form-item>

          <el-form-item value="任务类型">
            <el-select v-model="newTask.type" placeholder="选择任务类型">
              <el-option label="数据同步" value="sync"></el-option>
              <el-option label="日志清理" value="cleanup"></el-option>
              <el-option label="数据备份" value="backup"></el-option>
              <el-option label="系统检查" value="check"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item value="Cron表达式">
            <el-input v-model="newTask.cron" placeholder="0 0 2 * * ?"></el-input>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              示例: 0 0 2 * * ? (每天凌晨2点执行)
            </div>
          </el-form-item>

          <el-form-item value="执行参数">
            <el-input v-model="newTask.params" type="textarea" placeholder="JSON格式的执行参数"></el-input>
          </el-form-item>
        </el-form>

        <template #footer>
          <el-button @click="addTaskVisible = false">取消</el-button>
          <el-button type="primary" @click="addTask">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Timer, List, Plus, Refresh, Document } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 加载状态
const loading = ref(false)

// 添加任务对话框
const addTaskVisible = ref(false)

// 新任务表单
const newTask = ref({
  name: '',
  description: '',
  type: '',
  cron: '',
  params: ''
})

// 任务列表
const tasks = ref([
  {
    id: 1,
    name: '数据同步任务',
    description: '同步用户数据到备份数据库',
    cron: '0 0 2 * * ?',
    status: 'active',
    lastRun: '2023-12-15 02:00:00',
    nextRun: '2023-12-16 02:00:00'
  },
  {
    id: 2,
    name: '日志清理任务',
    description: '清理30天前的系统日志',
    cron: '0 0 3 * * ?',
    status: 'active',
    lastRun: '2023-12-15 03:00:00',
    nextRun: '2023-12-16 03:00:00'
  },
  {
    id: 3,
    name: '系统检查任务',
    description: '检查系统健康状态',
    cron: '0 */30 * * * ?',
    status: 'paused',
    lastRun: '2023-12-15 14:00:00',
    nextRun: '-'
  }
])

// 任务执行日志
const taskLogs = ref([
  {
    id: 1,
    taskName: '数据同步任务',
    startTime: '2023-12-15 02:00:00',
    endTime: '2023-12-15 02:05:23',
    duration: '5分23秒',
    status: 'success',
    message: '成功同步1250条用户数据'
  },
  {
    id: 2,
    taskName: '日志清理任务',
    startTime: '2023-12-15 03:00:00',
    endTime: '2023-12-15 03:01:15',
    duration: '1分15秒',
    status: 'success',
    message: '成功清理356个日志文件，释放空间2.3GB'
  },
  {
    id: 3,
    taskName: '系统检查任务',
    startTime: '2023-12-15 14:00:00',
    endTime: '2023-12-15 14:00:45',
    duration: '45秒',
    status: 'failed',
    message: '数据库连接超时'
  }
])

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    active: 'success',
    paused: 'warning',
    running: 'primary',
    error: 'danger'
  }
  return colors[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '运行中',
    paused: '已暂停',
    running: '执行中',
    error: '错误'
  }
  return texts[status] || '未知'
}

// 显示添加任务对话框
const showAddTaskDialog = () => {
  newTask.value = {
    name: '',
    description: '',
    type: '',
    cron: '',
    params: ''
  }
  addTaskVisible.value = true
}

// 添加任务
const addTask = () => {
  if (!newTask.value.name || !newTask.value.cron) {
    ElMessage.warning('请填写任务名称和Cron表达式')
    return
  }

  tasks.value.push({
    id: tasks.value.length + 1,
    name: newTask.value.name,
    description: newTask.value.description,
    cron: newTask.value.cron,
    status: 'active',
    lastRun: '-',
    nextRun: '计算中...'
  })

  addTaskVisible.value = false
  ElMessage.success('任务添加成功')
}

// 刷新任务
const refreshTasks = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('任务列表已刷新')
  }, 1000)
}

// 立即执行任务
const runTask = (task: any) => {
  ElMessage.info(`正在执行任务: ${task.name}`)
  task.status = 'running'

  setTimeout(() => {
    task.status = 'active'
    task.lastRun = new Date().toLocaleString()
    ElMessage.success(`任务 ${task.name} 执行完成`)
  }, 3000)
}

// 切换任务状态
const toggleTask = (task: any) => {
  if (task.status === 'active') {
    task.status = 'paused'
    task.nextRun = '-'
    ElMessage.warning(`任务 ${task.name} 已暂停`)
  } else {
    task.status = 'active'
    task.nextRun = '计算中...'
    ElMessage.success(`任务 ${task.name} 已启用`)
  }
}

// 编辑任务
const editTask = (_task: any) => {
  ElMessage.info('编辑任务功能开发中...')
}

// 删除任务
const deleteTask = (task: any) => {
  ElMessageBox.confirm(`确定要删除任务 "${task.name}" 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      tasks.value.splice(index, 1)
      ElMessage.success('任务删除成功')
    }
  })
}

// 查看日志详情
const viewLogDetail = (_log: any) => {
  ElMessage.info('查看日志详情功能开发中...')
}
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary-color);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary-color);
  padding-left: 12px;
}

.task-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.task-card, .log-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

/* 修复表格宽度计算问题 */
:deep(.el-table__header) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__body) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

/* 修复滚动条视图导致的列对齐问题 */
:deep(.el-scrollbar__view) {
  display: block !important;
  width: 100% !important;
}

/* 确保表格容器正确显示 */
:deep(.el-table) {
  width: 100% !important;
  table-layout: auto !important;
}

/* 自定义滚动条样式 */
:deep(.el-table__header-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__header-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  height: 6px;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__header-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
}

:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__body-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__body-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3;
}
</style>