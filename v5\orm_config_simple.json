{"model_name": "ZdyVwGetZyhtListModel", "table_name": "ZDY_VW_GET_ZYHT_LIST", "description": "ZDY_VW_GET_ZYHT_LIST 数据模型", "http_methods": ["GET", "POST", "PUT", "DELETE"], "datasource_info": {"datasource_id": 4, "db_type": "mssql", "table_type": "view"}, "sqlalchemy_model": {"class_name": "ZdyVwGetZyhtListModel", "table_name": "ZDY_VW_GET_ZYHT_LIST", "fields": [{"name": "F_ID", "original_name": "F_ID", "type": "decimal", "sqlalchemy_type": "Numeric", "nullable": false, "primary_key": true, "default": null, "comment": "主键ID", "searchable": false, "filterable": true, "rangeable": true, "sortable": true}, {"name": "F_NAME", "original_name": "F_NAME", "type": "n<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(200)", "nullable": true, "primary_key": false, "default": null, "comment": "项目名称", "searchable": true, "filterable": false, "rangeable": false, "sortable": true}, {"name": "F_STATE", "original_name": "F_STATE", "type": "<PERSON><PERSON><PERSON>", "sqlalchemy_type": "String(50)", "nullable": true, "primary_key": false, "default": null, "comment": "状态", "searchable": true, "filterable": true, "rangeable": false, "sortable": true}]}, "query_mapping": {"fuzzy_search_fields": ["F_NAME", "F_STATE"], "exact_match_fields": ["F_ID"], "range_query_fields": ["F_ID"], "default_sort": "F_ID", "default_order": "desc", "allowed_sort_fields": ["F_ID", "F_NAME", "F_STATE"]}, "response_config": {"exclude_fields": [], "date_format": "%Y-%m-%d %H:%M:%S", "include_total_count": true, "null_to_empty": false}}