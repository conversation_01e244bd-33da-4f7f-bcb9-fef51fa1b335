#!/usr/bin/env python3
"""
数据库初始化脚本
创建SQLite数据库文件和数据源表
"""

import sqlite3
import os
from datetime import datetime

# 数据库文件路径（相对于项目根目录）
DB_PATH = "../../../../mapi-data/mapi_config.db"

def create_database():
    """创建数据库文件和目录"""
    # 确保mapi-data目录存在
    db_dir = os.path.dirname(DB_PATH)
    os.makedirs(db_dir, exist_ok=True)
    
    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    print(f"✅ 数据库文件已创建: {DB_PATH}")
    return conn, cursor

def create_data_sources_table(cursor):
    """创建数据源表"""
    sql = """
    CREATE TABLE IF NOT EXISTS data_sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL UNIQUE,
        description VARCHAR(200) NULL,
        db_type VARCHAR(20) NOT NULL,
        host VARCHAR(255) NOT NULL,
        port INTEGER NOT NULL,
        database VARCHAR(100) NOT NULL,
        username VARCHAR(100) NOT NULL,
        password VARCHAR(500) NOT NULL,  -- AES加密存储
        max_connections INTEGER NOT NULL DEFAULT 10,
        connection_timeout INTEGER NOT NULL DEFAULT 60,  -- 60秒超时
        refresh_time VARCHAR(10) NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'active',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(50) NULL
    );
    """
    
    cursor.execute(sql)
    print("✅ 数据源表 (data_sources) 已创建")

def create_indexes(cursor):
    """创建索引"""
    indexes = [
        "CREATE UNIQUE INDEX IF NOT EXISTS idx_data_sources_name ON data_sources(name);",
        "CREATE INDEX IF NOT EXISTS idx_data_sources_status ON data_sources(status);",
        "CREATE INDEX IF NOT EXISTS idx_data_sources_db_type ON data_sources(db_type);"
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    
    print("✅ 索引已创建")

def insert_sample_data(cursor):
    """插入示例数据"""
    sample_data = [
        (
            '管理系统数据库',
            '项目管理系统的主数据库',
            'sqlserver',
            '192.168.1.100',
            1433,
            'ProjectManagement_DB',
            'pm_admin',
            'encrypted_password_here',  # 实际使用时需要AES加密
            20,
            60,
            '02:00',
            'active',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'admin'
        ),
        (
            '财务管理数据库',
            '财务系统数据库连接',
            'oracle',
            '192.168.1.101',
            1521,
            'FINANCE_PROD',
            'finance_user',
            'encrypted_password_here',
            30,
            60,
            '01:30',
            'active',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'admin'
        ),
        (
            '印章管理数据库',
            '印章管理系统数据库',
            'mysql',
            '*************',
            3306,
            'seal_management',
            'seal_admin',
            'encrypted_password_here',
            8,
            60,
            '03:30',
            'inactive',
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'admin'
        )
    ]
    
    insert_sql = """
    INSERT OR IGNORE INTO data_sources 
    (name, description, db_type, host, port, database, username, password, 
     max_connections, connection_timeout, refresh_time, status, created_at, updated_at, created_by)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    cursor.executemany(insert_sql, sample_data)
    print("✅ 示例数据已插入")

def main():
    """主函数"""
    print("🚀 开始初始化Mapi配置数据库...")
    
    try:
        # 创建数据库
        conn, cursor = create_database()
        
        # 创建数据源表
        create_data_sources_table(cursor)
        
        # 创建索引
        create_indexes(cursor)
        
        # 插入示例数据
        insert_sample_data(cursor)
        
        # 提交事务
        conn.commit()
        
        # 验证表创建
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 数据库中的表: {[table[0] for table in tables]}")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM data_sources;")
        count = cursor.fetchone()[0]
        print(f"📊 数据源表中的记录数: {count}")
        
        print("🎉 数据库初始化完成！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()
