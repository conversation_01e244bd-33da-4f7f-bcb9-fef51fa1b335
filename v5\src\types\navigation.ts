// src/types/menu.ts
// 页签配置类型（与MainIndex.vue中的TabInfo兼容）
export interface PageConfig {
  routePath?: any;
  name: string; // 唯一标识（如'dashboard-1'）
  title: string; // 页签显示标题（如'运维监控'）
  path: string; // 跳转路径（如'dashboard/DashboardHome.html'）
  paneName: string; // 页签面板名称，用于标签页切换
}

// 子菜单类型（仅包含导航所需属性）
export interface SubMenu {
  // path: any; // 2024-01-20: 移除冗余path字段，统一使用pageConfigs中的path进行路由跳转
  name: string; // 唯一标识，对应pageConfigs中的name
  title: string; // 显示标题
}

// 一级菜单类型（用于左侧导航）
export interface TopLevelMenu {
  key: string; // 菜单组标识（如'dashboard'
  title: string; // 菜单组显示名称（如'首页看板'
  icon?: string; // 菜单图标名称（如'House'
  children?: SubMenu[]; // 子菜单列表
}

// 面包屑项类型（新增）
export interface BreadcrumbItem {
  label: string;
  title: string; // 显示文本
  path?: string; // 可选跳转路径（用于点击导航）
}

// 完整菜单数据结构（API应返回此格式）
export interface MenuData {
  topLevelMenus: TopLevelMenu[]; // 一级菜单列表
  pageConfigs: PageConfig[]; // 所有页签配置列表
}