# Mapi项目开发历史记录

## 项目概述
- **前端项目**：Mapi-Console（原v5）- 管理控制台
- **后端项目**：Mapi-ConfigCore - 配置管理核心服务  
- **报表项目**：Mapi-Viewer - 报表展示系统（预留）

## 已完成的模块

### 1. 数据源管理
- **页面**：`/src/views/datasource/DataSourceManagement.vue`
- **服务**：`/src/services/datasource.service.ts`
- **Mock**：`/src/mock/datasource.mock.ts`
- **类型**：`/src/types/datasource.ts`
- **功能**：数据源的增删改查、连接测试

### 2. 接口管理
#### 接口分组管理
- **页面**：`/src/views/interface/InterfaceGroup.vue`
- **服务**：`/src/services/interface-group.service.ts`
- **Mock**：`/src/mock/interface-group.mock.ts`
- **类型**：`/src/types/interface-group.ts`
- **修改**：2024-12-27 去掉页签功能，直接显示表格

#### 接口标签管理
- **页面**：`/src/views/interface/InterfaceTag.vue`
- **服务**：`/src/services/interface-tag.service.ts`
- **Mock**：`/src/mock/interface-tag.mock.ts`
- **类型**：`/src/types/interface-tag.ts`
- **修改**：2024-12-27 去掉页签功能，直接显示表格

#### 接口配置管理
- **页面**：`/src/views/interface/InterfaceConfig.vue`
- **功能**：接口的详细配置管理

### 3. 客户端管理
#### 客户端管理
- **页面**：`/src/views/client/ClientManagement.vue`
- **服务**：`/src/services/client.service.ts`
- **Mock**：`/src/mock/client.mock.ts`
- **类型**：`/src/types/client.ts`
- **功能**：客户端的增删改查、状态管理

#### 权限管理
- **页面**：`/src/views/client/PermissionManagement.vue`
- **权限查看**：`/src/views/client/ClientPermissionViewForm.vue`
- **权限设置**：`/src/views/client/ClientPermissionSettingForm.vue`
- **功能**：客户端权限的查看和设置，支持表格树展示

## 重要设计决策

### 数据分离原则
- **Mock数据分离**：所有mock数据独立存储在`/src/mock/`目录
- **Service职责单一**：service只负责数据获取，不包含数据本身
- **菜单数据分离**：2024-12-27 将menu.service.ts中的数据提取到menu.mock.ts

### 抽屉使用规则
- **命名规则**：所有抽屉组件文件名带Form后缀
- **打开方式**：使用`useGlobalDrawerMessenger`的`showDrawer`方法
- **关闭方式**：使用`drawerMessenger.hideDrawer()`

### 页面样式规范
- **容器**：使用`<div class="container">`作为根容器
- **页面头部**：`<div class="page-header">`包含标题和操作按钮
- **表格样式**：统一的行高和单元格样式
- **公共组件**：SearchComponent和PaginationComponent

### 权限设置特色功能
- **表格树结构**：支持分组和接口的层级显示
- **复选框缩进**：接口行复选框缩进30px，分组行10px
- **路径显示**：接口路径在名称下方显示，蓝色背景
- **防抖搜索**：300ms防抖优化搜索性能
- **抽屉宽度**：权限设置40%，权限查看40%

## 技术栈
- **前端框架**：Vue 3 + TypeScript
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **构建工具**：Vite
- **样式**：SCSS

## 下一步计划
1. 搭建后端架构（Mapi-ConfigCore）
2. 实现对应的API接口
3. 逐步替换Mock数据为真实API
4. 完成剩余前端模块

## 重要提醒
- 新建页面必须遵循已记录的规范
- 抽屉组件必须使用正确的命名和调用方式
- 样式导入使用@use而不是@import
- 所有新功能都要有对应的Service和Mock实现
