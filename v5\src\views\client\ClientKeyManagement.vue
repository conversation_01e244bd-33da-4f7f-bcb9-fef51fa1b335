<template>
  <div class="drawer-form-container">
    <div class="key-management-content">
      <!-- 密钥信息卡片 -->
      <div class="key-cards">
        <!-- 客户端ID卡片 -->
        <div class="key-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon"><Key /></el-icon>
              <span>客户端ID</span>
            </div>
          </div>
          <div class="card-content">
            <div class="key-display">
              <div class="key-text">{{ clientData.clientId }}</div>
              <el-button
                type="primary"
                size="small"
                @click="copyToClipboard(clientData.clientId)"
                class="copy-button"
              >
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
            </div>
            <div class="key-description">客户端的唯一标识，用于API访问认证</div>
          </div>
        </div>

        <!-- 安全密钥卡片 -->
        <div class="key-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon"><Lock /></el-icon>
              <span>安全密钥</span>
            </div>
            <el-button
              type="danger"
              size="small"
              @click="handleRegenerateSecret"
              :loading="regenerating"
              class="regenerate-button"
            >
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
          </div>
          <div class="card-content">
            <div class="key-display">
              <div class="key-text secret-text" :class="{ 'show-secret': showSecret }">
                {{ showSecret ? clientData.clientSecret : '••••••••••••••••••••••••••••••••' }}
              </div>
              <div class="key-actions">
                <el-button
                  type="info"
                  size="small"
                  @click="toggleSecretVisibility"
                  class="toggle-button"
                >
                  <el-icon><View v-if="!showSecret" /><Hide v-else /></el-icon>
                  {{ showSecret ? '隐藏' : '显示' }}
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="copyToClipboard(clientData.clientSecret)"
                  class="copy-button"
                >
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-button>
              </div>
            </div>
            <div class="key-description">客户端的安全密钥，请妥善保管，不要泄露给他人</div>
          </div>
        </div>

        <!-- Token有效期卡片 -->
        <div class="key-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon"><Clock /></el-icon>
              <span>Token有效期</span>
            </div>
          </div>
          <div class="card-content">
            <div class="key-display">
              <div class="key-text expires-text">{{ formatTokenExpires(clientData.tokenExpiresIn) }}</div>
            </div>
            <div class="key-description">Token的有效期，过期后需要重新获取</div>
          </div>
        </div>
      </div>

      <!-- 安全提示 -->
      <el-alert
        title="安全提示"
        type="warning"
        :closable="false"
        show-icon
        class="security-alert"
      >
        <template #default>
          <div class="security-tips">
            <p>• 客户端密钥是敏感信息，请妥善保管，不要在代码中硬编码</p>
            <p>• 重新生成密钥后，使用旧密钥的所有Token将立即失效</p>
            <p>• 建议定期更换密钥以提高安全性</p>
            <p>• 如发现密钥泄露，请立即重新生成</p>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CopyDocument, View, Hide } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'
import clientService from '@/services/client.service'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const showSecret = ref(false)
const regenerating = ref(false)
const clientData = reactive({
  id: 0,
  clientId: '',
  clientName: '',
  clientSecret: '',
  description: '',
  tokenExpiresIn: 0,
  status: 'enabled',
  createdAt: '',
  updatedAt: ''
})

// 监听抽屉属性变化，获取客户端数据
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.clientData) {
    Object.assign(clientData, newProps.clientData)
  }
}, { immediate: true, deep: true })

// 切换密钥显示/隐藏
const toggleSecretVisibility = () => {
  showSecret.value = !showSecret.value
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 重新生成密钥
const handleRegenerateSecret = async () => {
  try {
    await ElMessageBox.confirm(
      '重新生成密钥后，使用旧密钥的所有Token将立即失效，确定要继续吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    regenerating.value = true
    const result = await clientService.regenerateClientSecret(clientData.id)
    clientData.clientSecret = result.clientSecret
    clientData.updatedAt = new Date().toLocaleString('zh-CN')
    
    ElMessage.success('密钥重新生成成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重新生成密钥失败')
    }
  } finally {
    regenerating.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  drawerMessenger.hideDrawer()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 格式化Token有效期
const formatTokenExpires = (seconds) => {
  if (!seconds) return '永不过期'
  const days = Math.floor(seconds / 86400)
  if (days > 0) return `${days}天`
  const hours = Math.floor(seconds / 3600)
  if (hours > 0) return `${hours}小时`
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '关闭',
      handler: handleClose
    }
  ]
}

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.drawer-form-container {
  .drawer-form-content {
    padding: 24px;
  }
}

.key-management-content {
  .key-section {
    margin-bottom: 40px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .security-alert {
    margin-top: 40px;
  }
  
  .key-item {
    margin-bottom: 48px;
    padding: 20px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f0f2f5;
    }

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .key-label {
      font-weight: 500;
      color: #303133;
      margin-bottom: 16px;
      font-size: 15px;
    }

    .key-value-container {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .key-value {
      width: 400px;
      padding: 12px 16px;
      background: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-family: monospace;
      font-size: 14px;
      color: #606266;
      word-break: break-all;

      &.client-id {
        font-weight: 500;
      }

      &.client-secret {
        min-height: 20px;

        &.show-secret {
          color: #303133;
        }
      }
    }
    
    .copy-btn, .toggle-btn {
      padding: 8px 12px !important;
      
      .el-icon {
        margin-right: 4px;
      }
    }
    
    .key-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 3px solid #e4e7ed;
    }
  }
}

.security-tips {
  p {
    margin: 4px 0;
    line-height: 1.5;
  }
}
</style>
