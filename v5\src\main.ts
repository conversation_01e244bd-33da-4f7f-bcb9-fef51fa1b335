import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import '@/assets/styles/base.scss';
//import { initToolbar } from '@stagewise/toolbar';

const app = createApp(App);
app.use(createPinia());
app.use(router);

// 配置Element Plus全局选项，设置z-index基础值
app.use(ElementPlus, {
  zIndex: 15000, // 设置Element Plus组件的基础z-index
});

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

declare const process: {
  env: {
    NODE_ENV: string;
  };
};

if (process.env.NODE_ENV === 'development') {
  //initToolbar({ plugins: [] }); // 如需自定义插件可在数组中添加
}

app.mount('#app');


