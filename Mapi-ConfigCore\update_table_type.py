#!/usr/bin/env python3
"""
手动更新接口配置5的table_type为view
"""

import sqlite3
import os

def update_table_type():
    """更新table_type字段"""
    db_path = r"D:\others\Mapi\Mapi-Data\mapi_config.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看当前值
        cursor.execute("SELECT id, name, table_name, table_type FROM interface_configs WHERE id = 5")
        result = cursor.fetchone()
        
        if result:
            print(f"更新前 - ID: {result[0]}, Name: {result[1]}, Table: {result[2]}, Type: {result[3]}")
            
            # 更新table_type为view
            cursor.execute("UPDATE interface_configs SET table_type = 'view' WHERE id = 5")
            conn.commit()
            
            # 验证更新
            cursor.execute("SELECT id, name, table_name, table_type FROM interface_configs WHERE id = 5")
            result = cursor.fetchone()
            print(f"更新后 - ID: {result[0]}, Name: {result[1]}, Table: {result[2]}, Type: {result[3]}")
            
            print("✅ table_type已更新为view")
        else:
            print("❌ 未找到接口配置5")
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    update_table_type()
