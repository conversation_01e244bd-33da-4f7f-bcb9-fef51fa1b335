<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Document /></el-icon>
        <span style="margin-left: 10px;">运维与日志中心</span>
      </div>
      <div class="header-actions">
        <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px">
          <el-option label="全部" value="all" />
          <el-option label="错误" value="error" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
        </el-select>
        <el-button @click="exportLogs">导出日志</el-button>
        <el-button @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="page-tabs">
      <el-tab-pane label="系统日志" name="logs">
        <!-- 日志概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>今日日志总数</h4>
            <div class="card-value">{{ logOverview.todayTotal }}</div>
            <div class="card-trend success">↑ 15%</div>
          </div>
          <div class="overview-card">
            <h4>错误日志</h4>
            <div class="card-value error">{{ logOverview.errorLogs }}</div>
            <div class="card-trend warning">↑ 8%</div>
          </div>
          <div class="overview-card">
            <h4>警告日志</h4>
            <div class="card-value warning">{{ logOverview.warningLogs }}</div>
            <div class="card-trend success">↓ 5%</div>
          </div>
          <div class="overview-card">
            <h4>日志存储</h4>
            <div class="card-value">{{ logOverview.storageUsed }}GB</div>
            <div class="card-trend warning">↑ 12%</div>
          </div>
        </div>

        <!-- 日志级别分布 -->
        <div class="section">
          <h3 class="section-title">日志级别分布（最近24小时）</h3>
          <div class="log-distribution">
            <div v-for="item in logDistribution" :key="item.level" class="distribution-item">
              <div class="distribution-header">
                <span class="level-name" :class="item.level">{{ item.name }}</span>
                <span class="level-count">{{ item.count }}</span>
              </div>
              <div class="distribution-bar">
                <div class="bar-fill" :class="item.level" :style="{ width: `${item.percentage}%` }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新日志 -->
        <div class="section">
          <h3 class="section-title">最新系统日志</h3>
          <el-table :data="recentLogs" style="width: 100%" max-height="400">
            <!-- 空状态 -->
            <template #empty>
              <el-empty
                description="暂无系统日志"
                :image-size="120"
              >
                <template #description>
                  <p>当前没有系统日志记录</p>
                  <p>系统运行后会自动产生日志</p>
                </template>
              </el-empty>
            </template>
            <el-table-column prop="time" label="时间" width="160" />
            <el-table-column prop="level" label="级别" width="80">
              <template #default="{ row }">
                <el-tag :type="getLogLevelType(row.level)" size="small">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="module" label="模块" width="120" />
            <el-table-column prop="message" label="日志内容" min-width="300" />
            <el-table-column prop="source" label="来源" width="120" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button size="small" @click="viewLogDetail(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="任务监控" name="tasks">
        <!-- 任务概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>总任务数</h4>
            <div class="card-value">{{ taskOverview.totalTasks }}</div>
          </div>
          <div class="overview-card">
            <h4>运行中任务</h4>
            <div class="card-value success">{{ taskOverview.runningTasks }}</div>
          </div>
          <div class="overview-card">
            <h4>失败任务</h4>
            <div class="card-value error">{{ taskOverview.failedTasks }}</div>
          </div>
          <div class="overview-card">
            <h4>队列中任务</h4>
            <div class="card-value warning">{{ taskOverview.queuedTasks }}</div>
          </div>
        </div>

        <!-- 任务执行状态 -->
        <div class="section">
          <h3 class="section-title">任务执行状态</h3>
          <el-table :data="taskStatus" style="width: 100%">
            <el-table-column prop="name" label="任务名称" width="200" />
            <el-table-column prop="type" label="任务类型" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusType(row.status)" size="small">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="{ row }">
                <el-progress :percentage="row.progress" :status="getProgressStatus(row.progress)" />
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="开始时间" width="160" />
            <el-table-column prop="duration" label="执行时长" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="viewTaskDetail(row)">详情</el-button>
                <el-button size="small" type="danger" @click="stopTask(row)" v-if="row.status === '运行中'">停止</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="系统维护" name="maintenance">
        <!-- 维护概览 -->
        <div class="overview-cards">
          <div class="overview-card">
            <h4>系统运行时间</h4>
            <div class="card-value">{{ maintenanceOverview.uptime }}</div>
          </div>
          <div class="overview-card">
            <h4>最后重启</h4>
            <div class="card-value">{{ maintenanceOverview.lastRestart }}</div>
          </div>
          <div class="overview-card">
            <h4>备份状态</h4>
            <div class="card-value success">{{ maintenanceOverview.backupStatus }}</div>
          </div>
          <div class="overview-card">
            <h4>磁盘清理</h4>
            <div class="card-value">{{ maintenanceOverview.diskCleanup }}</div>
          </div>
        </div>

        <!-- 维护操作 -->
        <div class="section">
          <h3 class="section-title">系统维护操作</h3>
          <div class="maintenance-actions">
            <div class="action-group">
              <h4>系统控制</h4>
              <div class="action-buttons">
                <el-button type="warning" @click="restartSystem">重启系统</el-button>
                <el-button type="danger" @click="shutdownSystem">关闭系统</el-button>
                <el-button @click="enterMaintenanceMode">维护模式</el-button>
              </div>
            </div>
            
            <div class="action-group">
              <h4>缓存管理</h4>
              <div class="action-buttons">
                <el-button @click="clearCache">清理缓存</el-button>
                <el-button @click="refreshCache">刷新缓存</el-button>
                <el-button @click="viewCacheStatus">缓存状态</el-button>
              </div>
            </div>
            
            <div class="action-group">
              <h4>数据管理</h4>
              <div class="action-buttons">
                <el-button type="primary" @click="backupData">数据备份</el-button>
                <el-button @click="cleanupLogs">日志清理</el-button>
                <el-button @click="optimizeDatabase">数据库优化</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 维护记录 -->
        <div class="section">
          <h3 class="section-title">维护操作记录</h3>
          <el-table :data="maintenanceRecords" style="width: 100%">
            <el-table-column prop="time" label="操作时间" width="160" />
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="operator" label="操作人员" width="120" />
            <el-table-column prop="description" label="操作描述" min-width="200" />
            <el-table-column prop="result" label="执行结果" width="100">
              <template #default="{ row }">
                <el-tag :type="row.result === '成功' ? 'success' : 'danger'" size="small">{{ row.result }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="耗时" width="100" />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Document } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const activeTab = ref('logs')
const logLevel = ref('all')

// 日志概览数据
const logOverview = ref({
  todayTotal: 15420,
  errorLogs: 234,
  warningLogs: 567,
  storageUsed: 2.3
})

const logDistribution = ref([
  { level: 'error', name: '错误', count: 234, percentage: 15 },
  { level: 'warning', name: '警告', count: 567, percentage: 35 },
  { level: 'info', name: '信息', count: 1245, percentage: 80 },
  { level: 'debug', name: '调试', count: 890, percentage: 60 }
])

const recentLogs = ref([
  { time: '2024-01-10 14:25:30', level: 'ERROR', module: '数据源', message: 'MySQL连接超时', source: 'datasource-service' },
  { time: '2024-01-10 14:24:15', level: 'WARN', module: 'API', message: '接口响应时间过长', source: 'api-gateway' },
  { time: '2024-01-10 14:23:45', level: 'INFO', module: '用户', message: '用户登录成功', source: 'auth-service' },
  { time: '2024-01-10 14:22:30', level: 'ERROR', module: '任务', message: '定时任务执行失败', source: 'task-scheduler' },
  { time: '2024-01-10 14:21:15', level: 'INFO', module: '系统', message: '系统启动完成', source: 'system-core' }
])

// 任务监控数据
const taskOverview = ref({
  totalTasks: 45,
  runningTasks: 12,
  failedTasks: 3,
  queuedTasks: 8
})

const taskStatus = ref([
  { name: '数据同步任务', type: '定时任务', status: '运行中', progress: 75, startTime: '2024-01-10 14:00:00', duration: '25分钟' },
  { name: '报表生成任务', type: '手动任务', status: '已完成', progress: 100, startTime: '2024-01-10 13:30:00', duration: '45分钟' },
  { name: '数据备份任务', type: '定时任务', status: '队列中', progress: 0, startTime: '-', duration: '-' },
  { name: '日志清理任务', type: '定时任务', status: '失败', progress: 30, startTime: '2024-01-10 12:00:00', duration: '10分钟' }
])

// 系统维护数据
const maintenanceOverview = ref({
  uptime: '15天3小时',
  lastRestart: '15天前',
  backupStatus: '正常',
  diskCleanup: '3天前'
})

const maintenanceRecords = ref([
  { time: '2024-01-10 02:00:00', operation: '数据备份', operator: 'system', description: '自动数据备份', result: '成功', duration: '25分钟' },
  { time: '2024-01-09 03:00:00', operation: '日志清理', operator: 'admin', description: '清理7天前的日志文件', result: '成功', duration: '5分钟' },
  { time: '2024-01-08 01:00:00', operation: '缓存清理', operator: 'system', description: '自动清理过期缓存', result: '成功', duration: '2分钟' },
  { time: '2024-01-07 04:00:00', operation: '数据库优化', operator: 'admin', description: '优化数据库索引', result: '失败', duration: '15分钟' }
])

// 方法
const getLogLevelType = (level: string) => {
  const types: Record<string, string> = {
    'ERROR': 'danger',
    'WARN': 'warning',
    'INFO': 'success',
    'DEBUG': 'info'
  }
  return types[level] || 'info'
}

const getTaskStatusType = (status: string) => {
  const types: Record<string, string> = {
    '运行中': 'primary',
    '已完成': 'success',
    '失败': 'danger',
    '队列中': 'warning'
  }
  return types[status] || 'info'
}

const getProgressStatus = (progress: number) => {
  if (progress === 100) return 'success'
  if (progress > 0) return undefined
  return 'exception'
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
}

const exportLogs = () => {
  ElMessage.info('日志导出功能开发中')
}

const viewLogDetail = (log: any) => {
  ElMessage.info(`查看日志详情: ${log.message}`)
}

const viewTaskDetail = (task: any) => {
  ElMessage.info(`查看任务详情: ${task.name}`)
}

const stopTask = (task: any) => {
  ElMessageBox.confirm(`确定要停止任务 "${task.name}" 吗？`, '确认操作', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('任务已停止')
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

// 维护操作方法
const restartSystem = () => {
  ElMessageBox.confirm('确定要重启系统吗？这将中断所有正在进行的操作。', '确认重启', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('系统重启指令已发送')
  }).catch(() => {
    ElMessage.info('已取消重启')
  })
}

const shutdownSystem = () => {
  ElMessageBox.confirm('确定要关闭系统吗？这将停止所有服务。', '确认关闭', {
    type: 'error'
  }).then(() => {
    ElMessage.success('系统关闭指令已发送')
  }).catch(() => {
    ElMessage.info('已取消关闭')
  })
}

const enterMaintenanceMode = () => {
  ElMessage.info('进入维护模式')
}

const clearCache = () => {
  ElMessage.success('缓存清理完成')
}

const refreshCache = () => {
  ElMessage.success('缓存刷新完成')
}

const viewCacheStatus = () => {
  ElMessage.info('查看缓存状态')
}

const backupData = () => {
  ElMessage.success('数据备份任务已启动')
}

const cleanupLogs = () => {
  ElMessage.success('日志清理任务已启动')
}

const optimizeDatabase = () => {
  ElMessage.success('数据库优化任务已启动')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
/* 运维日志页面特有样式 */

/* 调整页面间距 - 减少空白 */
.page-header {
  margin-bottom: 12px !important; /* 覆盖公共样式的25px */
}

.page-tabs {
  margin-top: 5px !important; /* 覆盖公共样式的10px */
}

/* 重复的页签和表格样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 重复的按钮和滚动条样式已移除 - 使用 page-common.scss 中的通用样式 */

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.overview-card h4 {
  margin: 0 0 10px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;
}

.card-value.success {
  color: #10b981;
}

.card-value.warning {
  color: #f59e0b;
}

.card-value.error {
  color: #ef4444;
}

.card-trend {
  font-size: 12px;
  font-weight: 500;
}

.card-trend.success {
  color: #10b981;
}

.card-trend.warning {
  color: #f59e0b;
}

.card-trend.error {
  color: #ef4444;
}

/* 区域样式 */
.section {
  margin-bottom: 30px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #374151;
  font-weight: 600;
}

/* 日志分布图 */
.log-distribution {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.distribution-item {
  margin-bottom: 15px;
}

.distribution-item:last-child {
  margin-bottom: 0;
}

.distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.level-name {
  font-weight: 500;
  font-size: 14px;
}

.level-name.error {
  color: #ef4444;
}

.level-name.warning {
  color: #f59e0b;
}

.level-name.info {
  color: #3b82f6;
}

.level-name.debug {
  color: #6b7280;
}

.level-count {
  font-weight: 600;
  color: #374151;
}

.distribution-bar {
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.bar-fill.error {
  background: #ef4444;
}

.bar-fill.warning {
  background: #f59e0b;
}

.bar-fill.info {
  background: #3b82f6;
}

.bar-fill.debug {
  background: #6b7280;
}

/* 维护操作区域 */
.maintenance-actions {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.action-group {
  margin-bottom: 25px;
}

.action-group:last-child {
  margin-bottom: 0;
}

.action-group h4 {
  margin: 0 0 15px 0;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
}
</style>
