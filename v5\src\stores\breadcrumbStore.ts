import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { BreadcrumbItem, MenuData, SubMenu } from '@/types/navigation';

// 面包屑导航状态管理Store
export const useBreadcrumbStore = defineStore('breadcrumb', () => {
  // 面包屑导航数据
  const breadcrumbs = ref<BreadcrumbItem[]>([]);

  // 更新面包屑导航
  const updateBreadcrumbs = (menuKey: string, menuData: MenuData | null) => {
    // 添加调试日志
    if (!menuData || !menuData.topLevelMenus) {
      console.log('menuData无效或缺少topLevelMenus');
      breadcrumbs.value = [];
      return;
    }

    const breadcrumbItems: BreadcrumbItem[] = [];
    let found = false;

    // 递归查找菜单项并构建面包屑
    const findMenuItem = (menus: SubMenu[], currentPath: BreadcrumbItem[]) => {
      if (found) return;
      for (const menu of menus) {
        // SubMenu类型没有path属性，移除该字段
        const newPath = [...currentPath, { label: menu.title, title: menu.title }];
        if (menu.name === menuKey) {
          breadcrumbItems.push(...newPath);
          found = true;
          // console.log('找到匹配项，面包屑:', breadcrumbItems);
          return;
        }
      }
    };

    // 遍历所有顶级菜单
    for (const topMenu of menuData.topLevelMenus) {
      if (found) break;
      const topLevelPath: BreadcrumbItem = { label: topMenu.title, title: topMenu.title };
      if (topMenu.children && topMenu.children.length > 0) {
        findMenuItem(topMenu.children, [topLevelPath]);
      }
    }

    if (!found) {
      // 保留顶级菜单路径作为回退
      if (menuData.topLevelMenus.length > 0) {
        breadcrumbs.value = [{ label: menuData.topLevelMenus[0].title, title: menuData.topLevelMenus[0].title }];
      } else {
        breadcrumbs.value = [];
      }
    } else {
      breadcrumbs.value = breadcrumbItems;
    }
  };

  return {
    breadcrumbs,
    updateBreadcrumbs
  };
});