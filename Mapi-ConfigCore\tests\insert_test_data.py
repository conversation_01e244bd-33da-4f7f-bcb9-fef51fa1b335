#!/usr/bin/env python3
"""
插入接口管理模块的测试数据
"""

import sys
from pathlib import Path
import requests
import json

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def test_api_connection():
    """测试API连接"""
    try:
        response = requests.get("http://127.0.0.1:8000/health")
        if response.status_code == 200:
            print("✅ API连接正常")
            return True
        else:
            print(f"❌ API连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接异常: {e}")
        return False

def create_test_datasource():
    """创建测试数据源"""
    print("\n🔧 创建测试数据源...")
    
    datasource_data = {
        "name": "办公管理系统",
        "db_type": "sqlserver",
        "host": "*************",
        "port": 1433,
        "database": "master",
        "username": "sa",
        "password": "ABCabc123",
        "description": "办公管理系统数据库",
        "is_enabled": True,
        "connection_timeout": 30,
        "created_by": "system"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/datasource/", json=datasource_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 数据源创建成功: ID={result['id']}")
            return result['id']
        else:
            print(f"❌ 数据源创建失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 数据源创建异常: {e}")
        return None

def create_test_interface_groups():
    """创建测试接口分组"""
    print("\n📁 创建测试接口分组...")
    
    groups = [
        {
            "name": "用户管理",
            "path_prefix": "/user",
            "description": "用户相关的接口分组",
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "订单管理", 
            "path_prefix": "/order",
            "description": "订单相关的接口分组",
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "产品管理",
            "path_prefix": "/product", 
            "description": "产品相关的接口分组",
            "is_enabled": True,
            "created_by": "system"
        }
    ]
    
    group_ids = []
    for group_data in groups:
        try:
            response = requests.post(f"{BASE_URL}/interface/groups/", json=group_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 接口分组创建成功: {result['name']} (ID={result['id']})")
                group_ids.append(result['id'])
            else:
                print(f"❌ 接口分组创建失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ 接口分组创建异常: {e}")
    
    return group_ids

def create_test_interface_tags():
    """创建测试接口标签"""
    print("\n🏷️ 创建测试接口标签...")
    
    tags = [
        {
            "name": "查询",
            "color": "#3FC8DD",
            "description": "查询类接口",
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "新增",
            "color": "#67C23A", 
            "description": "新增类接口",
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "更新",
            "color": "#E6A23C",
            "description": "更新类接口", 
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "删除",
            "color": "#F56C6C",
            "description": "删除类接口",
            "is_enabled": True,
            "created_by": "system"
        },
        {
            "name": "重要",
            "color": "#9C27B0",
            "description": "重要接口标记",
            "is_enabled": True,
            "created_by": "system"
        }
    ]
    
    tag_ids = []
    for tag_data in tags:
        try:
            response = requests.post(f"{BASE_URL}/interface/tags/", json=tag_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 接口标签创建成功: {result['name']} (ID={result['id']})")
                tag_ids.append(result['id'])
            else:
                print(f"❌ 接口标签创建失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ 接口标签创建异常: {e}")
    
    return tag_ids

def create_test_interface_configs(datasource_id, group_ids, tag_ids):
    """创建测试接口配置"""
    print("\n⚙️ 创建测试接口配置...")
    
    if not datasource_id or not group_ids or not tag_ids:
        print("❌ 缺少必要的ID，跳过接口配置创建")
        return []
    
    configs = [
        {
            "name": "获取用户列表",
            "path": "/user/list",
            "method": "GET",
            "description": "分页获取用户列表",
            "group_id": group_ids[0],  # 用户管理分组
            "datasource_id": datasource_id,
            "table_name": "users",
            "is_enabled": True,
            "is_public": False,
            "query_fields": ["name", "email", "status"],
            "required_fields": [],
            "response_fields": ["id", "name", "email", "created_at"],
            "cache_duration": 300,
            "rate_limit": 100,
            "tags": [tag_ids[0]],  # 查询标签
            "created_by": "system"
        },
        {
            "name": "创建用户",
            "path": "/user/create", 
            "method": "POST",
            "description": "创建新用户",
            "group_id": group_ids[0],  # 用户管理分组
            "datasource_id": datasource_id,
            "table_name": "users",
            "is_enabled": True,
            "is_public": False,
            "query_fields": [],
            "required_fields": ["name", "email"],
            "response_fields": ["id", "name", "email", "created_at"],
            "cache_duration": 0,
            "rate_limit": 50,
            "tags": [tag_ids[1], tag_ids[4]],  # 新增、重要标签
            "created_by": "system"
        },
        {
            "name": "获取订单列表",
            "path": "/order/list",
            "method": "GET", 
            "description": "分页获取订单列表",
            "group_id": group_ids[1],  # 订单管理分组
            "datasource_id": datasource_id,
            "table_name": "orders",
            "is_enabled": True,
            "is_public": False,
            "query_fields": ["status", "user_id", "created_at"],
            "required_fields": [],
            "response_fields": ["id", "order_no", "amount", "status", "created_at"],
            "cache_duration": 600,
            "rate_limit": 200,
            "tags": [tag_ids[0]],  # 查询标签
            "created_by": "system"
        }
    ]
    
    config_ids = []
    for config_data in configs:
        try:
            response = requests.post(f"{BASE_URL}/interface/configs/", json=config_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 接口配置创建成功: {result['name']} (ID={result['id']})")
                config_ids.append(result['id'])
            else:
                print(f"❌ 接口配置创建失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ 接口配置创建异常: {e}")
    
    return config_ids

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 接口管理模块测试数据插入")
    print("=" * 60)
    
    # 1. 测试API连接
    if not test_api_connection():
        print("❌ API连接失败，请确保后端服务正在运行")
        return
    
    # 2. 创建测试数据源
    datasource_id = create_test_datasource()
    
    # 3. 创建测试接口分组
    group_ids = create_test_interface_groups()
    
    # 4. 创建测试接口标签
    tag_ids = create_test_interface_tags()
    
    # 5. 创建测试接口配置
    config_ids = create_test_interface_configs(datasource_id, group_ids, tag_ids)
    
    print("\n" + "=" * 60)
    print("📊 测试数据插入完成")
    print(f"   数据源: {1 if datasource_id else 0} 个")
    print(f"   接口分组: {len(group_ids)} 个")
    print(f"   接口标签: {len(tag_ids)} 个")
    print(f"   接口配置: {len(config_ids)} 个")
    print("=" * 60)

if __name__ == "__main__":
    main()
