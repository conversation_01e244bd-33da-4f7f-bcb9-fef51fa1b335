"""
动态控制器层
处理所有HTTP方法的业务逻辑分发
"""

from typing import Dict, Any, Optional
from fastapi import Depends
from sqlalchemy.orm import Session
from app.shared.database import get_database
from app.service.dynamic_service import DynamicService
from app.shared.core.log_util import LogUtil

class DynamicController:
    """动态控制器 - 处理所有HTTP方法的业务逻辑"""
    
    def __init__(self, db: Session = Depends(get_database)):
        self.db = db
        self.service = DynamicService(db)
    
    async def handle_get(self, interface_path: str, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理GET请求 - 查询数据
        
        Args:
            interface_path: 接口路径
            query_params: 查询参数
            
        Returns:
            查询结果
        """
        try:
            LogUtil.info("处理GET请求", interface_path=interface_path, query_params=query_params)
            
            # 参数验证
            if not interface_path:
                return self._error_response("接口路径不能为空")
            
            # 调用服务层处理
            result = await self.service.query_data(interface_path, query_params)
            
            LogUtil.info("GET请求处理完成", interface_path=interface_path, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("GET请求处理失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"查询失败: {str(e)}")
    
    async def handle_post(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理POST请求 - 创建数据
        
        Args:
            interface_path: 接口路径
            data: 创建数据
            query_params: 查询参数
            
        Returns:
            创建结果
        """
        try:
            LogUtil.info("处理POST请求", interface_path=interface_path, data_keys=list(data.keys()))
            
            # 参数验证
            if not interface_path:
                return self._error_response("接口路径不能为空")
            
            if not data:
                return self._error_response("创建数据不能为空")
            
            # 调用服务层处理
            result = await self.service.create_data(interface_path, data, query_params)
            
            LogUtil.info("POST请求处理完成", interface_path=interface_path, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("POST请求处理失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"创建失败: {str(e)}")
    
    async def handle_put(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理PUT请求 - 更新数据
        
        Args:
            interface_path: 接口路径
            data: 更新数据
            query_params: 查询参数（包含更新条件）
            
        Returns:
            更新结果
        """
        try:
            LogUtil.info("处理PUT请求", interface_path=interface_path, data_keys=list(data.keys()))
            
            # 参数验证
            if not interface_path:
                return self._error_response("接口路径不能为空")
            
            if not data:
                return self._error_response("更新数据不能为空")
            
            if not query_params:
                return self._error_response("PUT请求需要提供更新条件")
            
            # 调用服务层处理
            result = await self.service.update_data(interface_path, data, query_params)
            
            LogUtil.info("PUT请求处理完成", interface_path=interface_path, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("PUT请求处理失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"更新失败: {str(e)}")
    
    async def handle_delete(self, interface_path: str, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理DELETE请求 - 删除数据
        
        Args:
            interface_path: 接口路径
            query_params: 查询参数（包含删除条件）
            
        Returns:
            删除结果
        """
        try:
            LogUtil.info("处理DELETE请求", interface_path=interface_path, query_params=query_params)
            
            # 参数验证
            if not interface_path:
                return self._error_response("接口路径不能为空")
            
            if not query_params:
                return self._error_response("DELETE请求需要提供删除条件")
            
            # 调用服务层处理
            result = await self.service.delete_data(interface_path, query_params)
            
            LogUtil.info("DELETE请求处理完成", interface_path=interface_path, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("DELETE请求处理失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"删除失败: {str(e)}")
    
    async def handle_patch(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理PATCH请求 - 部分更新数据
        
        Args:
            interface_path: 接口路径
            data: 部分更新数据
            query_params: 查询参数（包含更新条件）
            
        Returns:
            更新结果
        """
        try:
            LogUtil.info("处理PATCH请求", interface_path=interface_path, data_keys=list(data.keys()))
            
            # 参数验证
            if not interface_path:
                return self._error_response("接口路径不能为空")
            
            if not data:
                return self._error_response("更新数据不能为空")
            
            if not query_params:
                return self._error_response("PATCH请求需要提供更新条件")
            
            # PATCH与PUT的区别：PATCH只更新提供的字段
            result = await self.service.patch_data(interface_path, data, query_params)
            
            LogUtil.info("PATCH请求处理完成", interface_path=interface_path, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("PATCH请求处理失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"部分更新失败: {str(e)}")
    
    async def handle_test(self, interface_config_id: int, test_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理接口测试请求
        
        Args:
            interface_config_id: 接口配置ID
            test_request: 测试请求数据
            
        Returns:
            测试结果
        """
        try:
            LogUtil.info("处理接口测试", interface_config_id=interface_config_id, test_request=test_request)
            
            # 参数验证
            method = test_request.get('method', 'GET').upper()
            path = test_request.get('path', '')
            params = test_request.get('params', {})
            body = test_request.get('body', {})
            
            if not path:
                return self._error_response("测试请求需要提供接口路径")
            
            # 调用服务层处理测试
            result = await self.service.test_interface(interface_config_id, method, path, params, body)
            
            LogUtil.info("接口测试完成", interface_config_id=interface_config_id, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("接口测试失败", error=str(e), interface_config_id=interface_config_id)
            return self._error_response(f"测试失败: {str(e)}")
    
    async def get_schema(self, interface_config_id: int) -> Dict[str, Any]:
        """
        获取接口数据结构
        
        Args:
            interface_config_id: 接口配置ID
            
        Returns:
            接口结构信息
        """
        try:
            LogUtil.info("获取接口结构", interface_config_id=interface_config_id)
            
            # 调用服务层获取结构信息
            result = await self.service.get_interface_schema(interface_config_id)
            
            LogUtil.info("接口结构获取完成", interface_config_id=interface_config_id, success=result.get("success", False))
            return result
            
        except Exception as e:
            LogUtil.error("接口结构获取失败", error=str(e), interface_config_id=interface_config_id)
            return self._error_response(f"获取结构失败: {str(e)}")
    
    def _error_response(self, message: str) -> Dict[str, Any]:
        """生成错误响应"""
        return {
            "success": False,
            "message": message,
            "data": None
        }
