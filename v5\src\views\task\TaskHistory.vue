<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Clock /></el-icon>
      任务历史
    </h2>
    
    <div class="content-container">
      <!-- 查询条件 -->
      <div class="section-title">
        <el-icon><Search /></el-icon>
        历史查询
      </div>
      
      <div class="query-card">
        <el-form :model="queryForm" label-width="100px" inline>
          <el-form-item label="任务名称">
            <el-input v-model="queryForm.taskName" placeholder="输入任务名称" clearable></el-input>
          </el-form-item>
          
          <el-form-item label="任务类型">
            <el-select v-model="queryForm.taskType" placeholder="选择任务类型" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="数据同步" value="sync"></el-option>
              <el-option label="系统清理" value="cleanup"></el-option>
              <el-option label="报表生成" value="report"></el-option>
              <el-option label="数据备份" value="backup"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="执行状态">
            <el-select v-model="queryForm.status" placeholder="选择执行状态" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="成功" value="success"></el-option>
              <el-option label="失败" value="failed"></el-option>
              <el-option label="超时" value="timeout"></el-option>
              <el-option label="取消" value="cancelled"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="执行时间">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss">
            </el-date-picker>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="searchHistory" icon="Search">查询</el-button>
            <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            <el-button @click="exportHistory" icon="Download">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 统计信息 -->
      <div class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        执行统计
      </div>
      
      <div class="stats-card">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number success">{{ historyStats.totalSuccess }}</div>
              <div class="stat-label">成功执行</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number failed">{{ historyStats.totalFailed }}</div>
              <div class="stat-label">执行失败</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ historyStats.avgDuration }}</div>
              <div class="stat-label">平均耗时</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ historyStats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 历史记录列表 -->
      <div class="section-title">
        <el-icon><List /></el-icon>
        执行记录
      </div>
      
      <div class="history-card">
        <el-table :data="historyList" style="width: 100%" v-loading="loading">
          <el-table-column prop="taskName" label="任务名称" width="200"></el-table-column>
          <el-table-column prop="taskType" label="任务类型" width="120">
            <template #default="scope">
              <el-tag size="small" :type="getTypeColor(scope.row.taskType)">{{ scope.row.taskType }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="执行状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180"></el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="180"></el-table-column>
          <el-table-column prop="duration" label="执行时长" width="120"></el-table-column>
          <el-table-column prop="executor" label="执行器" width="120"></el-table-column>
          <el-table-column prop="message" label="执行结果" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
              <el-button size="small" @click="rerunTask(scope.row)" v-if="scope.row.status === 'failed'">重新执行</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 20px; text-align: right;">
        </el-pagination>
      </div>
      
      <!-- 任务详情对话框 -->
      <el-dialog v-model="detailVisible" title="任务执行详情" width="800px">
        <div v-if="currentTask">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务名称">{{ currentTask.taskName }}</el-descriptions-item>
            <el-descriptions-item label="任务类型">
              <el-tag size="small" :type="getTypeColor(currentTask.taskType)">{{ currentTask.taskType }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="执行状态">
              <el-tag :type="getStatusColor(currentTask.status)">{{ getStatusText(currentTask.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="执行器">{{ currentTask.executor }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ currentTask.startTime }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ currentTask.endTime }}</el-descriptions-item>
            <el-descriptions-item label="执行时长">{{ currentTask.duration }}</el-descriptions-item>
            <el-descriptions-item label="重试次数">{{ currentTask.retryCount || 0 }}</el-descriptions-item>
            <el-descriptions-item label="执行结果" span="2">{{ currentTask.message }}</el-descriptions-item>
          </el-descriptions>
          
          <div style="margin-top: 20px;">
            <h4>执行日志</h4>
            <el-input
              v-model="currentTask.logs"
              type="textarea"
              :rows="8"
              readonly
              style="font-family: 'Courier New', monospace; font-size: 12px;">
            </el-input>
          </div>
          
          <div style="margin-top: 20px;" v-if="currentTask.parameters">
            <h4>执行参数</h4>
            <pre class="parameter-display">{{ formatJson(currentTask.parameters) }}</pre>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Clock, Search, DataAnalysis, List, Refresh, Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 加载状态
const loading = ref(false)

// 详情对话框
const detailVisible = ref(false)
const currentTask = ref<any>(null)

// 查询表单
const queryForm = ref({
  taskName: '',
  taskType: '',
  status: '',
  dateRange: []
})

// 分页
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

// 历史统计
const historyStats = ref({
  totalSuccess: 1245,
  totalFailed: 23,
  avgDuration: '2分35秒',
  successRate: 98.2
})

// 历史记录列表
const historyList = ref([
  {
    id: 1,
    taskName: '用户数据同步',
    taskType: '数据同步',
    status: 'success',
    startTime: '2023-12-15 14:30:00',
    endTime: '2023-12-15 14:32:35',
    duration: '2分35秒',
    executor: 'executor-01',
    message: '成功同步1250条用户数据',
    retryCount: 0,
    logs: '2023-12-15 14:30:00 [INFO] 任务开始执行\n2023-12-15 14:30:05 [INFO] 连接数据库成功\n2023-12-15 14:30:10 [INFO] 开始同步用户数据\n2023-12-15 14:32:30 [INFO] 数据同步完成，共处理1250条记录\n2023-12-15 14:32:35 [INFO] 任务执行完成',
    parameters: { source: 'mysql://prod', target: 'mysql://backup', batchSize: 100 }
  },
  {
    id: 2,
    taskName: '系统日志清理',
    taskType: '系统清理',
    status: 'success',
    startTime: '2023-12-15 03:00:00',
    endTime: '2023-12-15 03:01:15',
    duration: '1分15秒',
    executor: 'executor-02',
    message: '成功清理356个日志文件，释放空间2.3GB',
    retryCount: 0,
    logs: '2023-12-15 03:00:00 [INFO] 开始清理系统日志\n2023-12-15 03:00:30 [INFO] 扫描日志目录\n2023-12-15 03:01:00 [INFO] 清理过期日志文件\n2023-12-15 03:01:15 [INFO] 清理完成',
    parameters: { logPath: '/var/log', retentionDays: 30 }
  },
  {
    id: 3,
    taskName: '月度报表生成',
    taskType: '报表生成',
    status: 'failed',
    startTime: '2023-12-15 02:00:00',
    endTime: '2023-12-15 02:05:30',
    duration: '5分30秒',
    executor: 'executor-01',
    message: '数据库连接超时，报表生成失败',
    retryCount: 2,
    logs: '2023-12-15 02:00:00 [INFO] 开始生成月度报表\n2023-12-15 02:00:05 [ERROR] 数据库连接失败\n2023-12-15 02:02:00 [INFO] 重试连接数据库\n2023-12-15 02:02:05 [ERROR] 连接超时\n2023-12-15 02:05:30 [ERROR] 任务执行失败',
    parameters: { reportType: 'monthly', format: 'pdf', email: '<EMAIL>' }
  }
])

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    '数据同步': 'primary',
    '系统清理': 'warning',
    '报表生成': 'success',
    '数据备份': 'info'
  }
  return colors[type] || ''
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    success: 'success',
    failed: 'danger',
    timeout: 'warning',
    cancelled: 'info'
  }
  return colors[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    success: '成功',
    failed: '失败',
    timeout: '超时',
    cancelled: '取消'
  }
  return texts[status] || status
}

// 搜索历史
const searchHistory = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

// 重置查询
const resetQuery = () => {
  queryForm.value = {
    taskName: '',
    taskType: '',
    status: '',
    dateRange: []
  }
  searchHistory()
}

// 导出历史
const exportHistory = () => {
  ElMessage.info('导出功能开发中...')
}

// 查看详情
const viewDetail = (task: any) => {
  currentTask.value = task
  detailVisible.value = true
}

// 重新执行任务
const rerunTask = (task: any) => {
  ElMessageBox.confirm(`确定要重新执行任务 "${task.taskName}" 吗？`, '确认重新执行', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('任务已加入执行队列')
  })
}

// 格式化JSON
const formatJson = (obj: any) => {
  return JSON.stringify(obj, null, 2)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  searchHistory()
}

const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  searchHistory()
}

// 初始化
onMounted(() => {
  pagination.value.total = historyList.value.length
})
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary-color);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary-color);
  padding-left: 12px;
}

.query-card, .stats-card, .history-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.stat-item {
  text-align: center;
  padding: 15px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-number.success { color: #67c23a; }
.stat-number.failed { color: #f56c6c; }

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.parameter-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}
</style>
