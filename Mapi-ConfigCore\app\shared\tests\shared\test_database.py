"""
共享层 - 数据库连接和模型测试
"""

from app.shared.database import check_database_connection, SessionLocal
from app.config.models.data_source_model import DataSourceModel

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    if check_database_connection():
        print("✅ 数据库连接成功")
        return True
    else:
        print("❌ 数据库连接失败")
        return False

def test_data_source_model():
    """测试数据源模型"""
    print("🔍 测试数据源模型...")
    
    try:
        db = SessionLocal()
        
        # 查询数据源数量
        count = db.query(DataSourceModel).count()
        print(f"✅ 数据源表查询成功，共有 {count} 条记录")
        
        # 查询第一条记录
        if count > 0:
            first_ds = db.query(DataSourceModel).first()
            print(f"✅ 第一条记录: ID={first_ds.id}, Name={first_ds.name}, Type={first_ds.db_type}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据源模型测试失败: {e}")
        return False

def run_database_tests():
    """运行所有数据库测试"""
    print("🚀 开始共享层数据库测试...\n")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("数据源模型", test_data_source_model)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    return results
