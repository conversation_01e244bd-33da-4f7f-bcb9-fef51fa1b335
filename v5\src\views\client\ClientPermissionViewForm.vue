<template>
  <div class="drawer-form-container">
    <div class="drawer-form-content custom-scrollbar">
      <!-- 权限概览 -->
      <div class="permission-overview">
        <div class="overview-header">
          <h3>权限概览</h3>
          <el-button type="primary" size="small" @click="handleEditPermissions">
            <el-icon><Edit /></el-icon>
            编辑权限
          </el-button>
        </div>
        <div class="overview-stats">
          <div class="stat-card">
            <div class="stat-number">{{ totalInterfaces }}</div>
            <div class="stat-label">可访问接口</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalGroups }}</div>
            <div class="stat-label">授权分组</div>
          </div>
          <div class="stat-card time-card">
            <div class="stat-label">最后更新</div>
            <div class="stat-time">{{ lastUpdated }}</div>
          </div>
        </div>
      </div>

      <!-- 权限详情 -->
      <div class="permission-details">
        <div class="details-header">
          <h3>权限详情</h3>
          <div class="header-actions">
            <el-button
              size="small"
              type="primary"
              plain
              @click="expandAll"
            >
              全部展开
            </el-button>
            <el-button
              size="small"
              plain
              @click="collapseAll"
            >
              全部折叠
            </el-button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 权限表格 -->
        <div v-else class="permission-table">
          <el-table
            ref="tableRef"
            :data="permissionGroups"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            style="width: 100%"
            :default-expand-all="false"
            :row-class-name="getRowClassName"
          >
            <el-table-column label="分组/接口名称" min-width="200">
              <template #default="{ row }">
                <div class="name-cell" :class="{ 'is-group': row.type === 'group' }">
                  <div class="name-content">
                    <span class="name-text">{{ row.name }}</span>
                    <div v-if="row.type === 'interface' && row.path" class="interface-path-text">
                      {{ row.path }}
                    </div>
                  </div>
                  <el-tag v-if="row.type === 'group'" size="small" type="info" class="group-count-tag">
                    {{ row.children ? row.children.length : 0 }} 个接口
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="描述" min-width="200">
              <template #default="{ row }">
                <span class="description-text">{{ row.description || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="HTTP方法" width="100">
              <template #default="{ row }">
                <el-tag
                  v-if="row.method"
                  :type="getMethodTagType(row.method)"
                  size="small"
                >
                  {{ row.method }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 无权限状态 -->
        <div v-if="!permissionGroups || permissionGroups.length === 0" class="no-permissions">
          <el-empty description="该客户端暂未设置任何权限">
            <el-button type="primary" @click="handleEditPermissions">
              立即设置权限
            </el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Folder } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const loading = ref(false)
const clientData = ref({})
const permissionGroups = ref([])
const tableRef = ref()


// 计算属性
const totalInterfaces = computed(() => {
  if (!permissionGroups.value || !Array.isArray(permissionGroups.value)) {
    return 0
  }
  return permissionGroups.value.reduce((total, group) => {
    return total + (group?.children?.length || 0)
  }, 0)
})

const totalGroups = computed(() => {
  if (!permissionGroups.value || !Array.isArray(permissionGroups.value)) {
    return 0
  }
  return permissionGroups.value.length
})

const lastUpdated = computed(() => {
  return clientData.value?.permissionUpdatedAt || '未设置'
})

// 加载权限数据
const loadPermissionData = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取客户端权限详情
    // 模拟数据 - 根据客户端权限统计生成详情
    const mockGroups = []

    // 确保clientData和permissionGroups存在且为数组
    const permissionGroupsData = clientData.value?.permissionGroups
    if (permissionGroupsData && Array.isArray(permissionGroupsData) && permissionGroupsData.length > 0) {
      permissionGroupsData.forEach((groupName, index) => {
        const interfaces = []
        // 根据分组生成模拟接口
        if (groupName === '用户管理') {
          interfaces.push(
            { id: 'api_1', name: '获取用户列表', path: '/api/users', method: 'GET', description: '获取系统用户列表' },
            { id: 'api_2', name: '创建用户', path: '/api/users', method: 'POST', description: '创建新用户' }
          )
        } else if (groupName === '数据源管理') {
          interfaces.push(
            { id: 'api_3', name: '获取数据源列表', path: '/api/datasources', method: 'GET', description: '获取所有数据源配置' },
            { id: 'api_4', name: '测试数据源连接', path: '/api/datasources/{id}/test', method: 'POST', description: '测试数据源连接' }
          )
        } else if (groupName === '接口管理') {
          interfaces.push(
            { id: 'api_5', name: '获取接口列表', path: '/api/interfaces', method: 'GET', description: '获取系统中所有接口配置' },
            { id: 'api_6', name: '创建接口', path: '/api/interfaces', method: 'POST', description: '创建新的接口配置' }
          )
        } else if (groupName === '系统管理') {
          interfaces.push(
            { id: 'api_7', name: '获取系统配置', path: '/api/system/config', method: 'GET', description: '获取系统配置信息' },
            { id: 'api_8', name: '更新系统配置', path: '/api/system/config', method: 'PUT', description: '更新系统配置' }
          )
        }
        
        if (interfaces.length > 0) {
          // 转换接口数据为表格树结构
          const children = interfaces.map(api => ({
            id: api.id,
            name: api.name,
            path: api.path,
            method: api.method,
            description: api.description,
            type: 'interface'
          }))

          mockGroups.push({
            id: `group_${index + 1}`,
            name: groupName,
            description: `${groupName}相关的接口管理`,
            type: 'group',
            hasChildren: true,
            children: children
          })
        }
      })
    }
    
    permissionGroups.value = mockGroups
  } catch (error) {
    console.error('ClientPermissionViewForm: 加载权限数据失败:', error)
    ElMessage.error('加载权限数据失败')
    // 确保在错误情况下有安全的默认值
    permissionGroups.value = []
  } finally {
    loading.value = false
  }
}



// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  try {
    if (newProps && newProps.clientData) {
      clientData.value = { ...newProps.clientData }
      loadPermissionData()
    } else {
      // 如果没有数据，重置状态
      clientData.value = {}
      permissionGroups.value = []
      loading.value = false
    }
  } catch (error) {
    console.error('ClientPermissionViewForm: watch错误', error)
    // 确保在错误情况下有安全的状态
    clientData.value = {}
    permissionGroups.value = []
    loading.value = false
  }
}, { immediate: true, deep: true })

// 编辑权限
const handleEditPermissions = () => {
  const clientName = clientData.value?.clientName || '未知客户端'
  drawerMessenger.showDrawer({
    title: `权限设置 - ${clientName}`,
    component: 'ClientPermissionSettingForm',
    size: '50%',
    props: {
      clientData: { ...clientData.value }
    }
  })
}

// 关闭抽屉
const handleClose = () => {
  drawerMessenger.hideDrawer()
}

// 展开/折叠控制
const expandAll = () => {
  if (tableRef.value) {
    permissionGroups.value.forEach(group => {
      if (group.type === 'group' && group.children && group.children.length > 0) {
        tableRef.value.toggleRowExpansion(group, true)
      }
    })
  }
}

const collapseAll = () => {
  if (tableRef.value) {
    permissionGroups.value.forEach(group => {
      if (group.type === 'group' && group.children && group.children.length > 0) {
        tableRef.value.toggleRowExpansion(group, false)
      }
    })
  }
}

// 行样式类名
const getRowClassName = ({ row }) => {
  return row.type === 'interface' ? 'interface-row' : 'group-row'
}

// HTTP方法标签类型
const getMethodTagType = (method) => {
  if (!method || typeof method !== 'string') {
    return 'default'
  }
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return typeMap[method.toUpperCase()] || 'default'
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '关闭',
      handler: handleClose
    }
  ]
}

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.permission-overview {
  margin-bottom: 20px;
  
  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .overview-stats {
    display: flex;
    gap: 16px;
    
    .stat-card {
      flex: 1;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 6px;
      text-align: center;
      
      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }

    // 时间卡片特殊样式
    .time-card {
      .stat-label {
        margin-bottom: 8px;
      }

      .stat-time {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        line-height: 1.4;
      }
    }
  }
}

.permission-details {
  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.permission-group {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  
  .group-header {
    padding: 10px 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    cursor: pointer;
    transition: background-color 0.2s;
    min-height: 40px;
    display: flex;
    align-items: center;

    &:hover {
      background: #ecf5ff;
    }

    .group-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .expand-icon {
        color: #909399;
        transition: transform 0.2s;

        &.expanded {
          transform: rotate(90deg);
        }
      }

      .group-icon {
        color: #409eff;
      }

      .group-name {
        font-weight: 500;
        color: #303133;
      }

      .interface-count-tag {
        background-color: #1890ff !important;
        border-color: #1890ff !important;
        color: #ffffff !important;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
      }
    }
  }
  
  .group-interfaces {
    padding: 0;

    .interface-table {
      background-color: #fafbfc;

      .el-table__body tr {
        height: 36px !important;
        background-color: #ffffff;

        &:nth-child(even) {
          background-color: #f8f9fa;
        }

        &:hover {
          background-color: #e6f7ff !important;
        }
      }

      .el-table__body td {
        padding: 6px 6px !important;
        border-bottom: 1px solid #e4e7ed;
        vertical-align: middle;

        &:nth-child(3) {
          padding-right: 4px !important;
        }

        &:last-child {
          padding-right: 12px !important;
        }
      }

      .el-table__body tr:last-child td {
        border-bottom: none;
      }
    }
  }
}

.method-tag {
  min-width: 45px;
  text-align: center;
  font-weight: 600;
  font-size: 11px;
  border: none;
}

.interface-path {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 11px;
  color: #1890ff;
  font-weight: 600;
  background-color: #f0f8ff;
  padding: 2px 6px;
  border-radius: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.interface-name {
  font-weight: 600;
  color: #262626;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.interface-description {
  font-size: 12px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.no-permissions {
  text-align: center;
  padding: 40px 20px;
}

/* 权限表格样式 - 复用权限设置的样式 */
.permission-table {
  flex: 1;
  margin: 0 -20px;
  padding: 0 20px;
  overflow: hidden;

  .el-table {
    height: 100%;

    .el-table__body {
      .el-table__row {
        .el-table__cell {
          &:nth-child(1) {
            /* 第一列（名称列）的单元格 */
            .cell {
              display: flex !important;
              align-items: center !important;
              gap: 4px !important;

              /* 展开箭头 */
              .el-table__expand-icon {
                margin-right: 4px !important;
                margin-left: 0 !important;
                display: inline-flex !important;
                align-items: center !important;
                vertical-align: middle !important;
              }

              /* 树形缩进 */
              .el-table__indent {
                display: inline-block !important;
                vertical-align: middle !important;
              }

              /* 名称内容 */
              .name-cell {
                display: inline-flex !important;
                align-items: center !important;
                vertical-align: middle !important;
                flex: 1 !important;
              }
            }
          }
        }
      }
    }
  }
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .name-content {
    flex: 1;

    .name-text {
      font-weight: 500;
      color: #303133;
      display: block;
      line-height: 1.2;
    }

    .interface-path-text {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 11px;
      color: #1890ff;
      font-weight: 600;
      background-color: #f0f8ff;
      padding: 2px 6px;
      border-radius: 3px;
      margin-top: 3px;
      line-height: 1.2;
      display: inline-block;
    }
  }

  &.is-group {
    .name-content .name-text {
      font-weight: 600;
      color: #1890ff;
    }

    .group-count-tag {
      background-color: #f0f8ff !important;
      border-color: #d6e4ff !important;
      color: #1890ff !important;
      font-size: 11px;
    }
  }
}

.description-text {
  color: #606266;
  font-size: 13px;
}
</style>
