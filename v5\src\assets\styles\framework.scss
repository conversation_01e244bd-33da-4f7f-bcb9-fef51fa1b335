/* 框架组件公共样式 - 使用mixins和CSS变量 */

/* CSS变量定义 - 多个组件共用 */
:root {
  --header-height: 60px;
  --primary-color: #3FC8DD;
  --primary-hover: #35b3c7;
  --sidebar-bg: #2D313D;
  --border-color: #e4e7ed;
  --text-primary: #303133;
  --text-secondary: #606266;
  --bg-light: #f5f7fa;
}

/* 通用mixins - 多个组件使用 */
@mixin framework-transition-base {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin framework-transition-width {
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin framework-transition-opacity {
  transition: visibility 0.2s ease, opacity 0.2s ease, transform 0.2s ease;
}

@mixin framework-flex-center {
  display: flex;
  align-items: center;
}

@mixin framework-flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin framework-flex-column {
  display: flex;
  flex-direction: column;
}

@mixin framework-icon-size-base {
  font-size: 18px;
}

@mixin framework-icon-size-large {
  font-size: 22px;
}

@mixin framework-text-size-base {
  font-size: 14px;
}

@mixin framework-header-height {
  height: var(--header-height);
}

@mixin framework-border-bottom {
  border-bottom: 1px solid var(--border-color);
}

@mixin framework-custom-scrollbar {
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

@mixin framework-text-primary {
  color: var(--text-primary);
}

@mixin framework-text-secondary {
  color: var(--text-secondary);
}

@mixin framework-text-white {
  color: #fff;
}
