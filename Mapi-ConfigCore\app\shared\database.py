"""
数据库连接配置
SQLite数据库连接管理
"""

import os
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# 数据库文件路径（绝对路径更可靠）
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
db_path = os.path.join(project_root, "mapi-data", "mapi_config.db")
DATABASE_URL = f"sqlite:///{db_path}"

# 创建SQLAlchemy引擎
engine = create_engine(
    DATABASE_URL,
    connect_args={
        "check_same_thread": False,  # SQLite特有配置
        "timeout": 30  # 连接超时30秒
    },
    poolclass=StaticPool,  # SQLite使用静态连接池
    echo=False  # 是否打印SQL语句，开发时可设为True
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据对象
metadata = MetaData()

def get_database():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """初始化数据库表"""
    # 确保mapi-data目录存在（数据库文件已经在正确位置）
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        print(f"⚠️  数据库目录不存在: {db_dir}")
        print("请先运行数据库初始化脚本: python app/shared/utils/init_database.py")
        return

    # 导入所有模型以确保它们被注册到Base.metadata中
    try:
        # 数据源模块模型
        from app.config.datasource.models.data_source_model import DataSourceModel

        # 接口管理模块模型
        from app.config.interface.models.interface_group_model import InterfaceGroupModel
        from app.config.interface.models.interface_tag_model import InterfaceTagModel
        from app.config.interface.models.interface_config_model import InterfaceConfigModel
        from app.config.interface.models.interface_config_tag_model import InterfaceConfigTagModel

        print("✅ 所有模型导入成功")
    except ImportError as e:
        print(f"⚠️  模型导入失败: {e}")

    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表已初始化")

def check_database_connection():
    """检查数据库连接"""
    try:
        db = SessionLocal()
        # 执行简单查询测试连接
        db.execute(text("SELECT 1"))
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def cleanup_database():
    """清理数据库连接"""
    try:
        # 关闭所有连接
        engine.dispose()
        print("✅ 数据库连接已清理")
    except Exception as e:
        print(f"⚠️ 数据库连接清理失败: {e}")
