#!/usr/bin/env python3
"""
测试接口管理模块的API
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def test_interface_groups_api():
    """测试接口分组API"""
    print("📁 测试接口分组API...")
    
    # 测试获取列表
    try:
        response = requests.get(f"{BASE_URL}/interface/groups/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取接口分组列表成功: {data['total']} 个分组")
            if data['items']:
                group_id = data['items'][0]['id']
                
                # 测试获取单个分组
                response = requests.get(f"{BASE_URL}/interface/groups/{group_id}")
                if response.status_code == 200:
                    group = response.json()
                    print(f"✅ 获取单个接口分组成功: {group['name']}")
                else:
                    print(f"❌ 获取单个接口分组失败: {response.status_code}")
        else:
            print(f"❌ 获取接口分组列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 接口分组API测试异常: {e}")

def test_interface_tags_api():
    """测试接口标签API"""
    print("\n🏷️ 测试接口标签API...")
    
    # 测试获取列表
    try:
        response = requests.get(f"{BASE_URL}/interface/tags/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取接口标签列表成功: {data['total']} 个标签")
            
            # 测试获取启用的标签
            response = requests.get(f"{BASE_URL}/interface/tags/enabled")
            if response.status_code == 200:
                enabled_tags = response.json()
                print(f"✅ 获取启用标签成功: {len(enabled_tags)} 个启用标签")
            else:
                print(f"❌ 获取启用标签失败: {response.status_code}")
                
            if data['items']:
                tag_id = data['items'][0]['id']
                
                # 测试获取单个标签
                response = requests.get(f"{BASE_URL}/interface/tags/{tag_id}")
                if response.status_code == 200:
                    tag = response.json()
                    print(f"✅ 获取单个接口标签成功: {tag['name']}")
                else:
                    print(f"❌ 获取单个接口标签失败: {response.status_code}")
        else:
            print(f"❌ 获取接口标签列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 接口标签API测试异常: {e}")

def test_interface_configs_api():
    """测试接口配置API"""
    print("\n⚙️ 测试接口配置API...")
    
    # 测试获取列表
    try:
        response = requests.get(f"{BASE_URL}/interface/configs/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取接口配置列表成功: {data['total']} 个配置")
            
            if data['items']:
                config_id = data['items'][0]['id']
                
                # 测试获取单个配置
                response = requests.get(f"{BASE_URL}/interface/configs/{config_id}")
                if response.status_code == 200:
                    config = response.json()
                    print(f"✅ 获取单个接口配置成功: {config['name']}")
                    print(f"   路径: {config['path']}")
                    print(f"   方法: {config['method']}")
                    print(f"   分组: {config.get('groupName', 'N/A')}")
                    print(f"   数据源: {config.get('datasourceName', 'N/A')}")
                    print(f"   标签: {config.get('tagNames', [])}")
                else:
                    print(f"❌ 获取单个接口配置失败: {response.status_code}")
        else:
            print(f"❌ 获取接口配置列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 接口配置API测试异常: {e}")

def test_interface_configs_filters():
    """测试接口配置过滤功能"""
    print("\n🔍 测试接口配置过滤功能...")
    
    try:
        # 测试按分组过滤
        response = requests.get(f"{BASE_URL}/interface/configs/?group_id=1")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 按分组过滤成功: {data['total']} 个配置")
        else:
            print(f"❌ 按分组过滤失败: {response.status_code}")
        
        # 测试按方法过滤
        response = requests.get(f"{BASE_URL}/interface/configs/?method=GET")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 按方法过滤成功: {data['total']} 个GET接口")
        else:
            print(f"❌ 按方法过滤失败: {response.status_code}")
        
        # 测试搜索
        response = requests.get(f"{BASE_URL}/interface/configs/?search=用户")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 搜索功能成功: 找到 {data['total']} 个包含'用户'的接口")
        else:
            print(f"❌ 搜索功能失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 接口配置过滤测试异常: {e}")

def test_data_source_connection():
    """测试数据源连接"""
    print("\n🔗 测试数据源连接...")
    
    try:
        # 获取办公管理系统数据源
        response = requests.get(f"{BASE_URL}/datasource/")
        if response.status_code == 200:
            datasources = response.json()['items']
            office_ds = None
            for ds in datasources:
                if "办公" in ds['name']:
                    office_ds = ds
                    break
            
            if office_ds:
                # 测试连接
                response = requests.get(f"{BASE_URL}/datasource/{office_ds['id']}/test-connection")
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 数据源连接测试成功: {result['message']}")
                else:
                    print(f"❌ 数据源连接测试失败: {response.status_code}")
            else:
                print("❌ 未找到办公管理系统数据源")
        else:
            print(f"❌ 获取数据源失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 数据源连接测试异常: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 接口管理模块API测试")
    print("=" * 60)
    
    # 测试各个API
    test_interface_groups_api()
    test_interface_tags_api()
    test_interface_configs_api()
    test_interface_configs_filters()
    test_data_source_connection()
    
    print("\n" + "=" * 60)
    print("🎉 API测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
