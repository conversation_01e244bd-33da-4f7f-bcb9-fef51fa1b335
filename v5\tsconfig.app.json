{
  "moduleResolution": "Node",
  "resolveJsonModule": true,
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
      "types": ["vue"],
      "esModuleInterop": true,
      "allowSyntheticDefaultImports": true,
    "baseUrl": ".", // 设置baseUrl为项目根目录
      // "baseUrl": "src", //基础目录，路径映射基于此目录解析
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* 路径映射 */
    "paths": {
        "@/types/*": ["src/types/*"],
        "@/*": ["src/*"]
    }
  },
  "include": [
    "src/views/**/*.vue",
    "src/components/**/*.vue",
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "src/views/**/index.vue"
  ]
}
