:root {
  --primary-color: #3FC8DD;
  --bg-light: #f5f7fa;
  --header-light: #f8f9fa;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", sans-serif;
}

html, body, #app {
  height: 100%;
}

/* 隐藏最外层滚动条 */
html, body {
  overflow: hidden;
}

#app {
  overflow: hidden;
}

/* 基础样式 - 不包含业务页面样式 */

/* Element Plus 全局z-index修复 - 确保下拉选择在抽屉中正常显示 */
.el-select-dropdown,
.el-popper,
.el-select-dropdown__wrap,
.el-tooltip__popper,
.el-dropdown-menu {
  z-index: 25000 !important;
}

/* 确保抽屉内的所有弹出层都有足够高的z-index */
.el-drawer .el-select-dropdown,
.el-drawer .el-popper,
.el-drawer .el-select-dropdown__wrap,
.el-drawer .el-tooltip__popper,
.el-drawer .el-dropdown-menu {
  z-index: 25000 !important;
}

/* 高z-index的popper类 - 专门用于抽屉内的下拉选择 */
.high-z-index-popper {
  z-index: 25000 !important;
}

/* 全局覆盖Element Plus抽屉标题的默认margin-bottom */
.el-drawer__header {
  margin-bottom: 0 !important;
}

.high-z-index-popper .el-select-dropdown,
.high-z-index-popper .el-select-dropdown__wrap {
  z-index: 25000 !important;
}
