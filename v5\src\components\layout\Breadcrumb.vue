<!-- 面包屑组件，用于展示页面当前路径导航 -->
<template>
  <div class="breadcrumb-container">
    <span v-for="(item, index) in breadcrumbs" :key="index" class="breadcrumb-item">
      {{ item.label || '' }}
      <span v-if="index !== breadcrumbs.length - 1" class="separator">/</span>
    </span>
  </div>
</template>

<script setup lang="ts">
import { useBreadcrumbStore } from '@/stores/breadcrumbStore';
import { useTabContainerStore } from '@/stores/tabContainerStore';
import { useSidebarMenuStore } from '@/stores/sidebarMenuStore';
import { computed, watch } from 'vue';

const breadcrumbStore = useBreadcrumbStore();
const tabStore = useTabContainerStore();
const sidebarStore = useSidebarMenuStore();

// 使用computed属性确保响应性
const breadcrumbs = computed(() => breadcrumbStore.breadcrumbs);

// 监听页签激活状态变化，更新面包屑
watch(
  () => tabStore.modelActiveName,
  (activeName) => {
    if (activeName) {
      breadcrumbStore.updateBreadcrumbs(activeName, sidebarStore.menuData);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
@use '@/assets/styles/framework.scss' as *;

/* Breadcrumb.vue 特有样式 */
.breadcrumb-container {
  @include framework-flex-center;
  @include framework-text-size-base;
  color: #333; /* 面包屑特有的文本颜色 */
  margin-left: 12px;
  padding: 8px 12px;
  min-height: 36px;
}

.breadcrumb-item {
  margin-right: 4px;
  cursor: pointer;
}

.separator {
  margin: 0 4px;
  @include framework-text-secondary;
}
</style>