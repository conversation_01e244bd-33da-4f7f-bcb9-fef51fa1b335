"""
动态路由层
处理所有动态API的路由分发
支持GET/POST/PUT/DELETE等所有HTTP方法
"""

from fastapi import APIRouter, Depends, Query, Body, Path, Request
from typing import Dict, Any, Optional
from app.service.dynamic_controller import DynamicController

# 创建路由器
router = APIRouter()

@router.get("/api/dynamic/{interface_path:path}", summary="动态API - GET请求")
async def dynamic_get(
    request: Request,
    interface_path: str = Path(..., description="接口路径"),
    controller: DynamicController = Depends()
):
    """
    动态GET请求处理

    根据接口配置的路径直接访问，如：
    - GET /api/dynamic/v1/project/ht1
    - GET /api/dynamic/users
    - GET /api/dynamic/reports/sales

    支持查询参数：
    - page: 页码
    - size: 每页大小
    - sort: 排序字段
    - order: 排序方向(asc/desc)
    - 其他字段: 根据ORM配置支持模糊查询、精确查询、范围查询
    """
    # 构建完整路径（去掉dynamic前缀）
    full_path = f"/api/{interface_path}"
    query_params = dict(request.query_params)
    return await controller.handle_get(full_path, query_params)

@router.post("/api/dynamic/{interface_path:path}", summary="动态API - POST请求")
async def dynamic_post(
    request: Request,
    interface_path: str = Path(..., description="接口路径"),
    data: Dict[str, Any] = Body(..., description="创建数据"),
    controller: DynamicController = Depends()
):
    """
    动态POST请求处理 - 创建数据

    请求体应包含要创建的字段数据
    系统会根据ORM配置验证字段有效性
    """
    full_path = f"/api/{interface_path}"
    query_params = dict(request.query_params)
    return await controller.handle_post(full_path, data, query_params)

@router.put("/api/dynamic/{interface_path:path}", summary="动态API - PUT请求")
async def dynamic_put(
    request: Request,
    interface_path: str = Path(..., description="接口路径"),
    data: Dict[str, Any] = Body(..., description="更新数据"),
    controller: DynamicController = Depends()
):
    """
    动态PUT请求处理 - 更新数据

    需要在查询参数中提供更新条件（如id=123）
    请求体包含要更新的字段数据
    """
    full_path = f"/api/{interface_path}"
    query_params = dict(request.query_params)
    return await controller.handle_put(full_path, data, query_params)

@router.delete("/api/dynamic/{interface_path:path}", summary="动态API - DELETE请求")
async def dynamic_delete(
    request: Request,
    interface_path: str = Path(..., description="接口路径"),
    controller: DynamicController = Depends()
):
    """
    动态DELETE请求处理 - 删除数据

    需要在查询参数中提供删除条件（如id=123）
    """
    full_path = f"/api/{interface_path}"
    query_params = dict(request.query_params)
    return await controller.handle_delete(full_path, query_params)

@router.api_route("/api/dynamic/{interface_path:path}", methods=["PATCH"], summary="动态API - PATCH请求")
async def dynamic_patch(
    request: Request,
    interface_path: str = Path(..., description="接口路径"),
    data: Dict[str, Any] = Body(..., description="部分更新数据"),
    controller: DynamicController = Depends()
):
    """
    动态PATCH请求处理 - 部分更新数据

    与PUT类似，但只更新提供的字段
    """
    full_path = f"/api/{interface_path}"
    query_params = dict(request.query_params)
    return await controller.handle_patch(full_path, data, query_params)

# 测试和调试接口
@router.post("/test/{interface_config_id}", summary="接口测试")
async def test_interface(
    interface_config_id: int = Path(..., description="接口配置ID"),
    test_request: Dict[str, Any] = Body(..., description="测试请求"),
    controller: DynamicController = Depends()
):
    """
    接口测试专用端点
    
    请求体格式：
    {
        "method": "GET|POST|PUT|DELETE",
        "path": "/api/users",
        "params": {"page": 1, "size": 10},
        "body": {"name": "test"}
    }
    """
    return await controller.handle_test(interface_config_id, test_request)

@router.get("/schema/{interface_config_id}", summary="获取接口数据结构")
async def get_interface_schema(
    interface_config_id: int = Path(..., description="接口配置ID"),
    controller: DynamicController = Depends()
):
    """
    获取接口的数据结构信息
    
    返回字段定义、查询能力、验证规则等
    """
    return await controller.get_schema(interface_config_id)

@router.get("/health", summary="动态API服务健康检查")
async def health_check():
    """
    动态API服务健康检查
    """
    return {
        "status": "healthy",
        "service": "dynamic-api",
        "message": "动态API服务运行正常",
        "supported_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
        "features": [
            "动态路由解析",
            "ORM配置驱动",
            "多数据库支持",
            "查询能力配置",
            "自动参数验证"
        ]
    }
