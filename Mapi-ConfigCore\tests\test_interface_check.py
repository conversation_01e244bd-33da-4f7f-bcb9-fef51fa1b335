#!/usr/bin/env python3
"""
测试接口关联检查
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.shared.database import get_database
from app.config.interface.repositories.interface_config_repository import InterfaceConfigRepository

def test_interface_check():
    """测试接口关联检查"""
    print("🔍 测试接口关联检查...")
    
    db = next(get_database())
    
    try:
        interface_repo = InterfaceConfigRepository(db)
        
        # 查询使用数据源ID=8的接口配置
        related_configs = interface_repo.get_by_datasource_id(8)
        
        print(f"📊 找到 {len(related_configs)} 个使用数据源ID=8的接口配置:")
        for config in related_configs:
            print(f"   - {config.name} (ID={config.id})")
            
        return len(related_configs) > 0
        
    except Exception as e:
        print(f"❌ 检查异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 接口关联检查测试")
    print("=" * 60)
    
    has_related = test_interface_check()
    
    print("\n" + "=" * 60)
    if has_related:
        print("✅ 检查成功：发现关联的接口配置")
    else:
        print("❌ 检查失败：未发现关联的接口配置")
    print("=" * 60)
