"""
动态Repository层
处理数据访问逻辑，封装数据库操作
"""

from typing import Dict, Any, List, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.shared.core.log_util import LogUtil
from app.shared.crypto_utils import decrypt_password
from app.config.datasource.repositories.data_source_repository import DataSourceRepository
from app.shared.database import get_database

class DynamicRepository:
    """动态Repository - 处理数据访问逻辑"""
    
    def __init__(self):
        self._engines = {}  # 缓存数据库引擎
        self._sessions = {}  # 缓存数据库会话
    
    async def find_all(self, datasource_id: int, table_name: str, conditions: Dict[str, Any] = None, 
                      page: int = 1, size: int = 10, sort_field: str = None, sort_order: str = 'desc') -> Dict[str, Any]:
        """
        查询所有记录（支持分页和排序）
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            conditions: 查询条件
            page: 页码
            size: 每页大小
            sort_field: 排序字段
            sort_order: 排序方向
            
        Returns:
            查询结果
        """
        try:
            LogUtil.info("Repository查询所有记录", datasource_id=datasource_id, table_name=table_name)
            
            engine = await self._get_engine(datasource_id)
            
            # 构建基础查询
            sql = f"SELECT * FROM {table_name}"
            params = {}
            where_conditions = []
            
            # 添加查询条件
            if conditions:
                for key, value in conditions.items():
                    if key not in ['page', 'size', 'sort', 'order'] and value is not None:
                        where_conditions.append(f"{key} = :{key}")
                        params[key] = value
            
            # 添加WHERE子句
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            
            # 添加排序
            if sort_field:
                sql += f" ORDER BY {sort_field} {sort_order.upper()}"
            
            # 执行查询
            with engine.connect() as connection:
                # 先查询总数
                count_sql = f"SELECT COUNT(*) as total FROM ({sql}) as count_query"
                count_result = connection.execute(text(count_sql), params)
                total = count_result.fetchone()[0]
                
                # 添加分页
                if page and size:
                    offset = (page - 1) * size
                    sql += f" OFFSET {offset} ROWS FETCH NEXT {size} ROWS ONLY"
                
                # 执行分页查询
                result = connection.execute(text(sql), params)
                rows = result.fetchall()
                columns = result.keys()
                
                # 转换为字典列表
                data = [dict(zip(columns, row)) for row in rows]
                
                return {
                    "success": True,
                    "message": "查询成功",
                    "data": data,
                    "total": total,
                    "page": page,
                    "size": len(data)
                }
                
        except Exception as e:
            LogUtil.error("Repository查询失败", error=str(e), datasource_id=datasource_id)
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    async def find_by_id(self, datasource_id: int, table_name: str, id_field: str, id_value: Any) -> Dict[str, Any]:
        """
        根据ID查询单条记录
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            id_field: ID字段名
            id_value: ID值
            
        Returns:
            查询结果
        """
        try:
            LogUtil.info("Repository根据ID查询", datasource_id=datasource_id, table_name=table_name, id_value=id_value)
            
            engine = await self._get_engine(datasource_id)
            
            sql = f"SELECT * FROM {table_name} WHERE {id_field} = :id_value"
            params = {"id_value": id_value}
            
            with engine.connect() as connection:
                result = connection.execute(text(sql), params)
                row = result.fetchone()
                
                if row:
                    columns = result.keys()
                    data = dict(zip(columns, row))
                    return {
                        "success": True,
                        "message": "查询成功",
                        "data": data
                    }
                else:
                    return {
                        "success": False,
                        "message": "记录不存在",
                        "data": None
                    }
                    
        except Exception as e:
            LogUtil.error("Repository根据ID查询失败", error=str(e))
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    async def create(self, datasource_id: int, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新记录
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            data: 创建数据
            
        Returns:
            创建结果
        """
        try:
            LogUtil.info("Repository创建记录", datasource_id=datasource_id, table_name=table_name)
            
            engine = await self._get_engine(datasource_id)
            
            # 构建INSERT语句
            columns = list(data.keys())
            placeholders = [f":{col}" for col in columns]
            
            sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            
            with engine.connect() as connection:
                result = connection.execute(text(sql), data)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "创建成功",
                    "data": data,
                    "affected_rows": result.rowcount
                }
                
        except Exception as e:
            LogUtil.error("Repository创建失败", error=str(e))
            return {
                "success": False,
                "message": f"创建失败: {str(e)}",
                "data": None
            }
    
    async def update(self, datasource_id: int, table_name: str, data: Dict[str, Any], conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新记录
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            data: 更新数据
            conditions: 更新条件
            
        Returns:
            更新结果
        """
        try:
            LogUtil.info("Repository更新记录", datasource_id=datasource_id, table_name=table_name)
            
            engine = await self._get_engine(datasource_id)
            
            # 构建UPDATE语句
            set_clauses = [f"{col} = :set_{col}" for col in data.keys()]
            where_clauses = [f"{col} = :where_{col}" for col in conditions.keys()]
            
            sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)}"
            
            # 合并参数
            params = {}
            for col, val in data.items():
                params[f"set_{col}"] = val
            for col, val in conditions.items():
                params[f"where_{col}"] = val
            
            with engine.connect() as connection:
                result = connection.execute(text(sql), params)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "更新成功",
                    "data": data,
                    "affected_rows": result.rowcount
                }
                
        except Exception as e:
            LogUtil.error("Repository更新失败", error=str(e))
            return {
                "success": False,
                "message": f"更新失败: {str(e)}",
                "data": None
            }
    
    async def delete(self, datasource_id: int, table_name: str, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除记录
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            conditions: 删除条件
            
        Returns:
            删除结果
        """
        try:
            LogUtil.info("Repository删除记录", datasource_id=datasource_id, table_name=table_name)
            
            engine = await self._get_engine(datasource_id)
            
            # 构建DELETE语句
            where_clauses = [f"{col} = :{col}" for col in conditions.keys()]
            sql = f"DELETE FROM {table_name} WHERE {' AND '.join(where_clauses)}"
            
            with engine.connect() as connection:
                result = connection.execute(text(sql), conditions)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "删除成功",
                    "data": {"deleted": True},
                    "affected_rows": result.rowcount
                }
                
        except Exception as e:
            LogUtil.error("Repository删除失败", error=str(e))
            return {
                "success": False,
                "message": f"删除失败: {str(e)}",
                "data": None
            }
    
    async def execute_custom_query(self, datasource_id: int, sql: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行自定义查询
        
        Args:
            datasource_id: 数据源ID
            sql: SQL语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        try:
            LogUtil.info("Repository执行自定义查询", datasource_id=datasource_id)
            
            engine = await self._get_engine(datasource_id)
            
            with engine.connect() as connection:
                result = connection.execute(text(sql), params or {})
                
                if sql.strip().upper().startswith('SELECT'):
                    # 查询操作
                    rows = result.fetchall()
                    columns = result.keys()
                    data = [dict(zip(columns, row)) for row in rows]
                    
                    return {
                        "success": True,
                        "message": "查询成功",
                        "data": data,
                        "total": len(data)
                    }
                else:
                    # 修改操作
                    connection.commit()
                    return {
                        "success": True,
                        "message": "执行成功",
                        "affected_rows": result.rowcount
                    }
                    
        except Exception as e:
            LogUtil.error("Repository自定义查询失败", error=str(e))
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    async def _get_engine(self, datasource_id: int):
        """获取数据库引擎"""
        if datasource_id not in self._engines:
            # 获取数据源配置
            db = next(get_database())
            datasource_repo = DataSourceRepository(db)
            datasource = datasource_repo.get_by_id(datasource_id)
            
            if not datasource:
                raise ValueError(f"数据源 {datasource_id} 不存在")
            
            # 解密密码
            decrypted_password = decrypt_password(datasource.password)
            
            # 构建连接字符串
            if datasource.db_type == "sqlserver":
                # 使用与连接测试相同的格式，通过pyodbc直接连接
                from urllib.parse import quote_plus
                connection_string = (
                    f"mssql+pyodbc:///?odbc_connect="
                    f"{quote_plus(f'DRIVER={{SQL Server}};SERVER={datasource.host},{datasource.port};DATABASE={datasource.database};UID={datasource.username};PWD={decrypted_password};Timeout=30;')}"
                )
            elif datasource.db_type == "mysql":
                connection_string = f"mysql+pymysql://{datasource.username}:{decrypted_password}@{datasource.host}:{datasource.port}/{datasource.database}"
            elif datasource.db_type == "postgresql":
                connection_string = f"postgresql://{datasource.username}:{decrypted_password}@{datasource.host}:{datasource.port}/{datasource.database}"
            else:
                raise ValueError(f"暂不支持数据库类型: {datasource.db_type}")
            
            # 创建引擎
            engine = create_engine(connection_string, echo=False)
            self._engines[datasource_id] = engine
            
            LogUtil.info("Repository数据库引擎创建成功", datasource_id=datasource_id, db_type=datasource.db_type)
        
        return self._engines[datasource_id]
