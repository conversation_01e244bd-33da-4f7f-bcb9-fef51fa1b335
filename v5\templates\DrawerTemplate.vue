<!-- 
  抽屉组件标准模板
  使用说明：
  1. 复制此模板创建新抽屉组件
  2. 替换所有 {ComponentName} 为实际组件名
  3. 根据实际需求调整内容区域
  4. 确保使用统一的容器类和样式导入
-->
<template>
  <div class="drawer-form-container">
    <div class="drawer-form-content custom-scrollbar">
      <!-- 抽屉内容区域 -->
      <div class="content-section">
        <div class="section-header">
          <span class="section-title">标题</span>
        </div>
        
        <!-- 具体内容 -->
        <div class="section-body">
          <!-- 在这里添加具体的内容 -->
        </div>
      </div>

      <!-- 提示信息（可选） -->
      <el-alert
        title="提示信息"
        type="info"
        :closable="false"
        show-icon
        class="info-alert"
      >
        <template #default>
          <div class="alert-content">
            <p>在这里添加提示信息</p>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const loading = ref(false)

// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps) {
    // 处理传入的props
    console.log('抽屉props:', newProps)
  }
}, { immediate: true, deep: true })

// 处理操作
const handleAction = async () => {
  try {
    loading.value = true
    // 在这里添加具体的操作逻辑
    ElMessage.success('操作成功')
    drawerMessenger.hideDrawer()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  drawerMessenger.hideDrawer()
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '取消',
      handler: handleClose
    },
    {
      text: '确定',
      type: 'primary',
      handler: handleAction,
      loading: loading.value,
      loadingText: '处理中...'
    }
  ]
}

// 监听loading状态，更新按钮状态
watch(loading, () => {
  updateDrawerButtons()
})

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.content-section {
  margin-bottom: 24px;
  
  .section-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .section-body {
    // 在这里添加具体的内容样式
  }
}

.info-alert {
  margin-top: 24px;
}

.alert-content {
  p {
    margin: 4px 0;
    line-height: 1.5;
  }
}
</style>
