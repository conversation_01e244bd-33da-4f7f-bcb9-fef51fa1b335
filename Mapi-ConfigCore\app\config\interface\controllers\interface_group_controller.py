"""
接口分组控制器层
处理HTTP请求和响应
"""

from fastapi import Depends
from sqlalchemy.orm import Session
from typing import Optional
from app.config.interface.services.interface_group_service import InterfaceGroupService
from app.config.interface.schemas.interface_group_schema import (
    InterfaceGroupCreate,
    InterfaceGroupUpdate,
    InterfaceGroupResponse,
    InterfaceGroupListResponse
)
from app.shared.database import get_database
from app.shared.core.log_util import LogUtil


class InterfaceGroupController:
    """接口分组控制器类"""
    
    def __init__(self, db: Session = Depends(get_database)):
        self.db = db
        self.service = InterfaceGroupService(db)
        LogUtil.debug("接口分组控制器初始化", controller="InterfaceGroupController")
    
    async def get_interface_groups(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None
    ) -> InterfaceGroupListResponse:
        """
        获取接口分组列表
        
        Args:
            page: 页码，默认1
            size: 每页大小，默认10
            search: 搜索关键词，可选
            
        Returns:
            接口分组列表响应
        """
        LogUtil.debug("处理获取接口分组列表请求", 
                     controller="InterfaceGroupController",
                     action="get_interface_groups",
                     page=page, size=size, search=search)
        
        return self.service.get_interface_groups(page, size, search)
    
    async def get_interface_group(self, group_id: int) -> InterfaceGroupResponse:
        """
        获取单个接口分组
        
        Args:
            group_id: 接口分组ID
            
        Returns:
            接口分组响应
        """
        LogUtil.debug("处理获取接口分组详情请求", 
                     controller="InterfaceGroupController",
                     action="get_interface_group",
                     group_id=group_id)
        
        return self.service.get_interface_group(group_id)
    
    async def create_interface_group(self, group_data: InterfaceGroupCreate) -> InterfaceGroupResponse:
        """
        创建接口分组
        
        Args:
            group_data: 接口分组创建数据
            
        Returns:
            创建的接口分组响应
        """
        LogUtil.debug("处理创建接口分组请求", 
                     controller="InterfaceGroupController",
                     action="create_interface_group",
                     name=group_data.name,
                     path_prefix=group_data.path_prefix)
        
        return self.service.create_interface_group(group_data)
    
    async def update_interface_group(
        self, 
        group_id: int, 
        group_data: InterfaceGroupUpdate
    ) -> InterfaceGroupResponse:
        """
        更新接口分组
        
        Args:
            group_id: 接口分组ID
            group_data: 更新数据
            
        Returns:
            更新后的接口分组响应
        """
        LogUtil.debug("处理更新接口分组请求", 
                     controller="InterfaceGroupController",
                     action="update_interface_group",
                     group_id=group_id)
        
        return self.service.update_interface_group(group_id, group_data)
    
    async def delete_interface_group(self, group_id: int) -> dict:
        """
        删除接口分组
        
        Args:
            group_id: 接口分组ID
            
        Returns:
            删除结果
        """
        LogUtil.debug("处理删除接口分组请求", 
                     controller="InterfaceGroupController",
                     action="delete_interface_group",
                     group_id=group_id)
        
        return self.service.delete_interface_group(group_id)
