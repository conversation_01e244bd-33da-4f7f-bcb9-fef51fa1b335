{"name": "test-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:check": "eslint . --ext .vue,.js,.ts,.jsx,.tsx"}, "dependencies": {"echarts": "^5.6.0", "pinia": "^2.1.7", "vue": "^3.5.17"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@stagewise/toolbar": "^0.4.9", "@types/node": "^24.0.4", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "element-plus": "^2.10.3", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-router": "^4.5.1", "vue-tsc": "^2.2.10"}}