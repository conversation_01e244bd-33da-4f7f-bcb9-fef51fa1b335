/*
 * 抽屉布局样式文件
 * 创建时间: 2024-12-27
 * 说明: 统一管理所有抽屉的布局、定位、动画样式
 * 使用: 仅在MainIndex.vue中导入，抽屉实例无需引用
 */

/* CSS变量定义 */
:root {
  --first-drawer-width: 35%;
  --second-drawer-width: 45%;
  --drawer-z-index-first: 10000;
  --drawer-z-index-second: 10002;
  --drawer-animation-duration: 0.3s;
  --drawer-animation-timing: cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 第一层抽屉基础样式 */
.first-drawer {
  /* 抽屉容器样式 */
  :deep(.el-drawer) {
    z-index: var(--drawer-z-index-first) !important;
    --el-drawer-padding-primary: 0 !important;
    padding: 0 !important;
  }

  /* 确保自定义头部显示 - 最高优先级设置padding为0 */
  :deep(.el-drawer__header) {
    display: block !important;
    padding: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }

  /* 抽屉主体样式 */
  :deep(.el-drawer__body) {
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* 确保内容渲染流畅 */
    will-change: transform;
    transform: translateZ(0);
  }

  /* 抽屉内容包装器 */
  .drawer-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    
    /* 组件内容区域 */
    > :first-child {
      flex: 1;
      overflow: auto;
      /* 应用统一滚动条样式 */
      scrollbar-width: thin;
      scrollbar-color: #9db7bd #f1f5f9;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #9db7bd;
        border-radius: 2px;

        &:hover {
          background: #7a9ca3;
        }
      }
    }
  }

  /* 第一层抽屉动画 */
  :deep(.el-drawer.el-drawer--rtl) {
    transition: transform var(--drawer-animation-duration) var(--drawer-animation-timing) !important;
  }
}

/* 第二层抽屉样式 - 位置偏移 */
.second-drawer {
  /* 使用最高优先级的选择器 */
  :deep(.el-drawer.el-drawer--rtl.el-drawer--open) {
    right: var(--first-drawer-width) !important;
    width: var(--second-drawer-width) !important;
    left: auto !important;
    z-index: var(--drawer-z-index-second) !important;
    position: fixed !important;
    top: 0 !important;
    height: 100vh !important;
    /* 使用和第一层抽屉相同的庄重动画 */
    transition: transform var(--drawer-animation-duration) var(--drawer-animation-timing) !important;
    /* 确保初始状态正确 */
    transform: translateX(0) !important;
  }
  
  /* 第二层抽屉关闭状态 */
  :deep(.el-drawer.el-drawer--rtl:not(.el-drawer--open)) {
    transform: translateX(100%) !important;
    transition: transform var(--drawer-animation-duration) var(--drawer-animation-timing) !important;
  }

  :deep(.el-drawer) {
    z-index: var(--drawer-z-index-second) !important;
    /* 强制设置位置，使用更高优先级 */
    position: fixed !important;
    right: var(--first-drawer-width) !important;
    top: 0 !important;
    height: 100vh !important;
    width: var(--second-drawer-width) !important;
    /* 使用和第一层抽屉相同的庄重动画 */
    transition: transform var(--drawer-animation-duration) var(--drawer-animation-timing) !important;
  }

  /* 第二层抽屉头部样式 */
  :deep(.el-drawer__header) {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
  }

  /* 第二层抽屉遮罩层样式 */
  :deep(.el-overlay) {
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: 10001 !important;
    /* 遮罩层覆盖整个屏幕，包括第一层抽屉 */
    position: fixed !important;
    right: 0 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    /* 遮罩层动画 - 和第一层抽屉相同 */
    transition: opacity var(--drawer-animation-duration) ease !important;
  }
  
  :deep(.el-drawer__container) {
    /* 第二层抽屉容器 */
    position: fixed !important;
    right: var(--first-drawer-width) !important;
    top: 0 !important;
    width: var(--second-drawer-width) !important;
    height: 100vh !important;
    z-index: var(--drawer-z-index-second) !important;
    /* 容器动画 - 和第一层抽屉相同的庄重动画 */
    transition: transform var(--drawer-animation-duration) var(--drawer-animation-timing) !important;
  }
  
  :deep(.el-drawer__body) {
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100% !important;
    /* 抽屉内容动画优化 - 和第一层抽屉相同 */
    will-change: transform;
    transform: translateZ(0);
  }

  /* 第二层抽屉内容包装器 */
  .drawer-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    
    /* 组件内容区域 */
    > :first-child {
      flex: 1;
      overflow: auto;
      /* 应用统一滚动条样式 */
      scrollbar-width: thin;
      scrollbar-color: #9db7bd #f1f5f9;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #9db7bd;
        border-radius: 2px;

        &:hover {
          background: #7a9ca3;
        }
      }
    }
  }
}

/* 抽屉宽度动态计算 */
@media (max-width: 1200px) {
  :root {
    --first-drawer-width: 40%;
    --second-drawer-width: 50%;
  }
}

@media (max-width: 768px) {
  :root {
    --first-drawer-width: 80%;
    --second-drawer-width: 90%;
  }
}
