"""
接口配置数据访问层
负责数据库的CRUD操作
"""

from typing import List, Optional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from app.config.interface.models.interface_config_model import InterfaceConfigModel
from app.config.interface.models.interface_group_model import InterfaceGroupModel
from app.config.interface.models.interface_tag_model import InterfaceTagModel
from app.config.interface.models.interface_config_tag_model import InterfaceConfigTagModel
from app.config.datasource.models.data_source_model import DataSourceModel
from app.config.interface.schemas.interface_config_schema import InterfaceConfigCreate, InterfaceConfigUpdate
from app.shared.core.log_util import LogUtil
import json


class InterfaceConfigRepository:
    """接口配置数据访问类"""
    
    def __init__(self, db: Session):
        self.db = db
        LogUtil.debug("接口配置Repository初始化", repository="InterfaceConfigRepository")
    
    def get_by_id(self, config_id: int) -> Optional[InterfaceConfigModel]:
        """根据ID获取接口配置"""
        LogUtil.debug("根据ID获取接口配置", config_id=config_id)
        return self.db.query(InterfaceConfigModel)\
            .options(joinedload(InterfaceConfigModel.group), joinedload(InterfaceConfigModel.datasource))\
            .filter(InterfaceConfigModel.id == config_id).first()

    def get_by_path(self, path: str) -> Optional[InterfaceConfigModel]:
        """根据路径获取接口配置"""
        LogUtil.debug("根据路径获取接口配置", path=path)
        return self.db.query(InterfaceConfigModel)\
            .options(joinedload(InterfaceConfigModel.group), joinedload(InterfaceConfigModel.datasource))\
            .filter(InterfaceConfigModel.path == path).first()

    def check_path_exists(self, path: str, exclude_id: Optional[int] = None) -> bool:
        """检查路径是否已存在（用于唯一性校验）"""
        LogUtil.debug("检查路径唯一性", path=path, exclude_id=exclude_id)
        query = self.db.query(InterfaceConfigModel).filter(
            InterfaceConfigModel.path == path
        )
        if exclude_id:
            query = query.filter(InterfaceConfigModel.id != exclude_id)
        return query.first() is not None

    def get_list(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None,
        group_id: Optional[int] = None,
        method: Optional[str] = None,
        is_enabled: Optional[bool] = None
    ) -> Tuple[List[InterfaceConfigModel], int]:
        """
        获取接口配置列表（分页）
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词（名称、路径、描述）
            group_id: 分组ID过滤
            method: HTTP方法过滤
            is_enabled: 启用状态过滤
            
        Returns:
            (接口配置列表, 总数量)
        """
        LogUtil.debug("获取接口配置列表", 
                     page=page, size=size, search=search, 
                     group_id=group_id, method=method, is_enabled=is_enabled)
        
        query = self.db.query(InterfaceConfigModel)\
            .options(joinedload(InterfaceConfigModel.group), joinedload(InterfaceConfigModel.datasource))
        
        # 搜索过滤
        if search:
            search_filter = or_(
                InterfaceConfigModel.name.ilike(f"%{search}%"),
                InterfaceConfigModel.path.ilike(f"%{search}%"),
                InterfaceConfigModel.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 分组过滤
        if group_id:
            query = query.filter(InterfaceConfigModel.group_id == group_id)
        
        # HTTP方法过滤
        if method:
            query = query.filter(InterfaceConfigModel.method == method)
        
        # 启用状态过滤
        if is_enabled is not None:
            query = query.filter(InterfaceConfigModel.is_enabled == is_enabled)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        items = query.order_by(InterfaceConfigModel.created_at.desc()).offset((page - 1) * size).limit(size).all()
        
        LogUtil.info("接口配置列表查询完成", total=total, returned_count=len(items))
        return items, total
    
    def create(self, config_data: InterfaceConfigCreate) -> InterfaceConfigModel:
        """
        创建接口配置
        
        Args:
            config_data: 接口配置创建数据
            
        Returns:
            创建的接口配置模型
        """
        LogUtil.debug("创建接口配置", name=config_data.name, path=config_data.path, method=config_data.method)
        
        # 创建接口配置模型
        db_config = InterfaceConfigModel(
            name=config_data.name,
            path=config_data.path,
            method=config_data.method.value,
            description=config_data.description,
            group_id=config_data.group_id,
            datasource_id=config_data.datasource_id,
            table_name=config_data.table_name,
            is_enabled=config_data.is_enabled,
            is_public=config_data.is_public,
            query_fields=json.dumps(config_data.query_fields) if config_data.query_fields else None,
            required_fields=json.dumps(config_data.required_fields) if config_data.required_fields else None,
            response_fields=json.dumps(config_data.response_fields) if config_data.response_fields else None,
            # 新增字段
            orm_model_config=json.dumps(config_data.orm_model_config) if config_data.orm_model_config else None,
            orm_model_name=config_data.orm_model_name,
            orm_relationships=json.dumps(config_data.orm_relationships) if config_data.orm_relationships else None,
            parameter_config=json.dumps(config_data.parameter_config) if config_data.parameter_config else None,
            visual_config=json.dumps(config_data.visual_config) if config_data.visual_config else None,
            validation_rules=json.dumps(config_data.validation_rules) if config_data.validation_rules else None,
            cache_duration=config_data.cache_duration,
            rate_limit=config_data.rate_limit,
            created_by=config_data.created_by
        )
        
        self.db.add(db_config)
        self.db.commit()
        self.db.refresh(db_config)
        
        # 处理标签关联
        if config_data.tags:
            self._update_config_tags(db_config.id, config_data.tags, config_data.created_by)
        
        LogUtil.info("接口配置创建成功", config_id=db_config.id, name=db_config.name, path=db_config.path)
        return db_config
    
    def update(self, config_id: int, config_data: InterfaceConfigUpdate) -> Optional[InterfaceConfigModel]:
        """
        更新接口配置
        
        Args:
            config_id: 接口配置ID
            config_data: 更新数据
            
        Returns:
            更新后的接口配置模型
        """
        LogUtil.debug("更新接口配置", config_id=config_id)
        
        db_config = self.get_by_id(config_id)
        if not db_config:
            LogUtil.warning("接口配置不存在", config_id=config_id)
            return None
        
        # 更新字段
        update_data = config_data.dict(exclude_unset=True, exclude={'tags'})
        json_fields = ['query_fields', 'required_fields', 'response_fields',
                      'orm_model_config', 'orm_relationships', 'parameter_config',
                      'visual_config', 'validation_rules']

        for field, value in update_data.items():
            if field in json_fields and value is not None:
                setattr(db_config, field, json.dumps(value))
            elif field == 'method' and value is not None:
                setattr(db_config, field, value.value)
            else:
                setattr(db_config, field, value)
        
        self.db.commit()
        self.db.refresh(db_config)
        
        # 处理标签关联更新
        if config_data.tags is not None:
            self._update_config_tags(db_config.id, config_data.tags)
        
        LogUtil.info("接口配置更新成功", config_id=db_config.id, name=db_config.name)
        return db_config
    
    def delete(self, config_id: int) -> bool:
        """
        删除接口配置
        
        Args:
            config_id: 接口配置ID
            
        Returns:
            是否删除成功
        """
        LogUtil.debug("删除接口配置", config_id=config_id)
        
        db_config = self.get_by_id(config_id)
        if not db_config:
            LogUtil.warning("接口配置不存在", config_id=config_id)
            return False
        
        # 删除标签关联
        self.db.query(InterfaceConfigTagModel).filter(
            InterfaceConfigTagModel.interface_config_id == config_id
        ).delete()
        
        # 删除接口配置
        self.db.delete(db_config)
        self.db.commit()
        
        LogUtil.info("接口配置删除成功", config_id=config_id, name=db_config.name)
        return True
    
    def check_path_method_exists(self, path: str, method: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查路径+方法组合是否已存在
        
        Args:
            path: 接口路径
            method: HTTP方法
            exclude_id: 排除的ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = self.db.query(InterfaceConfigModel).filter(
            and_(InterfaceConfigModel.path == path, InterfaceConfigModel.method == method)
        )
        
        if exclude_id:
            query = query.filter(InterfaceConfigModel.id != exclude_id)
        
        exists = query.first() is not None
        LogUtil.debug("检查路径+方法组合是否存在", path=path, method=method, exclude_id=exclude_id, exists=exists)
        return exists
    
    def get_config_tags(self, config_id: int) -> List[InterfaceTagModel]:
        """获取接口配置的标签列表"""
        LogUtil.debug("获取接口配置的标签列表", config_id=config_id)
        
        return self.db.query(InterfaceTagModel)\
            .join(InterfaceConfigTagModel, InterfaceTagModel.id == InterfaceConfigTagModel.interface_tag_id)\
            .filter(InterfaceConfigTagModel.interface_config_id == config_id)\
            .all()
    
    def _update_config_tags(self, config_id: int, tag_ids: List[int], created_by: Optional[str] = None):
        """更新接口配置的标签关联"""
        LogUtil.debug("更新接口配置的标签关联", config_id=config_id, tag_ids=tag_ids)
        
        # 删除现有关联
        self.db.query(InterfaceConfigTagModel).filter(
            InterfaceConfigTagModel.interface_config_id == config_id
        ).delete()
        
        # 创建新关联
        for tag_id in tag_ids:
            tag_relation = InterfaceConfigTagModel(
                interface_config_id=config_id,
                interface_tag_id=tag_id,
                created_by=created_by
            )
            self.db.add(tag_relation)
        
        self.db.commit()
        LogUtil.info("接口配置标签关联更新完成", config_id=config_id, tag_count=len(tag_ids))

    def get_by_datasource_id(self, datasource_id: int) -> List[InterfaceConfigModel]:
        """根据数据源ID获取接口配置列表"""
        try:
            configs = self.db.query(InterfaceConfigModel).filter(
                InterfaceConfigModel.datasource_id == datasource_id
            ).all()

            LogUtil.info("根据数据源ID查询接口配置", datasource_id=datasource_id, count=len(configs))
            return configs

        except Exception as e:
            LogUtil.tech_error("根据数据源ID查询接口配置失败",
                             datasource_id=datasource_id,
                             error=str(e))
            raise
