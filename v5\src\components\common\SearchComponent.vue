<!-- 
  SearchComponent - 通用搜索输入框组件
  功能: 提供带搜索按钮的输入框，支持回车搜索、清空和双向绑定
  特点:
    1. 支持Enter键触发搜索
    2. 内置清空按钮和事件
    3. 支持自定义尺寸、宽度和占位符
    4. 与父组件的双向数据绑定
  Props:
    modelValue - 搜索关键词(v-model)
    placeholder - 输入框提示文本
    clearable - 是否显示清空按钮
    size - 组件尺寸(large/default/small)
    width - 组件宽度
  Events:
    search - 搜索触发事件(返回搜索值)
    clear - 清空按钮点击事件
    update:modelValue - 输入值变化事件
-->
<template>
  <div class="search-component">
    <el-input
      v-model="searchValue"
      :placeholder="placeholder"
      :clearable="clearable"
      :size="size"
      :style="{ width: width }"
      @clear="handleClear"
      @keyup.enter="handleSearch"
    >
      <template #suffix>
        <el-button
          :icon="Search"
          :size="size"
          @click="handleSearch"
          class="search-button"
          text
        />
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';

interface Props {
  modelValue: string;
  placeholder?: string;
  clearable?: boolean;
  size?: 'large' | 'default' | 'small';
  width?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'search', value: string): void;
  (e: 'clear'): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词',
  clearable: true,
  size: 'default',
  width: '300px'
});

const emit = defineEmits<Emits>();

const searchValue = ref(props.modelValue);

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue;
});

// 监听内部值变化
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue);
});

// 搜索处理
const handleSearch = () => {
  emit('search', searchValue.value);
};

// 清空处理
const handleClear = () => {
  searchValue.value = '';
  emit('update:modelValue', '');
  emit('clear');
};
</script>

<style scoped>
.search-component {
  display: inline-block;
}

.search-button {
  border: none;
  background: transparent;
  padding: 4px 8px;
  margin-right: 0;
  color: #3FC8DD;
}

:deep(.el-button.search-button:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  color: #409eff !important;
  border-radius: 4px;
}

:deep(.el-button.is-text.search-button:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  color: #409eff !important;
  border-radius: 4px;
}

:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}

:deep(.el-input__suffix-inner) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 修复搜索组件右侧放大镜距离问题 - 只针对带有suffix的输入框 */
:deep(.el-input--suffix .el-input__wrapper) {
  padding-right: 0 !important;
}

/* 移除输入框包装器的右侧内边距，让搜索按钮贴近右边 */
:deep(.el-input__wrapper) {
  padding-right: 0 !important;
}
</style>
