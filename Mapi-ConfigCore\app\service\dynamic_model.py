"""
动态模型层
动态生成模型，管理数据库连接，执行SQL操作
"""

from typing import Dict, Any, List
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.shared.core.log_util import LogUtil
from app.shared.crypto_utils import decrypt_password
from app.config.datasource.repositories.data_source_repository import DataSourceRepository
from app.shared.database import get_database

class DynamicModel:
    """动态模型 - 处理数据库操作"""
    
    def __init__(self):
        self._engines = {}  # 缓存数据库引擎
    
    async def execute_query(self, datasource_id: int, orm_config: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询操作"""
        try:
            LogUtil.info("执行查询操作", datasource_id=datasource_id, query_params=query_params)
            
            # 获取数据库引擎
            engine = await self._get_engine(datasource_id)
            
            # 构建查询SQL
            sql_query, params = self._build_select_sql(orm_config, query_params)
            
            # 执行查询
            with engine.connect() as connection:
                result = connection.execute(text(sql_query), params)
                rows = result.fetchall()
                columns = result.keys()
                
                # 转换为字典列表
                data = [dict(zip(columns, row)) for row in rows]
                
                # 处理分页
                total = len(data)
                page = int(query_params.get('page', 1))
                size = int(query_params.get('size', 10))
                
                if 'page' in query_params:
                    start = (page - 1) * size
                    end = start + size
                    data = data[start:end]
                
                return {
                    "success": True,
                    "message": "查询成功",
                    "data": data,
                    "total": total,
                    "page": page,
                    "size": len(data)
                }
                
        except Exception as e:
            LogUtil.error("查询操作失败", error=str(e), datasource_id=datasource_id)
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    async def execute_insert(self, datasource_id: int, orm_config: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """执行插入操作"""
        try:
            engine = await self._get_engine(datasource_id)
            sql_query, params = self._build_insert_sql(orm_config, data)
            
            with engine.connect() as connection:
                result = connection.execute(text(sql_query), params)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "创建成功",
                    "data": data,
                    "affected_rows": result.rowcount
                }
        except Exception as e:
            LogUtil.error("插入操作失败", error=str(e))
            return {
                "success": False,
                "message": f"创建失败: {str(e)}",
                "data": None
            }
    
    async def execute_update(self, datasource_id: int, orm_config: Dict[str, Any], data: Dict[str, Any], conditions: Dict[str, Any]) -> Dict[str, Any]:
        """执行更新操作"""
        try:
            engine = await self._get_engine(datasource_id)
            sql_query, params = self._build_update_sql(orm_config, data, conditions)
            
            with engine.connect() as connection:
                result = connection.execute(text(sql_query), params)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "更新成功",
                    "data": data,
                    "affected_rows": result.rowcount
                }
        except Exception as e:
            LogUtil.error("更新操作失败", error=str(e))
            return {
                "success": False,
                "message": f"更新失败: {str(e)}",
                "data": None
            }
    
    async def execute_delete(self, datasource_id: int, orm_config: Dict[str, Any], conditions: Dict[str, Any]) -> Dict[str, Any]:
        """执行删除操作"""
        try:
            engine = await self._get_engine(datasource_id)
            sql_query, params = self._build_delete_sql(orm_config, conditions)
            
            with engine.connect() as connection:
                result = connection.execute(text(sql_query), params)
                connection.commit()
                
                return {
                    "success": True,
                    "message": "删除成功",
                    "data": {"deleted": True},
                    "affected_rows": result.rowcount
                }
        except Exception as e:
            LogUtil.error("删除操作失败", error=str(e))
            return {
                "success": False,
                "message": f"删除失败: {str(e)}",
                "data": None
            }
    
    def _build_select_sql(self, orm_config: Dict[str, Any], query_params: Dict[str, Any]) -> tuple:
        """构建SELECT SQL"""
        LogUtil.info("构建SELECT SQL", orm_config_keys=list(orm_config.keys()), query_params=query_params)

        table_name = orm_config.get("table_name", "")
        query_mapping = orm_config.get("query_mapping", {})

        LogUtil.info("SQL构建参数", table_name=table_name, has_query_mapping=bool(query_mapping))

        # 构建字段映射 - 将ORM字段名映射到实际数据库字段名
        sqlalchemy_model = orm_config.get("sqlalchemy_model", {})
        fields = sqlalchemy_model.get("fields", [])

        # 创建字段映射字典：ORM字段名 -> 实际字段名
        field_mapping = {}
        select_fields = []

        for field in fields:
            orm_field_name = field.get("name", "")
            actual_field_name = field.get("original_name", orm_field_name)
            field_mapping[orm_field_name] = actual_field_name
            # 构建SELECT字段：实际字段名 AS ORM字段名
            if orm_field_name != actual_field_name:
                select_fields.append(f"{actual_field_name} AS {orm_field_name}")
            else:
                select_fields.append(actual_field_name)

        # 如果没有字段定义，使用SELECT *
        if not select_fields:
            sql = f"SELECT * FROM {table_name}"
        else:
            sql = f"SELECT {', '.join(select_fields)} FROM {table_name}"

        params = {}
        where_conditions = []
        
        # 处理查询条件
        for key, value in query_params.items():
            if key in ['page', 'size', 'sort', 'order']:
                continue

            # 获取实际字段名
            actual_field_name = field_mapping.get(key, key)

            # 模糊查询
            if key in query_mapping.get('fuzzy_search_fields', []):
                where_conditions.append(f"{actual_field_name} LIKE :fuzzy_{key}")
                params[f'fuzzy_{key}'] = f"%{value}%"
            # 精确匹配
            elif key in query_mapping.get('exact_match_fields', []):
                where_conditions.append(f"{actual_field_name} = :exact_{key}")
                params[f'exact_{key}'] = value
            # 范围查询
            elif key in query_mapping.get('range_query_fields', []):
                if key.endswith('_min'):
                    base_field = key[:-4]
                    actual_base_field = field_mapping.get(base_field, base_field)
                    where_conditions.append(f"{actual_base_field} >= :range_min_{base_field}")
                    params[f'range_min_{base_field}'] = value
                elif key.endswith('_max'):
                    base_field = key[:-4]
                    actual_base_field = field_mapping.get(base_field, base_field)
                    where_conditions.append(f"{actual_base_field} <= :range_max_{base_field}")
                    params[f'range_max_{base_field}'] = value
                else:
                    actual_field_name = field_mapping.get(key, key)
                    where_conditions.append(f"{actual_field_name} = :range_{key}")
                    params[f'range_{key}'] = value
        
        # 添加WHERE子句
        if where_conditions:
            sql += " WHERE " + " AND ".join(where_conditions)
        
        # 添加排序
        sort_field = query_params.get('sort', query_mapping.get('default_sort', 'id'))
        sort_order = query_params.get('order', query_mapping.get('default_order', 'desc'))
        if sort_field in query_mapping.get('allowed_sort_fields', []):
            actual_sort_field = field_mapping.get(sort_field, sort_field)
            sql += f" ORDER BY {actual_sort_field} {sort_order.upper()}"

        LogUtil.info("SQL构建完成", sql=sql, params=params)
        return sql, params
    
    def _build_insert_sql(self, orm_config: Dict[str, Any], data: Dict[str, Any]) -> tuple:
        """构建INSERT SQL"""
        table_name = orm_config.get("table_name", "")
        fields = orm_config.get("sqlalchemy_model", {}).get("fields", [])
        
        # 过滤有效字段
        valid_fields = []
        params = {}
        
        for field in fields:
            field_name = field['name']
            if field_name in data and not field.get('primary_key', False):
                valid_fields.append(field_name)
                params[field_name] = data[field_name]
        
        if not valid_fields:
            raise ValueError("没有有效的插入字段")
        
        columns = ", ".join(valid_fields)
        placeholders = ", ".join([f":{field}" for field in valid_fields])
        
        sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        return sql, params
    
    def _build_update_sql(self, orm_config: Dict[str, Any], data: Dict[str, Any], conditions: Dict[str, Any]) -> tuple:
        """构建UPDATE SQL"""
        table_name = orm_config.get("table_name", "")
        fields = orm_config.get("sqlalchemy_model", {}).get("fields", [])
        
        # 构建SET子句
        set_clauses = []
        params = {}
        
        for field in fields:
            field_name = field['name']
            if field_name in data and not field.get('primary_key', False):
                set_clauses.append(f"{field_name} = :set_{field_name}")
                params[f'set_{field_name}'] = data[field_name]
        
        # 构建WHERE子句
        where_conditions = []
        for key, value in conditions.items():
            if key not in ['page', 'size', 'sort', 'order']:
                where_conditions.append(f"{key} = :where_{key}")
                params[f'where_{key}'] = value
        
        if not set_clauses:
            raise ValueError("没有有效的更新字段")
        if not where_conditions:
            raise ValueError("UPDATE操作需要WHERE条件")
        
        sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_conditions)}"
        return sql, params
    
    def _build_delete_sql(self, orm_config: Dict[str, Any], conditions: Dict[str, Any]) -> tuple:
        """构建DELETE SQL"""
        table_name = orm_config.get("table_name", "")
        
        where_conditions = []
        params = {}
        
        for key, value in conditions.items():
            if key not in ['page', 'size', 'sort', 'order']:
                where_conditions.append(f"{key} = :where_{key}")
                params[f'where_{key}'] = value
        
        if not where_conditions:
            raise ValueError("DELETE操作需要WHERE条件")
        
        sql = f"DELETE FROM {table_name} WHERE {' AND '.join(where_conditions)}"
        return sql, params
    
    async def _get_engine(self, datasource_id: int):
        """获取数据库引擎"""
        if datasource_id not in self._engines:
            # 获取数据源配置
            db = next(get_database())
            datasource_repo = DataSourceRepository(db)
            datasource = datasource_repo.get_by_id(datasource_id)
            
            if not datasource:
                raise ValueError(f"数据源 {datasource_id} 不存在")
            
            # 解密密码
            decrypted_password = decrypt_password(datasource.password)
            
            # 构建连接字符串
            if datasource.db_type == "sqlserver":
                # 使用与连接测试相同的格式，通过pyodbc直接连接
                from urllib.parse import quote_plus
                connection_string = (
                    f"mssql+pyodbc:///?odbc_connect="
                    f"{quote_plus(f'DRIVER={{SQL Server}};SERVER={datasource.host},{datasource.port};DATABASE={datasource.database};UID={datasource.username};PWD={decrypted_password};Timeout=30;')}"
                )
            elif datasource.db_type == "mysql":
                connection_string = f"mysql+pymysql://{datasource.username}:{decrypted_password}@{datasource.host}:{datasource.port}/{datasource.database}"
            elif datasource.db_type == "postgresql":
                connection_string = f"postgresql://{datasource.username}:{decrypted_password}@{datasource.host}:{datasource.port}/{datasource.database}"
            else:
                raise ValueError(f"暂不支持数据库类型: {datasource.db_type}")
            
            # 创建引擎
            engine = create_engine(connection_string, echo=False)
            self._engines[datasource_id] = engine
            
            LogUtil.info("数据库引擎创建成功", datasource_id=datasource_id, db_type=datasource.db_type)
        
        return self._engines[datasource_id]
