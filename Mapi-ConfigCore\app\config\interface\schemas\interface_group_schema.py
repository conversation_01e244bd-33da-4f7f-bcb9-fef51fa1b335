"""
接口分组数据模式定义
"""

from typing import Optional
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
# 移除转换工具类导入


class InterfaceGroupBase(BaseModel):
    """接口分组基础模式"""
    model_config = ConfigDict(from_attributes=True)

    name: str = Field(..., min_length=1, max_length=100, description="分组名称")
    path_prefix: str = Field(..., min_length=1, max_length=50, description="路径前缀")
    description: Optional[str] = Field(None, max_length=500, description="分组描述")
    is_enabled: bool = Field(True, description="是否启用")


class InterfaceGroupCreate(InterfaceGroupBase):
    """接口分组创建模式"""
    created_by: Optional[str] = Field(None, max_length=50, description="创建人")


class InterfaceGroupUpdate(BaseModel):
    """接口分组更新模式"""
    model_config = ConfigDict(from_attributes=True)

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="分组名称")
    path_prefix: Optional[str] = Field(None, min_length=1, max_length=50, description="路径前缀")
    description: Optional[str] = Field(None, max_length=500, description="分组描述")
    is_enabled: Optional[bool] = Field(None, description="是否启用")


class InterfaceGroupResponse(BaseModel):
    """接口分组响应模式"""
    model_config = ConfigDict(from_attributes=True)

    # 继承基础字段
    name: str = Field(..., description="分组名称")
    path_prefix: str = Field(..., description="路径前缀")
    description: Optional[str] = Field(None, description="分组描述")
    is_enabled: bool = Field(..., description="是否启用")

    # 响应特有字段
    id: int
    interface_count: Optional[int] = Field(None, description="包含的接口数量")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]


class InterfaceGroupListResponse(BaseModel):
    """接口分组列表响应模式"""
    model_config = ConfigDict(from_attributes=True)

    items: list[InterfaceGroupResponse]
    total: int
    page: int
    size: int
    pages: int
