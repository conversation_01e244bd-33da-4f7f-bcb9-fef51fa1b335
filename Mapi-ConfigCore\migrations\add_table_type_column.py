"""
添加 table_type 字段到 interface_configs 表

这个迁移脚本为 interface_configs 表添加 table_type 字段，
用于存储数据表类型（table/view/procedure）
"""

import sqlite3
import os

def migrate():
    """执行数据库迁移"""
    # 使用实际的数据库路径
    db_path = r"D:\others\Mapi\Mapi-Data\mapi_config.db"
    print(f"数据库路径: {db_path}")
    print(f"数据库文件是否存在: {os.path.exists(db_path)}")

    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(interface_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'table_type' not in columns:
            print("添加 table_type 字段到 interface_configs 表...")
            
            # 添加 table_type 字段
            cursor.execute("""
                ALTER TABLE interface_configs 
                ADD COLUMN table_type VARCHAR(20) DEFAULT 'table'
            """)
            
            # 更新现有记录的默认值
            cursor.execute("""
                UPDATE interface_configs 
                SET table_type = 'table' 
                WHERE table_type IS NULL
            """)
            
            conn.commit()
            print("✅ table_type 字段添加成功")
        else:
            print("✅ table_type 字段已存在，跳过迁移")
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate()
