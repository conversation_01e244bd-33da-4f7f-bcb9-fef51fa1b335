<template>
  <div class="drawer-form-container">
    <div class="drawer-form-content custom-scrollbar">


      <!-- 权限配置 -->
      <div class="permission-section">
        <!-- 操作栏 -->
        <div class="permission-toolbar">
          <div class="toolbar-left">
            <h3 class="section-title">接口权限配置</h3>
            <el-tag type="info" class="selected-count">
              已选择: {{ selectedCount }} 个接口
            </el-tag>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button size="small" @click="expandAll">
                <el-icon><Plus /></el-icon>
                全部展开
              </el-button>
              <el-button size="small" @click="collapseAll">
                <el-icon><Minus /></el-icon>
                全部收起
              </el-button>
            </el-button-group>
            <el-button-group>
              <el-button type="primary" size="small" @click="selectAll">
                <el-icon><Check /></el-icon>
                全选
              </el-button>
              <el-button size="small" @click="clearAll">
                <el-icon><Close /></el-icon>
                清空
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 搜索和筛选栏 -->
        <div class="search-filter-bar">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索接口名称、路径或描述"
              clearable
              @input="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-box">
            <el-select
              v-model="filterMethod"
              placeholder="HTTP方法"
              clearable
              @change="handleFilter"
              class="filter-select"
            >
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
              <el-option label="PUT" value="PUT" />
              <el-option label="DELETE" value="DELETE" />
              <el-option label="PATCH" value="PATCH" />
            </el-select>
            <el-select
              v-model="filterGroup"
              placeholder="接口分组"
              clearable
              @change="handleFilter"
              class="filter-select"
            >
              <el-option
                v-for="group in interfaceGroups"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </div>
        </div>

        <!-- 接口权限表格 -->
        <div class="permission-table">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="filteredInterfaces"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            @select="handleSelect"
            @select-all="handleSelectAll"
          >
            <el-table-column type="selection" width="55" :selectable="isSelectable" />
            <el-table-column label="接口信息" min-width="300">
              <template #default="{ row }">
                <div class="interface-info">
                  <div class="interface-header">
                    <el-tag
                      v-if="row.method"
                      :type="getMethodTagType(row.method)"
                      size="small"
                      class="method-tag"
                    >
                      {{ row.method }}
                    </el-tag>
                    <span class="interface-name">{{ row.name }}</span>
                  </div>
                  <div v-if="row.path" class="interface-path">{{ row.path }}</div>
                  <div v-if="row.description" class="interface-description">
                    {{ row.description }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="访问类型" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.accessType" :type="row.accessType === 'public' ? 'success' : 'warning'" size="small">
                  {{ row.accessType === 'public' ? '公有' : '私有' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag v-if="row.status" :type="row.status === 'enabled' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'enabled' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 已选择的权限统计 -->
        <div class="permission-summary">
          <el-alert
            :title="`已选择 ${selectedPermissions.length} 个权限项`"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="summary-content">
                <span>分组: {{ selectedGroups.length }} 个</span>
                <span>接口: {{ selectedInterfaces.length }} 个</span>
              </div>
            </template>
          </el-alert>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus, Minus, Check, Close } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const tableRef = ref()
const loading = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const filterMethod = ref('')
const filterGroup = ref('')

// 客户端数据
const clientData = ref({})

// 接口数据
const interfaceGroups = ref([])
const allInterfaces = ref([])
const selectedPermissions = ref([])

// 计算属性
const filteredInterfaces = computed(() => {
  let result = [...allInterfaces.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = filterByKeyword(result, keyword)
  }
  
  // 方法过滤
  if (filterMethod.value) {
    result = filterByMethod(result, filterMethod.value)
  }
  
  // 分组过滤
  if (filterGroup.value) {
    result = result.filter(item => {
      if (item.type === 'group') {
        return item.id === filterGroup.value
      }
      return item.groupId === filterGroup.value
    })
  }
  
  return result
})

const selectedGroups = computed(() => {
  return selectedPermissions.value.filter(p => p.resourceType === 'group')
})

const selectedInterfaces = computed(() => {
  return selectedPermissions.value.filter(p => p.resourceType === 'interface')
})

const selectedCount = computed(() => {
  return selectedPermissions.value.length
})

// 更新表格选中状态
const updateTableSelection = () => {
  if (!tableRef.value) return

  // 清空当前选择
  tableRef.value.clearSelection()

  // 根据权限数据设置选中状态
  const setSelection = (items) => {
    items.forEach(item => {
      const isSelected = selectedPermissions.value.some(p =>
        (p.resourceType === 'group' && p.resourceId === item.id && item.type === 'group') ||
        (p.resourceType === 'interface' && p.resourceId === item.id && item.type === 'interface')
      )

      if (isSelected) {
        tableRef.value.toggleRowSelection(item, true)
      }

      if (item.children) {
        setSelection(item.children)
      }
    })
  }

  setSelection(allInterfaces.value)
}

// 加载权限数据
const loadPermissionData = async () => {
  try {
    // TODO: 调用API获取客户端已有权限
    // 模拟数据
    selectedPermissions.value = [
      { resourceType: 'interface', resourceId: 'interface_1' }
    ]

    // 设置表格选中状态
    updateTableSelection()
  } catch (error) {
    ElMessage.error('加载权限数据失败')
    console.error('加载权限数据失败:', error)
  }
}

// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.clientData) {
    clientData.value = newProps.clientData
    loadPermissionData()
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  loadInterfaceData()
})

// 加载接口数据
const loadInterfaceData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取接口分组和接口列表
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))

    interfaceGroups.value = [
      { id: 1, name: '用户管理' },
      { id: 2, name: '数据源管理' },
      { id: 3, name: '接口管理' }
    ]

    allInterfaces.value = [
      {
        id: 'group_1',
        name: '用户管理',
        type: 'group',
        children: [
          {
            id: 'interface_1',
            name: '获取用户列表',
            path: '/api/users',
            method: 'GET',
            description: '获取系统用户列表',
            accessType: 'public',
            status: 'enabled',
            groupId: 1,
            type: 'interface'
          },
          {
            id: 'interface_2',
            name: '创建用户',
            path: '/api/users',
            method: 'POST',
            description: '创建新用户',
            accessType: 'private',
            status: 'enabled',
            groupId: 1,
            type: 'interface'
          }
        ]
      },
      {
        id: 'group_2',
        name: '数据源管理',
        type: 'group',
        children: [
          {
            id: 'interface_3',
            name: '获取数据源列表',
            path: '/api/datasources',
            method: 'GET',
            description: '获取数据源列表',
            accessType: 'public',
            status: 'enabled',
            groupId: 2,
            type: 'interface'
          }
        ]
      }
    ]
  } catch (error) {
    ElMessage.error('加载接口数据失败')
    console.error('加载接口数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索过滤
const filterByKeyword = (items, keyword) => {
  const result = []

  items.forEach(item => {
    const matchesKeyword =
      item.name.toLowerCase().includes(keyword) ||
      (item.path && item.path.toLowerCase().includes(keyword)) ||
      (item.description && item.description.toLowerCase().includes(keyword))

    if (item.type === 'group') {
      const filteredChildren = item.children ? filterByKeyword(item.children, keyword) : []
      if (matchesKeyword || filteredChildren.length > 0) {
        result.push({
          ...item,
          children: filteredChildren
        })
      }
    } else if (matchesKeyword) {
      result.push(item)
    }
  })

  return result
}

// 方法过滤
const filterByMethod = (items, method) => {
  const result = []

  items.forEach(item => {
    if (item.type === 'group') {
      const filteredChildren = item.children ? filterByMethod(item.children, method) : []
      if (filteredChildren.length > 0) {
        result.push({
          ...item,
          children: filteredChildren
        })
      }
    } else if (item.method === method) {
      result.push(item)
    }
  })

  return result
}

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const typeMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return typeMap[method] || 'info'
}

// 判断行是否可选择
const isSelectable = (row) => {
  return row.status === 'enabled'
}

// 处理搜索
const handleSearch = () => {
  // 搜索时自动展开所有匹配项
  if (searchKeyword.value) {
    expandAll()
  }
}

// 处理筛选
const handleFilter = () => {
  // 筛选后更新表格选中状态
  setTimeout(() => {
    updateTableSelection()
  }, 100)
}

// 展开所有
const expandAll = () => {
  if (!tableRef.value) return

  const expandRows = (items) => {
    items.forEach(item => {
      if (item.type === 'group') {
        tableRef.value.toggleRowExpansion(item, true)
        if (item.children) {
          expandRows(item.children)
        }
      }
    })
  }

  expandRows(filteredInterfaces.value)
}

// 收起所有
const collapseAll = () => {
  if (!tableRef.value) return

  const collapseRows = (items) => {
    items.forEach(item => {
      if (item.type === 'group') {
        tableRef.value.toggleRowExpansion(item, false)
        if (item.children) {
          collapseRows(item.children)
        }
      }
    })
  }

  collapseRows(filteredInterfaces.value)
}

// 清空选择
const clearAll = () => {
  selectedPermissions.value = []
  tableRef.value?.clearSelection()
}

// 处理行选择
const handleSelect = (selection, row) => {
  const isSelected = selection.includes(row)

  if (row.type === 'group') {
    // 选择分组时，同时选择/取消选择所有子接口
    const permission = {
      resourceType: 'group',
      resourceId: row.id
    }

    if (isSelected) {
      selectedPermissions.value.push(permission)
      // 选择所有子接口
      if (row.children) {
        row.children.forEach(child => {
          if (child.status === 'enabled') {
            tableRef.value.toggleRowSelection(child, true)
          }
        })
      }
    } else {
      // 移除分组权限
      const index = selectedPermissions.value.findIndex(p =>
        p.resourceType === 'group' && p.resourceId === row.id
      )
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }

      // 取消选择所有子接口
      if (row.children) {
        row.children.forEach(child => {
          tableRef.value.toggleRowSelection(child, false)
          // 移除子接口权限
          const childIndex = selectedPermissions.value.findIndex(p =>
            p.resourceType === 'interface' && p.resourceId === child.id
          )
          if (childIndex > -1) {
            selectedPermissions.value.splice(childIndex, 1)
          }
        })
      }
    }
  } else {
    // 选择接口
    const permission = {
      resourceType: 'interface',
      resourceId: row.id
    }

    if (isSelected) {
      selectedPermissions.value.push(permission)
    } else {
      const index = selectedPermissions.value.findIndex(p =>
        p.resourceType === 'interface' && p.resourceId === row.id
      )
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    }
  }
}

// 处理全选
const handleSelectAll = (selection) => {
  // 处理当前页面的全选/取消全选
  const currentPageItems = filteredInterfaces.value

  if (selection.length === 0) {
    // 取消全选
    clearAll()
  } else {
    // 全选当前页面的所有可选项
    const selectItems = (items) => {
      items.forEach(item => {
        if (item.status === 'enabled') {
          const permission = {
            resourceType: item.type,
            resourceId: item.id
          }

          const exists = selectedPermissions.value.some(p =>
            p.resourceType === permission.resourceType && p.resourceId === permission.resourceId
          )

          if (!exists) {
            selectedPermissions.value.push(permission)
          }
        }

        if (item.children) {
          selectItems(item.children)
        }
      })
    }

    selectItems(currentPageItems)
  }
}

// 提交权限配置
const handleSubmit = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存权限配置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('权限配置保存成功')
    drawerMessenger.hideDrawer()

    // 刷新父页面数据
    // TODO: 触发父页面数据刷新

  } catch (error) {
    ElMessage.error('保存权限配置失败')
    console.error('保存权限配置失败:', error)
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer()
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '取消',
      handler: handleCancel
    },
    {
      text: '保存权限',
      type: 'primary',
      handler: handleSubmit,
      loading: saving.value,
      loadingText: '保存中...'
    }
  ]
}

// 监听saving状态，更新按钮状态
watch(saving, () => {
  updateDrawerButtons()
})

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.client-info-section {
  margin-bottom: 20px;
}

.client-info-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.client-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
}

.client-id {
  font-family: monospace;
  font-size: 13px;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

.permission-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 工具栏样式 */
.permission-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin: -20px -20px 0 -20px;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .selected-count {
      font-size: 12px;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

/* 搜索筛选栏样式 */
.search-filter-bar {
  display: flex;
  gap: 16px;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
  margin: 0 -20px;

  .search-box {
    flex: 1;

    .search-input {
      width: 100%;
    }
  }

  .filter-box {
    display: flex;
    gap: 12px;

    .filter-select {
      width: 120px;
    }
  }
}

.permission-table {
  flex: 1;
  margin: 0 -20px;
  padding: 0 20px;
  overflow: hidden;

  .el-table {
    height: 100%;
  }
}

.interface-info {
  padding: 4px 0;
}

.interface-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.method-tag {
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

.interface-name {
  font-weight: 500;
  color: #303133;
}

.interface-path {
  font-family: monospace;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.interface-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.permission-summary {
  margin-top: 16px;
}

.summary-content {
  display: flex;
  gap: 16px;
}
</style>
