<template>
  <div class="interface-config-form drawer-form-content">
    <div class="interface-config-container" :class="{ 'orm-maximized': isOrmMaximized }">
      <el-tabs v-model="activeTab" class="config-tabs">
        <el-tab-pane name="basic">
          <template #label>
            <span class="tab-label">
              <el-icon><Setting /></el-icon>
              基础信息
              <el-badge v-if="getTabStatus('basic')" :value="getTabStatus('basic')" class="tab-badge" />
            </span>
          </template>

          <el-form
            ref="basicFormRef"
            :model="formData"
            :rules="basicFormRules"
            label-width="100px"
            class="tab-form"
          >
            <div class="required-tip">
              <el-icon><InfoFilled /></el-icon>
              标有 <span class="required-mark">*</span> 的为必填项
            </div>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="数据源" prop="datasourceId" required>
                  <el-select
                    v-model="formData.datasourceId"
                    placeholder="请选择数据源"
                    @change="handleDataSourceChange"
                    style="width: 100%"
                    popper-class="drawer-select-dropdown"
                  >
                    <el-option
                      v-for="datasource in dataSources"
                      :key="datasource.id"
                      :label="datasource.name"
                      :value="datasource.id"
                    />
                  </el-select>
                  <!-- 调试信息 -->
                  <div v-if="dataSources.length === 0" style="color: red; font-size: 12px;">
                    数据源数据为空
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="接口分组" prop="groupId" required>
                  <el-select
                    v-model="formData.groupId"
                    placeholder="请选择接口分组"
                    @change="handleGroupChange"
                    style="width: 100%"
                    popper-class="drawer-select-dropdown"
                  >
                    <el-option
                      v-for="group in interfaceGroups"
                      :key="group.id"
                      :label="group.name"
                      :value="group.id"
                    />
                  </el-select>
                  <!-- 调试信息 -->
                  <div v-if="interfaceGroups.length === 0" style="color: red; font-size: 12px;">
                    接口分组数据为空
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="接口名称" prop="name" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入接口名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="16">
              <el-col :span="16">
                <el-form-item label="接口路径" required>
                  <div v-if="selectedGroupPrefix" class="path-input-group">
                    <div class="path-prefix">
                      <span>/api/v1/{{ selectedGroupPrefix }}</span>
                    </div>
                    <el-input
                      v-model="formData.path"
                      :placeholder="pathPlaceholder"
                      maxlength="200"
                      class="path-suffix-input"
                      @blur="handlePathBlur"
                      @input="handlePathInput"
                    />
                  </div>
                  <div v-else class="path-input-no-group">
                    <el-input
                      v-model="formData.path"
                      :placeholder="pathPlaceholder"
                      maxlength="200"
                      disabled
                    />
                  </div>
                  <div class="path-preview">
                    <span v-if="selectedGroupPrefix && formData.path">
                      完整路径：<code>/api/v1/{{ selectedGroupPrefix }}{{ formData.path.startsWith('/') ? formData.path : '/' + formData.path }}</code>
                    </span>
                    <span v-else-if="selectedGroupPrefix">
                      完整路径：<code>/api/v1/{{ selectedGroupPrefix }}/<span class="path-placeholder">请输入路径后缀</span></code>
                    </span>
                    <span v-else class="path-warning">
                      <el-icon><WarningFilled /></el-icon>
                      请先选择接口分组
                    </span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="HTTP方法" prop="method" required>
                  <el-select v-model="formData.method" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="method in httpMethods"
                      :key="method.value"
                      :label="method.label"
                      :value="method.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="接口状态">
                  <el-radio-group v-model="formData.isEnabled">
                    <el-radio :value="true">启用</el-radio>
                    <el-radio :value="false">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="访问权限">
                  <el-radio-group v-model="formData.isPublic">
                    <el-radio :value="false">私有</el-radio>
                    <el-radio :value="true">公开</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="接口描述">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入接口描述（可选）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <!-- 检测配置结果显示区域 -->
            <div v-if="validationResult && validationResult.message" class="validation-result-section">
              <div :class="['validation-result', validationResult.type]">
                <el-icon v-if="validationResult.type === 'success'"><SuccessFilled /></el-icon>
                <el-icon v-else><WarningFilled /></el-icon>
                <span>{{ validationResult.message }}</span>
              </div>
            </div>
          </el-form>
        </el-tab-pane>

        <el-tab-pane name="data">
          <template #label>
            <span class="tab-label">
              <el-icon><Coin /></el-icon>
              数据配置
              <el-badge v-if="getTabStatus('data')" :value="getTabStatus('data')" class="tab-badge" />
            </span>
          </template>

          <el-form
            ref="dataFormRef"
            :model="formData"
            :rules="dataFormRules"
            label-width="100px"
            class="tab-form"
          >
            <div class="required-tip">
              <el-icon><InfoFilled /></el-icon>
              配置数据表信息和ORM模型
            </div>

            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="数据表类型" prop="tableType" required>
                  <el-select v-model="formData.tableType" placeholder="请选择" style="width: 100%">
                    <el-option label="表" value="table" />
                    <el-option label="视图" value="view" />
                    <el-option label="存储过程" value="procedure" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item :label="getTableTypeLabel()" prop="tableName" required>
                  <el-input
                    v-model="formData.tableName"
                    :placeholder="getTableTypePlaceholder()"
                    maxlength="100"
                  />

                  <!-- 校验状态提示 - 移到输入框外面 -->
                  <div v-if="tableValidationStatus.isValidated" class="validation-status" style="margin-top: 8px;">
                    <el-tag
                      :type="tableValidationStatus.isValid ? 'success' : 'danger'"
                      size="small"
                      effect="light"
                    >
                      <el-icon style="margin-right: 4px;">
                        <Check v-if="tableValidationStatus.isValid" />
                        <Close v-else />
                      </el-icon>
                      {{ tableValidationStatus.isValid ? '校验成功' : '校验失败' }}
                    </el-tag>

                    <el-tag
                      v-if="needsRevalidation"
                      type="warning"
                      size="small"
                      effect="light"
                      style="margin-left: 8px;"
                    >
                      <el-icon style="margin-right: 4px;">
                        <Warning />
                      </el-icon>
                      需要重新校验
                    </el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <div class="config-section" :class="{ 'orm-maximized': isOrmMaximized }">
              <div class="config-section-header">
                <div class="config-section-title">
                  <el-icon><Setting /></el-icon>
                  ORM模型配置
                </div>
                <div class="orm-actions">
                  <el-button
                    size="small"
                    type="warning"
                    @click="handleValidateTableName"
                    :loading="loadingTables || generatingOrm"
                    :disabled="!formData.tableName || !formData.tableName.trim()"
                  >
                    <el-icon><Refresh /></el-icon>
                    {{ getValidateButtonText() }}
                  </el-button>
                  <el-button size="small" @click="handleOpenVisualConfig" type="success">
                    <el-icon><Setting /></el-icon>
                    可视化配置
                  </el-button>
                  <el-dropdown @command="handleOrmCommand" size="small">
                    <el-button size="small">
                      <el-icon><Tools /></el-icon>
                      更多操作
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="loadDemo">
                          <el-icon><Document /></el-icon>
                          加载示例
                        </el-dropdown-item>
                        <el-dropdown-item command="validateOrm">
                          <el-icon><SuccessFilled /></el-icon>
                          验证格式
                        </el-dropdown-item>
                        <el-dropdown-item command="copyJson">
                          <el-icon><DocumentAdd /></el-icon>
                          复制JSON
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleSaveOrm"
                    :disabled="!canEditOrm"
                  >
                    保存配置
                  </el-button>
                  <el-button size="small" @click="toggleOrmMaximize" :icon="isOrmMaximized ? 'Minus' : 'FullScreen'">
                    {{ isOrmMaximized ? '还原' : '最大化' }}
                  </el-button>
                </div>
              </div>
              <div class="config-description">
                配置数据表字段映射关系，定义数据模型结构和字段类型，设置返回数据的字段选择和格式化规则
              </div>
              <el-input
                v-model="ormConfig"
                type="textarea"
                :placeholder="canEditOrm ? '请输入ORM配置（JSON格式）' : (ormConfig && ormConfig.trim() ? '点击重新生成ORM按钮更新配置' : '请先校验表名称，校验成功后将自动生成ORM配置')"
                :readonly="!canEditOrm"
                class="orm-textarea-expanded"
                :class="{ 'readonly-textarea': !canEditOrm }"
              />
            </div>
          </el-form>
        </el-tab-pane>

        <el-tab-pane name="params">
          <template #label>
            <span class="tab-label">
              <el-icon><Tools /></el-icon>
              参数配置
            </span>
          </template>

          <div class="tab-form">
            <div class="required-tip">
              <el-icon><InfoFilled /></el-icon>
              配置接口的请求参数，包括路径参数、查询参数、请求头、请求体等
            </div>

            <!-- 参数配置区域 -->
            <div class="params-config-section">
              <div class="params-header">
                <span class="params-title">接口参数列表</span>
                <div class="params-actions">
                  <el-dropdown @command="handleAddCommonParam" trigger="click">
                    <el-button size="small">
                      <el-icon><DocumentAdd /></el-icon>
                      常用参数
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="page">page (页码)</el-dropdown-item>
                        <el-dropdown-item command="size">size (每页数量)</el-dropdown-item>
                        <el-dropdown-item command="keyword">keyword (关键词)</el-dropdown-item>
                        <el-dropdown-item command="status">status (状态)</el-dropdown-item>
                        <el-dropdown-item command="sort">sort (排序字段)</el-dropdown-item>
                        <el-dropdown-item command="order">order (排序方向)</el-dropdown-item>
                        <el-dropdown-item command="id">id (主键ID)</el-dropdown-item>
                        <el-dropdown-item command="token">token (认证令牌)</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button type="primary" size="small" @click="handleAddParam">
                    <el-icon><Plus /></el-icon>
                    添加参数
                  </el-button>
                </div>
              </div>

              <el-table :data="interfaceParams" style="width: 100%" size="small">
                <el-table-column label="参数名" width="120">
                  <template #default="{ row }">
                    <el-input v-model="row.name" placeholder="参数名" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="类型" width="90">
                  <template #default="{ row }">
                    <el-select v-model="row.type" placeholder="类型" size="small">
                      <el-option label="Query" value="query" />
                      <el-option label="Path" value="path" />
                      <el-option label="Header" value="header" />
                      <el-option label="Body" value="body" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="数据类型" width="90">
                  <template #default="{ row }">
                    <el-select v-model="row.dataType" placeholder="数据类型" size="small">
                      <el-option label="String" value="string" />
                      <el-option label="Number" value="number" />
                      <el-option label="Boolean" value="boolean" />
                      <el-option label="Array" value="array" />
                      <el-option label="Object" value="object" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="必填" width="60" align="center">
                  <template #default="{ row }">
                    <el-checkbox v-model="row.required" />
                  </template>
                </el-table-column>
                <el-table-column label="默认值" width="100">
                  <template #default="{ row }">
                    <el-input v-model="row.defaultValue" placeholder="默认值" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="描述" min-width="150">
                  <template #default="{ row }">
                    <el-input v-model="row.description" placeholder="参数描述" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="60" align="center">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDeleteParam($index)"
                      :icon="Delete"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane name="advanced">
          <template #label>
            <span class="tab-label">
              <el-icon><Setting /></el-icon>
              高级设置
            </span>
          </template>

          <div class="tab-form">
            <div class="required-tip">
              <el-icon><InfoFilled /></el-icon>
              配置接口的高级选项，包括缓存、频率限制、标签等
            </div>

            <!-- 高级设置区域 -->
            <div class="advanced-settings-section">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="缓存时长">
                    <el-input-number
                      v-model="formData.cacheDuration"
                      :min="0"
                      :max="86400"
                      placeholder="秒"
                      style="width: 100%"
                      size="small"
                    />
                    <div class="form-tip">设置为0表示不缓存</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="频率限制">
                    <el-input-number
                      v-model="formData.rateLimit"
                      :min="1"
                      :max="10000"
                      placeholder="次/分钟"
                      style="width: 100%"
                      size="small"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="接口标签">
                <el-select
                  v-model="formData.tags"
                  multiple
                  placeholder="选择接口标签"
                  style="width: 100%"
                  size="small"
                >
                  <el-option
                    v-for="tag in interfaceTags"
                    :key="tag.id"
                    :label="tag.name"
                    :value="tag.id"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 底部按钮已抽象到MainIndex中统一管理 -->

    <!-- 可视化配置已重构为抽屉组件 InterfaceVisualConfigForm.vue -->

  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from 'vue';
import { ElMessage, type FormInstance } from 'element-plus';
import { Setting, Coin, Tools, SuccessFilled, WarningFilled, Document, DocumentAdd, ArrowDown, Delete, Check, Close, Warning, Refresh } from '@element-plus/icons-vue';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore';

import dataSourceService from '@/services/datasource.service';
import interfaceGroupService from '@/services/interface-group.service';
import interfaceTagService from '@/services/interface-tag.service';
import interfaceConfigService from '@/services/interface-config.service';
import { PageRefresh } from '@/utils/pageRefreshUtil';
import { extractErrorMessage } from '@/utils/common-utils';
import { generateSqlAlchemyModel, generateCompleteOrmConfig, type TableStructure } from '@/utils/orm-generator';

// 全局抽屉状态管理
const drawerMessenger = useGlobalDrawerMessenger();
const drawerStore = useGlobalDrawerStore();

// 表单引用
const basicFormRef = ref<FormInstance>();
const dataFormRef = ref<FormInstance>();

// 加载状态
const submitting = ref(false);
const validating = ref(false);
const loadingTables = ref(false);
const generatingOrm = ref(false);

// 校验状态跟踪
const tableValidationStatus = ref<{
  isValidated: boolean;
  isValid: boolean;
  validatedTableName: string;
  validatedDataSourceId: number | null;
  validatedTableType: string;
}>({
  isValidated: false,
  isValid: false,
  validatedTableName: '',
  validatedDataSourceId: null,
  validatedTableType: 'table'
});



// 验证结果
const validationResult = ref<{ type: string; message: string } | null>(null);

// ORM配置
const ormConfig = ref('');

// 可视化配置相关变量已移至InterfaceVisualConfigForm.vue

// 页签状态
const activeTab = ref('basic');
const isOrmMaximized = ref(false);

// 计算属性 - 从抽屉store中获取数据
const isEdit = computed(() => drawerStore.props.isEdit || false);
const editId = computed(() => drawerStore.props.editId || null);
const editData = ref(null);

// 数据源、接口分组、接口标签数据
const dataSources = ref<any[]>([]);
const interfaceGroups = ref<any[]>([]);
const interfaceTags = ref<any[]>([]);

// 接口参数
const interfaceParams = ref<any[]>([]);

// 表单辅助数据
const selectedGroupPrefix = ref('');
const pathPlaceholder = ref('请先选择接口分组');

// HTTP方法选项
const httpMethods = ref([
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
]);

// 表单数据（使用camelCase命名）
const formData = reactive({
  datasourceId: null,
  groupId: null,
  name: '',
  path: '',
  method: 'GET',
  description: '',
  isEnabled: true,
  isPublic: false,
  tableType: 'table',
  tableName: '',
  cacheDuration: 300,
  rateLimit: 100,
  tags: [],
  // 更多字段将在后续添加
});

// 表单验证规则（使用camelCase字段名）
const basicFormRules = {
  datasourceId: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  groupId: [
    { required: true, message: '请选择接口分组', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
    { min: 1, max: 100, message: '接口名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入接口路径', trigger: 'blur' },
    { min: 1, max: 200, message: '接口路径长度在 1 到 200 个字符', trigger: 'blur' },
    {
      pattern: /^\/.*/,
      message: '接口路径必须以斜杠(/)开头',
      trigger: 'blur'
    }
  ],
  method: [
    { required: true, message: '请选择HTTP方法', trigger: 'change' }
  ]
};

const dataFormRules = {
  tableType: [
    { required: true, message: '请选择数据表类型', trigger: 'change' }
  ],
  tableName: [
    { required: true, message: '请输入数据表名', trigger: 'blur' },
    { min: 1, max: 100, message: '数据表名长度在 1 到 100 个字符', trigger: 'blur' }
  ]
};

// 更新抽屉底部按钮配置
const updateDrawerButtons = () => {
  const leftButtons = [
    {
      text: '检测配置',
      handler: handleValidateForm,
      loading: validating.value,
      loadingText: '检测中...'
    }
  ];

  const rightButtons = [
    {
      text: '取消',
      handler: handleCancel
    },
    {
      text: isEdit.value ? '更新' : '创建',
      type: 'primary' as const,
      handler: handleSubmit,
      loading: submitting.value
    }
  ];

  // 更新store中的按钮配置
  drawerStore.leftButtons = leftButtons;
  drawerStore.rightButtons = rightButtons;
};

// 页签状态检测
const getTabStatus = (tabName: string) => {
  switch (tabName) {
    case 'basic':
      const basicRequired = formData.name && formData.path && formData.datasourceId && formData.groupId && formData.method;
      return basicRequired ? '' : '!';
    case 'data':
      const dataRequired = formData.tableName && formData.tableType;
      return dataRequired ? '' : '!';
    default:
      return '';
  }
};

// 根据数据表类型获取标签名称
const getTableTypeLabel = () => {
  switch (formData.tableType) {
    case 'table':
      return '表名称';
    case 'view':
      return '视图名称';
    case 'procedure':
      return '存储过程名称';
    default:
      return '数据表名';
  }
};

// 根据数据表类型获取占位符
const getTableTypePlaceholder = () => {
  switch (formData.tableType) {
    case 'table':
      return '请输入表名称，如：users';
    case 'view':
      return '请输入视图名称，如：user_view';
    case 'procedure':
      return '请输入存储过程名称，如：get_user_info';
    default:
      return '请输入数据表名';
  }
};

// 数据源变化处理
const handleDataSourceChange = (value: any) => {
  console.log('数据源变化:', value);

  // 🔄 重置校验状态
  resetValidationStatus();
};

// 路径输入处理
const handlePathInput = (value: string) => {
  // 实时处理路径输入，确保格式正确
  if (value && !value.startsWith('/')) {
    formData.path = '/' + value;
  }
};

// 路径失焦处理
const handlePathBlur = () => {
  // 确保路径以斜杠开头
  if (formData.path && !formData.path.startsWith('/')) {
    formData.path = '/' + formData.path;
  }
  // 移除多余的斜杠
  if (formData.path) {
    formData.path = formData.path.replace(/\/+/g, '/');
  }
};

// 分组变化处理
const handleGroupChange = (groupId: number) => {
  const selectedGroup = interfaceGroups.value.find(g => g.id === groupId);
  if (selectedGroup) {
    selectedGroupPrefix.value = selectedGroup.pathPrefix;
    pathPlaceholder.value = `如：/list 或 /{id}`;

    // 清空路径输入，避免与之前选择的分组路径混合
    // 无论是新增还是编辑模式，切换分组时都应该清空路径
    formData.path = '';
    console.log('🔄 切换分组，已清空路径输入');
  } else {
    selectedGroupPrefix.value = '';
    pathPlaceholder.value = '请先选择接口分组';
    // 清空路径输入
    formData.path = '';
  }
  console.log('分组变化:', groupId, '前缀:', selectedGroupPrefix.value);
};

// 校验表名称/视图名称/存储过程名称
const handleValidateTableName = async () => {
  console.log('🔍 handleValidateTableName 被调用');
  console.log('🔍 当前表单数据:', {
    datasourceId: formData.datasourceId,
    tableName: formData.tableName,
    tableType: formData.tableType
  });

  // 如果已有ORM配置，直接重新生成
  if (ormConfig.value && ormConfig.value.trim()) {
    console.log('🔄 已有ORM配置，直接重新生成');
    await generateOrmModel();
    return;
  }

  // 🔧 增强验证：确保必填项都已填写
  if (!formData.datasourceId) {
    console.log('❌ 数据源ID为空');
    ElMessage.warning('请先选择数据源');
    return;
  }

  if (!formData.tableName || formData.tableName.trim() === '') {
    console.log('❌ 表名称为空');
    ElMessage.warning('请先输入名称');
    return;
  }

  loadingTables.value = true;
  try {
    // 调用后端校验接口
    const result = await validateTableName(formData.datasourceId, formData.tableName.trim(), formData.tableType);

    if (result.success && result.exists) {
      const typeName = getTableTypeLabel().replace('名称', '');

      // 🎯 更新校验状态
      tableValidationStatus.value = {
        isValidated: true,
        isValid: true,
        validatedTableName: formData.tableName.trim(),
        validatedDataSourceId: formData.datasourceId,
        validatedTableType: formData.tableType
      };

      ElMessage.success(`${typeName}校验成功，名称有效`);

      // 🚀 只有校验成功且表存在时才自动生成ORM模型
      console.log('✅ 表名称校验成功，开始自动生成ORM模型');
      await generateOrmModel();

    } else if (result.success && !result.exists) {
      const typeName = getTableTypeLabel().replace('名称', '');

      // 重置校验状态
      tableValidationStatus.value.isValidated = true;
      tableValidationStatus.value.isValid = false;

      ElMessage.error(`${typeName}校验失败，名称不存在`);
    } else {
      const typeName = getTableTypeLabel().replace('名称', '');

      // 重置校验状态
      tableValidationStatus.value.isValidated = true;
      tableValidationStatus.value.isValid = false;

      ElMessage.error(`${typeName}校验失败：${result.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('校验失败:', error);

    // 重置校验状态
    tableValidationStatus.value.isValidated = true;
    tableValidationStatus.value.isValid = false;

    ElMessage.error(extractErrorMessage(error, '校验失败'));
  } finally {
    loadingTables.value = false;
  }
};

// 校验表名称的API调用
const validateTableName = async (datasourceId: number, tableName: string, tableType: string): Promise<any> => {
  try {
    console.log('🔍 校验表名称参数:', { datasourceId, tableName, tableType });

    // 🔧 调用真正的后端校验接口
    const result = await dataSourceService.validateTableName(datasourceId, tableName, tableType);
    console.log('🔍 校验结果:', result);

    return result;
  } catch (error) {
    console.error('🔍 校验API调用失败:', error);
    throw error;
  }
};

// 🚀 自动生成ORM模型
const generateOrmModel = async () => {
  // 如果已有ORM配置，允许直接重新生成（跳过校验检查）
  const hasExistingOrm = ormConfig.value && ormConfig.value.trim();

  if (!hasExistingOrm) {
    // 🎯 新建时的严格检查：只有校验成功的表才能生成ORM
    if (!tableValidationStatus.value.isValidated || !tableValidationStatus.value.isValid) {
      console.log('❌ 表名称未校验或校验失败，不能生成ORM模型');
      ElMessage.warning('请先校验表名称成功后再生成ORM模型');
      return;
    }

    // 🎯 检查当前配置是否与校验时一致
    if (tableValidationStatus.value.validatedTableName !== formData.tableName.trim() ||
        tableValidationStatus.value.validatedDataSourceId !== formData.datasourceId ||
        tableValidationStatus.value.validatedTableType !== formData.tableType) {
      console.log('❌ 表配置已修改，需要重新校验');
      ElMessage.warning('表名称或数据源已修改，请重新校验后再生成ORM模型');
      return;
    }
  } else {
    console.log('🔄 已有ORM配置，允许直接重新生成');
  }

  if (!formData.datasourceId || !formData.tableName) {
    return;
  }

  generatingOrm.value = true;
  try {
    console.log('🚀 开始生成ORM模型...', {
      datasourceId: formData.datasourceId,
      tableName: formData.tableName.trim(),
      tableType: formData.tableType,
      validationStatus: tableValidationStatus.value
    });

    // 获取表结构
    const structureResult = await dataSourceService.getTableStructure(
      formData.datasourceId,
      formData.tableName.trim(),
      formData.tableType
    );

    console.log('📋 表结构结果:', structureResult);

    if (structureResult.success && structureResult.columns?.length > 0) {
      // 🚀 生成完整的ORM配置（包含查询能力）
      const generatedOrmConfig = generateCompleteOrmConfig(
        formData.tableName.trim(),
        structureResult as TableStructure,
        [formData.method], // 使用用户选择的HTTP方法
        {
          datasource_id: formData.datasourceId,
          db_type: 'mssql', // 您使用的是MSSQL
          table_type: formData.tableType
        }
      );

      if (generatedOrmConfig.error) {
        console.log('⚠️ ORM配置生成失败:', generatedOrmConfig.error);
        ElMessage.warning(`无法生成ORM模型：${generatedOrmConfig.error}`);
        return;
      }

      // 生成SQLAlchemy代码用于显示
      const ormCode = generateSqlAlchemyModel(
        formData.tableName.trim(),
        structureResult as TableStructure
      );

      // 🎯 写入到ORM字段（显示在textarea中）
      ormConfig.value = JSON.stringify(generatedOrmConfig, null, 2);

      // 保存完整的ORM配置到表单数据（用于提交）
      formData.ormModelConfig = JSON.stringify(generatedOrmConfig, null, 2);

      console.log('✅ ORM模型和配置生成成功', {
        ormCode: ormCode.substring(0, 200) + '...',
        ormConfig: generatedOrmConfig,
        textareaContent: ormConfig.value.substring(0, 200) + '...'
      });

      ElMessage.success('ORM模型已自动生成，包含查询能力配置');
    } else {
      console.log('⚠️ 无法获取表结构:', structureResult.message);
      ElMessage.warning(`无法生成ORM模型：${structureResult.message || '获取表结构失败'}`);
    }
  } catch (error: any) {
    console.error('❌ ORM模型生成失败:', error);
    ElMessage.error(`ORM模型生成失败：${extractErrorMessage(error, '未知错误')}`);
  } finally {
    generatingOrm.value = false;
  }
};

// 🔄 重置校验状态
const resetValidationStatus = () => {
  tableValidationStatus.value = {
    isValidated: false,
    isValid: false,
    validatedTableName: '',
    validatedDataSourceId: null,
    validatedTableType: 'table'
  };
};

// 监听表名称变化
watch(() => formData.tableName, (newValue, oldValue) => {
  if (newValue !== oldValue && tableValidationStatus.value.isValidated) {
    // 如果表名称改变且之前已校验过，重置校验状态
    resetValidationStatus();
  }
});

// 监听表类型变化
watch(() => formData.tableType, (newValue, oldValue) => {
  if (newValue !== oldValue && tableValidationStatus.value.isValidated) {
    // 如果表类型改变且之前已校验过，重置校验状态
    resetValidationStatus();
  }
});

// 检查是否需要重新校验
const needsRevalidation = computed(() => {
  if (!tableValidationStatus.value.isValidated) return false;

  return (
    tableValidationStatus.value.validatedTableName !== formData.tableName ||
    tableValidationStatus.value.validatedDataSourceId !== formData.datasourceId ||
    tableValidationStatus.value.validatedTableType !== formData.tableType
  );
});

// 检查是否允许保存
const canSave = computed(() => {
  // 如果没有填写表名称，不需要校验
  if (!formData.tableName || !formData.datasourceId) {
    return true; // 让表单验证处理必填项
  }

  // 如果填写了表名称，必须校验成功
  return tableValidationStatus.value.isValidated &&
         tableValidationStatus.value.isValid &&
         !needsRevalidation.value;
});

// 获取校验按钮文本
const getValidateButtonText = () => {
  if (loadingTables.value || generatingOrm.value) {
    return '生成中...';
  }

  // 如果已有ORM配置，显示"重新生成ORM"
  if (ormConfig.value && ormConfig.value.trim()) {
    return '重新生成ORM';
  }

  // 第一次使用，显示"生成ORM"
  return '生成ORM';
};

// 检查是否可以生成ORM
const canGenerateOrm = computed(() => {
  return tableValidationStatus.value.isValidated &&
         tableValidationStatus.value.isValid &&
         !needsRevalidation.value;
});

// 检查是否可以编辑ORM配置
const canEditOrm = computed(() => {
  // 编辑模式下，如果已有ORM配置，允许编辑
  if (isEdit.value && ormConfig.value && ormConfig.value.trim()) {
    return true;
  }
  // 新建模式下，需要校验成功才能编辑
  return validationResult.value?.type === 'success';
});



// 获取HTTP方法说明
const getMethodNote = (method: string) => {
  switch (method) {
    case 'GET':
      return 'GET请求：用于查询数据，字段配置专注于查询性能，不需要数据库约束';
    case 'POST':
      return 'POST请求：用于创建数据，需要完整的字段约束（unique、nullable、index等）确保数据完整性';
    case 'PUT':
      return 'PUT请求：用于更新数据，需要完整的字段约束确保数据完整性和唯一性';
    case 'DELETE':
      return 'DELETE请求：用于删除数据，只需要标识字段，不需要完整约束';
    default:
      return '请选择合适的HTTP方法以获得对应的ORM配置';
  }
};

// 根据HTTP方法生成字段配置
const generateFieldsByMethod = (method: string) => {
  const baseFields = [
    {
      "name": "id",
      "type": "Integer",
      "comment": "用户ID - 主键，自动递增",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "username",
      "type": "String(50)",
      "comment": "用户名 - 唯一标识，支持登录认证",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "email",
      "type": "String(100)",
      "comment": "邮箱地址 - 用于通知和找回密码",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "phone",
      "type": "String(20)",
      "comment": "手机号码 - 可选，用于短信通知",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "password_hash",
      "type": "String(255)",
      "comment": "密码哈希 - 加密存储，不可查询",
      "source": "auto_generated",
      "enabled": false
    },
    {
      "name": "status",
      "type": "Integer",
      "comment": "用户状态 - 0:禁用 1:启用 2:待审核",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "created_at",
      "type": "DateTime",
      "comment": "创建时间 - 记录用户注册时间",
      "source": "auto_generated",
      "enabled": true
    },
    {
      "name": "updated_at",
      "type": "DateTime",
      "comment": "更新时间 - 记录最后修改时间",
      "source": "auto_generated",
      "enabled": false
    }
  ];

  // 根据HTTP方法添加不同的约束
  if (method === 'POST' || method === 'PUT') {
    // POST/PUT需要完整的数据库约束
    return {
      "fields": baseFields.map(field => {
        const constraints: any = { ...field };

        switch (field.name) {
          case 'id':
            constraints.primary_key = true;
            constraints.autoincrement = true;
            constraints.nullable = false;
            break;
          case 'username':
            constraints.unique = true;        // 唯一约束
            constraints.nullable = false;     // 必须填写
            constraints.index = true;         // 创建索引
            break;
          case 'email':
            constraints.unique = true;        // 唯一约束
            constraints.nullable = false;     // 必须填写
            constraints.index = true;         // 创建索引
            break;
          case 'phone':
            constraints.nullable = true;      // 可选字段
            constraints.index = true;         // 创建索引
            break;
          case 'password_hash':
            constraints.nullable = false;     // 必须填写
            break;
          case 'status':
            constraints.default = 1;          // 默认值
            constraints.nullable = false;     // 必须填写
            constraints.index = true;         // 创建索引
            break;
          case 'created_at':
            constraints.default = "func.now()";
            constraints.nullable = false;
            break;
          case 'updated_at':
            constraints.default = "func.now()";
            constraints.onupdate = "func.now()";
            constraints.nullable = false;
            break;
        }
        return constraints;
      })
    };
  } else {
    // GET/DELETE只需要基础字段定义，不需要约束
    return {
      "fields": baseFields.map(field => ({
        ...field,
        // GET请求只保留基础信息，不需要数据库约束
        "query_purpose": method === 'GET' ? 'read_only' : 'identification'
      }))
    };
  }
};

// 根据HTTP方法生成查询映射配置
const generateQueryMappingByMethod = (method: string) => {
  if (method === 'GET') {
    // GET请求：完整的查询配置
    return {
      "fuzzy_search_fields": ["username", "email", "phone"],  // 支持模糊搜索的字段
      "exact_match_fields": ["status", "id"],                 // 支持精确匹配的字段
      "range_query_fields": ["created_at"],                   // 支持范围查询的字段
      "default_sort": "id",                                   // 默认排序字段
      "default_order": "desc",                                // 默认排序方向
      "allowed_sort_fields": ["id", "username", "created_at", "status"]  // 允许排序的字段
    };
  } else if (method === 'POST') {
    // POST请求：通常不需要复杂查询，主要是数据验证
    return {
      "validation_fields": ["username", "email"],             // 需要验证唯一性的字段
      "required_fields": ["username", "email", "password_hash"], // 必填字段
      "default_values": {                                     // 默认值
        "status": 1,
        "created_at": "func.now()"
      }
    };
  } else if (method === 'PUT') {
    // PUT请求：需要标识字段和可更新字段
    return {
      "identifier_fields": ["id", "username"],               // 用于标识记录的字段
      "updatable_fields": ["email", "phone", "status"],      // 可更新的字段
      "validation_fields": ["username", "email"],            // 需要验证唯一性的字段
      "auto_update_fields": ["updated_at"]                   // 自动更新的字段
    };
  } else if (method === 'DELETE') {
    // DELETE请求：只需要标识字段
    return {
      "identifier_fields": ["id", "username"],               // 用于标识要删除记录的字段
      "soft_delete_field": "status",                         // 软删除字段（可选）
      "cascade_rules": []                                     // 级联删除规则
    };
  } else {
    // 默认配置
    return {
      "note": "请选择具体的HTTP方法以获得相应的查询配置"
    };
  }
};

// 加载ORM示例
const handleLoadOrmDemo = () => {
  // 根据当前接口的HTTP方法生成不同的ORM配置
  const currentMethod = formData.method || 'GET';

  const demoConfig = {
    // ==================== 基础配置 ====================
    "model_name": "User",
    "table_name": "users",
    "description": "用户信息表",
    "http_method": currentMethod,
    "method_note": getMethodNote(currentMethod),

    // ==================== SQLAlchemy模型定义（根据HTTP方法区分） ====================
    "sqlalchemy_model": generateFieldsByMethod(currentMethod),

    // ==================== 查询映射配置（根据HTTP方法区分） ====================
    "query_mapping": generateQueryMappingByMethod(currentMethod),

    // ==================== 响应配置 ====================
    "response_config": {
      "exclude_fields": ["password_hash"],     // 响应中排除的字段
      "date_format": "%Y-%m-%d %H:%M:%S",     // 日期格式
      "include_total_count": true,            // 是否包含总数
      "null_to_empty": false                  // 是否将null转为空字符串
    },

    // ==================== 配置元数据 ====================
    "config_metadata": {
      "version": "1.0",
      "last_auto_generated": new Date().toISOString(),
      "last_manual_edited": null,
      "source": "demo"  // demo | auto_generated | manual | mixed
    }
  };

  ormConfig.value = JSON.stringify(demoConfig, null, 2);
  ElMessage.success('已加载ORM配置示例');
};



// 打开可视化配置
const handleOpenVisualConfig = () => {
  console.log('🔧 点击了可视化配置按钮');

  // 准备传递给抽屉的数据
  let ormConfigData = null;
  if (ormConfig.value && ormConfig.value.trim()) {
    try {
      ormConfigData = JSON.parse(ormConfig.value);
    } catch (error) {
      console.error('ORM配置JSON解析失败:', error);
      ElMessage.error('ORM配置格式错误，请检查JSON格式');
      return;
    }
  }

  const editData = {
    ...formData,  // 包含所有表单数据
    ormModelConfig: ormConfigData,  // 使用正确的字段名和对象格式
    orm_config: ormConfig.value     // 保留原始字符串格式作为备用
  };

  console.log('🔧 打开可视化配置，传递数据：', editData);
  console.log('🔧 当前抽屉状态：', drawerStore.visible, drawerStore.title);

  drawerMessenger.showDrawer({
    title: '字段查询配置',
    component: 'InterfaceVisualConfigForm',
    props: {
      isEdit: true,
      editData: editData
    },
    size: '45%',
    isSecond: true  // 使用第二层抽屉
  });

  console.log('🔧 调用showDrawer后，抽屉状态：', drawerStore.visible, drawerStore.title);
};

// 处理可视化配置结果
const handleVisualConfigResult = (result: any) => {
  try {
    console.log('🔍 接收到可视化配置结果：', result);
    console.log('🔍 result.visualFields:', result.visualFields);
    console.log('🔍 result.queryMapping:', result.queryMapping);

    // 解析当前ORM配置
    let config;
    try {
      config = ormConfig.value ? JSON.parse(ormConfig.value) : {};
      console.log('🔍 解析的ORM配置：', config);
    } catch (parseError) {
      console.error('❌ ORM配置JSON解析失败：', parseError);
      ElMessage.error('当前ORM配置格式错误，无法应用可视化配置');
      return;
    }

    // 支持多种字段名格式（camelCase 和 snake_case）
    const sqlalchemyModelKey = config.sqlalchemyModel ? 'sqlalchemyModel' : 'sqlalchemy_model';
    const queryMappingKey = config.queryMapping ? 'queryMapping' : 'query_mapping';

    // 更新字段定义
    if (!config[sqlalchemyModelKey]) {
      config[sqlalchemyModelKey] = {};
    }

    // 只保留启用的字段
    if (result.visualFields && Array.isArray(result.visualFields)) {
      const enabledFields = result.visualFields.filter((field: any) => field.enabled);
      config[sqlalchemyModelKey].fields = enabledFields;
      console.log('🔍 更新后的字段：', enabledFields);
    }

    // 更新查询映射配置
    if (!config[queryMappingKey]) {
      config[queryMappingKey] = {};
    }

    if (result.queryMapping) {
      config[queryMappingKey] = {
        ...config[queryMappingKey],
        ...result.queryMapping
      };
      console.log('🔍 更新后的查询映射：', config[queryMappingKey]);
    }

    // 更新ORM配置
    ormConfig.value = JSON.stringify(config, null, 2);
    console.log('✅ ORM配置更新成功');

    ElMessage.success('可视化配置已应用到ORM配置');
  } catch (error) {
    console.error('❌ 应用配置失败：', error);
    console.error('❌ 错误详情：', error.message);
    console.error('❌ 错误堆栈：', error.stack);
    ElMessage.error(`应用配置失败：${error.message}`);
  }
};

// 暴露方法到window对象供子抽屉调用
onMounted(() => {
  (window as any).handleVisualConfigResult = handleVisualConfigResult;
});



// 可视化配置相关方法已移至InterfaceVisualConfigForm.vue





// 保存ORM配置
const handleSaveOrm = () => {
  if (!ormConfig.value.trim()) {
    ElMessage.warning('请输入ORM配置');
    return;
  }
  try {
    const parsedConfig = JSON.parse(ormConfig.value);
    // 同步到formData
    formData.ormModelConfig = ormConfig.value;
    ElMessage.success('ORM配置保存成功');
  } catch (error) {
    ElMessage.error('ORM配置格式错误，请检查JSON格式');
  }
};

// 监听ORM配置变化，自动同步到formData
watch(ormConfig, (newValue) => {
  if (newValue && newValue.trim()) {
    try {
      JSON.parse(newValue);
      formData.ormModelConfig = newValue;
    } catch (error) {
      // JSON格式错误时不同步
    }
  }
}, { deep: true });

// 切换ORM最大化
const toggleOrmMaximize = () => {
  isOrmMaximized.value = !isOrmMaximized.value;
};

// 处理ORM操作命令
const handleOrmCommand = (command: string) => {
  switch (command) {
    case 'loadDemo':
      handleLoadOrmDemo();
      break;
    case 'validateOrm':
      handleValidateOrm();
      break;
    case 'copyJson':
      handleCopyOrmJson();
      break;
  }
};

// 验证ORM格式
const handleValidateOrm = () => {
  if (!ormConfig.value.trim()) {
    ElMessage.warning('请输入ORM配置');
    return;
  }

  try {
    JSON.parse(ormConfig.value);
    ElMessage.success('ORM配置格式正确');
  } catch (error) {
    ElMessage.error('ORM配置格式错误，请检查JSON格式');
  }
};

// 复制ORM JSON到剪贴板
const handleCopyOrmJson = async () => {
  if (!ormConfig.value) {
    ElMessage.warning('当前没有ORM配置可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(ormConfig.value);
    ElMessage.success('ORM配置已复制到剪贴板');
  } catch (error) {
    // 如果剪贴板API不可用，使用传统方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = ormConfig.value;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('ORM配置已复制到剪贴板');
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制');
    }
  }
};

// 添加参数
const handleAddParam = () => {
  interfaceParams.value.push({
    name: '',
    type: 'query',
    dataType: 'string',
    required: false,
    defaultValue: '',
    description: ''
  });
};

// 删除参数
const handleDeleteParam = (index: number) => {
  interfaceParams.value.splice(index, 1);
};

// 添加常用参数
const handleAddCommonParam = (command: string) => {
  const commonParams: Record<string, any> = {
    page: {
      name: 'page',
      type: 'query',
      dataType: 'number',
      required: false,
      defaultValue: '1',
      description: '页码'
    },
    size: {
      name: 'size',
      type: 'query',
      dataType: 'number',
      required: false,
      defaultValue: '10',
      description: '每页数量'
    },
    keyword: {
      name: 'keyword',
      type: 'query',
      dataType: 'string',
      required: false,
      defaultValue: '',
      description: '搜索关键词'
    },
    status: {
      name: 'status',
      type: 'query',
      dataType: 'string',
      required: false,
      defaultValue: '',
      description: '状态'
    },
    sort: {
      name: 'sort',
      type: 'query',
      dataType: 'string',
      required: false,
      defaultValue: 'id',
      description: '排序字段'
    },
    order: {
      name: 'order',
      type: 'query',
      dataType: 'string',
      required: false,
      defaultValue: 'desc',
      description: '排序方向'
    },
    id: {
      name: 'id',
      type: 'path',
      dataType: 'number',
      required: true,
      defaultValue: '',
      description: '主键ID'
    },
    token: {
      name: 'token',
      type: 'header',
      dataType: 'string',
      required: true,
      defaultValue: '',
      description: '认证令牌'
    }
  };

  if (commonParams[command]) {
    interfaceParams.value.push({ ...commonParams[command] });
  }
};

// 检测配置
const handleValidateForm = async () => {
  validating.value = true;
  validationResult.value = null;

  try {
    // 检查必填项
    const missingFields = [];
    if (!formData.name) missingFields.push('接口名称');
    if (!formData.path) missingFields.push('接口路径');
    if (!formData.datasourceId) missingFields.push('数据源');
    if (!formData.groupId) missingFields.push('接口分组');
    if (!formData.method) missingFields.push('HTTP方法');
    if (!formData.tableName) missingFields.push('数据表名');
    if (!formData.tableType) missingFields.push('数据表类型');

    if (missingFields.length > 0) {
      validationResult.value = {
        type: 'error',
        message: `发现未填写的必填项：${missingFields.join('、')}`
      };
      // 切换到第一个有错误的tab
      if (missingFields.some(field => ['接口名称', '接口路径', '数据源', '接口分组', 'HTTP方法'].includes(field))) {
        activeTab.value = 'basic';
      } else if (missingFields.some(field => ['数据表名', '数据表类型'].includes(field))) {
        activeTab.value = 'data';
      }
      ElMessage.warning(`发现未填写的必填项：${missingFields.join('、')}`);
    } else {
      validationResult.value = {
        type: 'success',
        message: '配置检测通过！所有必填项都已填写完整。'
      };
      ElMessage.success('配置检测通过！');
    }
  } catch (error: any) {
    console.error('检测失败:', error);
    validationResult.value = {
      type: 'error',
      message: '检测过程中出现错误'
    };
    ElMessage.error('检测过程中出现错误');
  } finally {
    validating.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer();
};

// 提交表单
const handleSubmit = async () => {
  if (!basicFormRef.value || !dataFormRef.value) return;

  try {
    // 分别验证各个表单，提供具体的错误定位
    let basicFormValid = false;
    let dataFormValid = false;
    let validationErrors = [];

    // 验证基础信息表单
    try {
      await basicFormRef.value.validate();
      basicFormValid = true;
    } catch (error) {
      validationErrors.push('基础信息页签有必填项未填写');
      console.log('基础信息表单验证失败:', error);
    }

    // 验证数据配置表单
    try {
      await dataFormRef.value.validate();
      dataFormValid = true;
    } catch (error) {
      validationErrors.push('数据配置页签有必填项未填写');
      console.log('数据配置表单验证失败:', error);
    }

    // 额外的手动验证
    if (!formData.tableName || formData.tableName.trim() === '') {
      validationErrors.push('数据配置页签：请输入数据表名');
      dataFormValid = false;
    }

    // 🎯 ORM配置检查 - 改为检查ORM配置内容而不是表名校验
    if (!ormConfig.value || !ormConfig.value.trim()) {
      validationErrors.push('数据配置页签：请生成ORM配置后再提交');
      dataFormValid = false;
    } else {
      // 验证ORM配置是否为有效的JSON格式
      try {
        JSON.parse(ormConfig.value);
      } catch (error) {
        validationErrors.push('数据配置页签：ORM配置格式错误，请检查JSON格式');
        dataFormValid = false;
      }
    }

    if (!formData.name || formData.name.trim() === '') {
      validationErrors.push('基础信息页签：请输入接口名称');
      basicFormValid = false;
    }

    if (!formData.path || formData.path.trim() === '') {
      validationErrors.push('基础信息页签：请输入接口路径');
      basicFormValid = false;
    }

    if (!formData.groupId) {
      validationErrors.push('基础信息页签：请选择接口分组');
      basicFormValid = false;
    }

    if (!formData.datasourceId) {
      validationErrors.push('基础信息页签：请选择数据源');
      basicFormValid = false;
    }

    // 如果有验证错误，显示具体信息并自动切换到有错误的页签
    if (validationErrors.length > 0) {
      // 自动切换到第一个有错误的页签
      if (!basicFormValid) {
        activeTab.value = 'basic';
        ElMessage.error(`请完善基础信息页签的必填项：${validationErrors.filter(err => err.includes('基础信息')).map(err => err.replace('基础信息页签：', '')).join('、')}`);
      } else if (!dataFormValid) {
        activeTab.value = 'data';
        ElMessage.error(`请完善数据配置页签的必填项：${validationErrors.filter(err => err.includes('数据配置')).map(err => err.replace('数据配置页签：', '')).join('、')}`);
      } else {
        // 如果有其他错误
        ElMessage.error(`表单验证失败：${validationErrors.join('；')}`);
      }

      return;
    }

    submitting.value = true;

    // 确保路径以斜杠开头
    let finalPath = formData.path;
    if (finalPath && !finalPath.startsWith('/')) {
      finalPath = '/' + finalPath;
    }

    // 拼接完整路径：/api/v1/{groupPrefix}{path}
    let fullPath = finalPath;
    if (selectedGroupPrefix.value) {
      // 🔧 检查路径是否已经是完整路径，避免重复拼接
      const expectedPrefix = `/api/v1/${selectedGroupPrefix.value}`;
      if (finalPath.startsWith(expectedPrefix)) {
        // 路径已经是完整路径，直接使用
        fullPath = finalPath;
        console.log('🔧 路径已是完整路径，直接使用:', fullPath);
      } else {
        // 路径是用户输入的部分，需要拼接前缀
        fullPath = `${expectedPrefix}${finalPath}`;
        console.log('🔧 拼接完整路径:', {
          前缀: expectedPrefix,
          用户路径: finalPath,
          完整路径: fullPath
        });
      }
    }

    // 准备提交数据
    const submitData = {
      name: formData.name,
      path: fullPath, // 使用拼接后的完整路径
      method: formData.method,
      groupId: formData.groupId,
      datasourceId: formData.datasourceId,
      tableName: formData.tableName,
      tableType: formData.tableType, // 添加表类型字段
      description: formData.description,
      isEnabled: formData.isEnabled,
      isPublic: formData.isPublic,
      tags: formData.tags,
      cacheDuration: formData.cacheDuration,
      rateLimit: formData.rateLimit
    };

    // 添加ORM配置到提交数据
    if (ormConfig.value && ormConfig.value.trim()) {
      try {
        // 验证JSON格式并添加到提交数据
        const parsedOrmConfig = JSON.parse(ormConfig.value);
        submitData.ormModelConfig = parsedOrmConfig;
        console.log('✅ ORM配置已添加到提交数据');
      } catch (error) {
        console.error('❌ ORM配置JSON格式错误:', error);
        ElMessage.error('ORM配置格式错误，请检查JSON格式');
        return;
      }
    }

    if (isEdit.value && editData.value) {
      // 更新接口配置
      console.log('🔄 开始更新接口配置，ID:', editData.value.id);
      console.log('🔄 提交数据:', submitData);

      const result = await interfaceConfigService.updateInterfaceConfig(editData.value.id, submitData);
      console.log('✅ 更新接口配置成功，返回结果:', result);

      ElMessage.success('接口配置更新成功');

      // 触发刷新机制（修改操作：保持当前页）
      PageRefresh.interfaceConfig.afterEdit();
    } else {
      // 创建接口配置
      console.log('🔄 开始创建接口配置');
      console.log('🔄 提交数据:', submitData);

      const result = await interfaceConfigService.createInterfaceConfig(submitData);
      console.log('✅ 创建接口配置成功，返回结果:', result);

      ElMessage.success('接口配置创建成功');

      // 触发刷新机制（新增操作：跳转第一页）
      PageRefresh.interfaceConfig.afterAdd();
    }

    drawerMessenger.hideDrawer();
  } catch (error: any) {
    console.error('提交失败:', error);

    // 智能错误消息提取
    let errorMessage = '操作失败';
    if (error?.message) {
      errorMessage = error.message;
    } else if (error?.detail) {
      errorMessage = error.detail;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    ElMessage.error(errorMessage);
  } finally {
    submitting.value = false;
  }
};

// 根据ID获取编辑数据
const loadEditData = async () => {
  if (!isEdit.value || !editId.value) {
    console.log('🔄 非编辑模式或无editId，跳过数据加载');
    return;
  }

  try {
    console.log('🔄 根据ID获取编辑数据:', editId.value);
    const data = await interfaceConfigService.getInterfaceConfigById(editId.value);
    console.log('🔍 API返回的编辑数据:', data);

    if (data) {
      editData.value = data;
      console.log('✅ 编辑数据已设置:', editData.value);
    } else {
      console.error('❌ 未获取到编辑数据');
      ElMessage.error('获取接口配置详情失败');
    }
  } catch (error) {
    console.error('❌ 获取编辑数据失败:', error);
    ElMessage.error('获取接口配置详情失败');
  }
};

// 初始化数据
const initFormData = () => {
  console.log('🔄 initFormData 开始执行');
  console.log('🔄 isEdit:', isEdit.value);
  console.log('🔄 editData:', editData.value);

  if (isEdit.value && editData.value) {
    console.log('🔄 编辑模式，设置表单数据:', {
      datasourceId: editData.value.datasourceId,
      datasource_id: editData.value.datasource_id,
      groupId: editData.value.groupId,
      group_id: editData.value.group_id,
      name: editData.value.name,
      '完整editData': editData.value
    });

    // 详细检查每个字段的值和类型
    console.log('🔍 字段详细检查:', {
      'editData.value.datasourceId': {
        value: editData.value.datasourceId,
        type: typeof editData.value.datasourceId,
        exists: editData.value.hasOwnProperty('datasourceId')
      },
      'editData.value.datasource_id': {
        value: editData.value.datasource_id,
        type: typeof editData.value.datasource_id,
        exists: editData.value.hasOwnProperty('datasource_id')
      },
      'editData.value.groupId': {
        value: editData.value.groupId,
        type: typeof editData.value.groupId,
        exists: editData.value.hasOwnProperty('groupId')
      },
      'editData.value.group_id': {
        value: editData.value.group_id,
        type: typeof editData.value.group_id,
        exists: editData.value.hasOwnProperty('group_id')
      }
    });

    // 先计算要赋值的数据
    let datasourceIdValue = editData.value.datasourceId || editData.value.datasource_id || null;
    let groupIdValue = editData.value.groupId || editData.value.group_id || null;

    // 如果是字符串，转换为数字
    if (datasourceIdValue && typeof datasourceIdValue === 'string') {
      datasourceIdValue = parseInt(datasourceIdValue, 10);
      console.log('🔄 datasourceId 字符串转数字:', datasourceIdValue);
    }

    if (groupIdValue && typeof groupIdValue === 'string') {
      groupIdValue = parseInt(groupIdValue, 10);
      console.log('🔄 groupId 字符串转数字:', groupIdValue);
    }

    console.log('🔍 计算出的赋值数据:', {
      datasourceIdValue: { value: datasourceIdValue, type: typeof datasourceIdValue },
      groupIdValue: { value: groupIdValue, type: typeof groupIdValue }
    });

    Object.assign(formData, {
      // 使用后端返回的真实数据，兼容snake_case和camelCase
      datasourceId: datasourceIdValue,
      groupId: groupIdValue,
      name: editData.value.name || '',
      path: editData.value.path || '',
      method: editData.value.method || 'GET',
      description: editData.value.description || '',
      isEnabled: editData.value.isEnabled !== undefined ? editData.value.isEnabled : true,
      isPublic: editData.value.isPublic !== undefined ? editData.value.isPublic : false,
      tableType: editData.value.tableType || editData.value.table_type || 'table',
      tableName: editData.value.tableName || editData.value.table_name || '',
      cacheDuration: editData.value.cacheDuration || 300,
      rateLimit: editData.value.rateLimit || 100,
      tags: editData.value.tags || [],
    });

    console.log('✅ 表单数据已设置:', {
      datasourceId: formData.datasourceId,
      groupId: formData.groupId,
      name: formData.name
    });

    // 检查数据源是否能找到，兼容snake_case和camelCase
    const realDatasourceId = editData.value.datasourceId || editData.value.datasource_id;
    if (realDatasourceId) {
      const selectedDataSource = dataSources.value.find(ds => ds.id === realDatasourceId);
      console.log('🔍 查找数据源:', {
        searchId: realDatasourceId,
        searchType: typeof realDatasourceId,
        availableDataSources: dataSources.value.map(ds => ({ id: ds.id, type: typeof ds.id, name: ds.name })),
        foundDataSource: selectedDataSource
      });
    }

    // 延迟检查表单数据是否真的被设置了
    setTimeout(() => {
      console.log('🔍 延迟检查表单数据:', {
        datasourceId: formData.datasourceId,
        groupId: formData.groupId,
        name: formData.name
      });
    }, 100);

    // 设置分组前缀 - 使用后端返回的真实groupId，兼容snake_case和camelCase
    const realGroupId = editData.value.groupId || editData.value.group_id;
    if (realGroupId) {
      const selectedGroup = interfaceGroups.value.find(g => g.id === realGroupId);
      console.log('🔍 查找分组:', {
        searchId: realGroupId,
        searchType: typeof realGroupId,
        availableGroups: interfaceGroups.value.map(g => ({ id: g.id, type: typeof g.id, name: g.name })),
        foundGroup: selectedGroup
      });

      if (selectedGroup) {
        selectedGroupPrefix.value = selectedGroup.pathPrefix;
        pathPlaceholder.value = `如：/list 或 /{id}`;
        console.log('✅ 分组前缀已设置:', selectedGroupPrefix.value);

        // 🔧 编辑模式：从完整路径中提取用户输入的部分
        if (formData.path) {
          const fullPathPrefix = `/api/v1/${selectedGroup.pathPrefix}`;
          if (formData.path.startsWith(fullPathPrefix)) {
            // 提取用户输入的路径部分
            const userPath = formData.path.substring(fullPathPrefix.length);
            formData.path = userPath || '/';
            console.log('🔧 编辑模式：从完整路径提取用户部分:', {
              原始完整路径: editData.value.path,
              前缀: fullPathPrefix,
              提取的用户路径: formData.path
            });
          }
        }
      } else {
        console.log('⚠️ 未找到对应的分组');
      }
    }

    // 加载ORM配置 - 使用自动转换后的驼峰格式字段名
    console.log('🔍 调试editData中的ORM相关字段:', {
      ormModelConfig: !!editData.value.ormModelConfig,
      ormConfig: !!editData.value.ormConfig,
      allKeys: Object.keys(editData.value).filter(k => k.toLowerCase().includes('orm'))
    });

    const ormData = editData.value.ormModelConfig || editData.value.ormConfig;
    if (ormData) {
      ormConfig.value = typeof ormData === 'string'
        ? ormData
        : JSON.stringify(ormData, null, 2);
      console.log('✅ ORM配置已加载:', ormConfig.value.substring(0, 200) + '...');
    } else {
      console.log('⚠️ 未找到ORM配置数据');
    }

    // 加载参数配置
    if (editData.value.parameterConfig && editData.value.parameterConfig.customParameters) {
      interfaceParams.value = [...editData.value.parameterConfig.customParameters];
    }
  } else {
    // 重置为默认值
    Object.assign(formData, {
      datasourceId: null,
      groupId: null,
      name: '',
      path: '',
      method: 'GET',
      description: '',
      isEnabled: true,
      isPublic: false,
      tableType: 'table',
      tableName: '',
      cacheDuration: 300,
      rateLimit: 100,
      tags: [],
    });

    // 重置其他数据
    ormConfig.value = '';
    interfaceParams.value = [];
    selectedGroupPrefix.value = '';
    pathPlaceholder.value = '请先选择接口分组';
  }

  // 清除验证结果
  validationResult.value = null;

  // 清除验证状态
  if (basicFormRef.value) {
    basicFormRef.value.clearValidate();
  }
  if (dataFormRef.value) {
    dataFormRef.value.clearValidate();
  }
};

// 加载基础数据
const loadBasicData = async () => {
  try {
    console.log('🔄 开始加载基础数据...');

    // 并行加载所有基础数据（限制在100以内，用于下拉选择）
    const [dataSourcesResult, interfaceGroupsResult, interfaceTagsResult] = await Promise.all([
      dataSourceService.getDataSources(1, 100), // 获取数据源（限制100条）
      interfaceGroupService.getInterfaceGroups({ page: 1, pageSize: 100 }), // 获取分组（限制100条）
      interfaceTagService.getInterfaceTags({ page: 1, page_size: 100 }) // 获取标签（限制100条）
    ]);

    // 设置数据（注意：所有服务都返回带items字段的分页响应）
    dataSources.value = dataSourcesResult.items || [];
    interfaceGroups.value = interfaceGroupsResult.items || [];
    interfaceTags.value = interfaceTagsResult.items || [];

    console.log('✅ 基础数据已加载:', {
      dataSources: dataSources.value.length,
      interfaceGroups: interfaceGroups.value.length,
      interfaceTags: interfaceTags.value.length
    });

    // 如果是编辑模式，确保选中的数据源和分组在列表中
    if (isEdit.value && editData.value) {
      const selectedDataSource = dataSources.value.find(ds => ds.id === editData.value?.datasourceId);
      const selectedGroup = interfaceGroups.value.find(g => g.id === editData.value?.groupId);

      console.log('🎯 编辑模式数据定位:', {
        selectedDataSource: selectedDataSource?.name,
        selectedGroup: selectedGroup?.name,
        editDataSourceId: editData.value.datasourceId,
        editGroupId: editData.value.groupId
      });
    }

  } catch (error: any) {
    console.error('💥 加载基础数据失败:', error);

    // 智能错误消息提取
    let errorMessage = '加载基础数据失败';
    if (error?.message) {
      errorMessage = `加载基础数据失败: ${error.message}`;
    } else if (error?.detail) {
      errorMessage = `加载基础数据失败: ${error.detail}`;
    } else if (typeof error === 'string') {
      errorMessage = `加载基础数据失败: ${error}`;
    }

    ElMessage.error(errorMessage);
  }
};

// 监听抽屉store中的数据变化，确保编辑数据正确加载
watch(() => drawerStore.props, async (newProps) => {
  if (newProps) {
    // 1. 先加载基础数据（数据源、分组、标签）
    await loadBasicData();

    // 2. 如果是编辑模式，根据ID加载编辑数据
    if (isEdit.value && editId.value) {
      await loadEditData();
    }

    // 3. 初始化表单数据
    initFormData();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 监听加载状态变化，更新按钮
watch([validating, submitting, isEdit], () => {
  updateDrawerButtons();
});

// 组件挂载时初始化
onMounted(async () => {
  // 1. 先加载基础数据（数据源、分组、标签）
  await loadBasicData();

  // 2. 如果是编辑模式，根据ID加载编辑数据
  if (isEdit.value && editId.value) {
    await loadEditData();
  }

  // 3. 初始化表单数据
  initFormData();
  updateDrawerButtons(); // 初始化按钮配置
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.interface-config-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.interface-config-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.tab-badge {
  margin-left: 4px;
}

.tab-form {
  height: 100%;
}

.required-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  margin-bottom: 20px;
  font-size: 13px;
  color: #1e40af;
}

.required-mark {
  color: #f56565;
  font-weight: bold;
}

/* 2024-12-27: 验证结果样式已抽象到 page-common.scss，使用公共样式 */
/*
.validation-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;

  &.success {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    color: #0369a1;

    .el-icon {
      color: #10b981;
    }
  }

  &.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;

    .el-icon {
      color: #ef4444;
    }
  }
}
*/

/* 验证结果区域样式 - 保留，为业务特定样式 */
.validation-result-section {
  margin-top: 16px;
}

/* 配置区域样式 */
.config-section {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3FC8DD;
}

.config-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: nowrap;
  gap: 5px;
}

.config-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 15px;
  white-space: nowrap;
  flex-shrink: 0;
}

.config-description {
  margin-bottom: 12px;
  font-size: 13px;
  color: #6b7280;
}

.orm-actions {
  display: flex;
  gap: 2px;
  flex-wrap: nowrap;
  align-items: center;
  flex-shrink: 1;
  min-width: 0;

  .el-button {
    font-size: 13px;
    padding: 5px 8px;
    white-space: nowrap;
    min-width: auto;
    margin-left: 0 !important; /* 覆盖Element Plus默认的margin-left */

    .el-icon {
      font-size: 13px;
    }
  }

  .el-dropdown .el-button {
    font-size: 13px;
    padding: 5px 6px;
    margin-left: 0 !important; /* 覆盖Element Plus默认的margin-left */
  }
}

.orm-textarea-expanded {
  min-height: 300px;

  :deep(.el-textarea__inner) {
    min-height: 300px !important;
    resize: vertical;
    /* 标准滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #9db7bd #f1f5f9;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #9db7bd;
      border-radius: 2px;
      transition: background 0.3s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #7a9ca3;
    }
  }
}

/* ORM只读状态样式 */
.readonly-textarea {
  :deep(.el-textarea__inner) {
    background-color: #f5f7fa !important;
    color: #909399 !important;
    cursor: not-allowed !important;
    border-color: #dcdfe6 !important;
  }
}

/* ORM最大化时的样式 */
.orm-maximized {
  .orm-textarea-expanded {
    min-height: 500px;

    :deep(.el-textarea__inner) {
      min-height: 500px !important;
    }
  }
}

/* 参数配置样式 */
.params-config-section {
  margin-top: 16px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.params-title {
  font-weight: 600;
  color: #374151;
}

.params-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 高级设置样式 */
.advanced-settings-section {
  margin-top: 16px;
}

/* 2024-12-27: 表单提示样式已抽象到 page-common.scss，使用公共样式 */
/*
.form-tip {
  font-size: 12px;
  color: #6b7280; // 原颜色，现已统一为 #909399
  margin-top: 4px;
}
*/

/* 可视化配置样式已移至InterfaceVisualConfigForm.vue */

/* 确保抽屉内的下拉菜单有足够高的z-index */
:deep(.el-select-dropdown) {
  z-index: 10000 !important;
}

:deep(.el-dropdown-menu) {
  z-index: 10000 !important;
}

:deep(.el-popper) {
  z-index: 10000 !important;
}

/* 抽屉中的下拉菜单样式 */
.drawer-select-dropdown {
  z-index: 10001 !important;
}

/* 路径输入组件样式 */
.path-input-group {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  .path-prefix {
    background: #f5f7fa;
    padding: 0 12px;
    height: 32px;
    display: flex;
    align-items: center;
    border-right: 1px solid #dcdfe6;
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }

  .path-suffix-input {
    flex: 1;

    :deep(.el-input__wrapper) {
      border: none;
      box-shadow: none;
    }
  }
}

.path-input-no-group {
  .el-input {
    :deep(.el-input__wrapper) {
      background-color: #f5f7fa;
    }
  }
}

.path-preview {
  margin-top: 8px;
  font-size: 13px;

  code {
    background: #f1f2f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #2d3748;
  }

  .path-placeholder {
    color: #a0a0a0;
    font-style: italic;
  }

  .path-warning {
    color: #e6a23c;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  // 页签错误状态样式
  .tab-badge {
    :deep(.el-badge__content) {
      background-color: #f56c6c;
      border-color: #f56c6c;
      font-size: 10px;
      height: 16px;
      line-height: 16px;
      padding: 0 4px;
      min-width: 16px;
    }
  }

  // 页签切换动画
  .config-tabs {
    :deep(.el-tabs__nav-wrap) {
      transition: all 0.3s ease;
    }

    :deep(.el-tabs__item) {
      transition: all 0.3s ease;

      &.is-active {
        animation: tabHighlight 0.5s ease;
      }
    }
  }

  @keyframes tabHighlight {
    0% {
      background-color: transparent;
    }
    50% {
      background-color: #e6f7ff;
    }
    100% {
      background-color: transparent;
    }
  }



  // ORM模型配置按钮间距调整
  .orm-actions {
    display: flex;
    align-items: center;
    gap: 3px; // 设置按钮间距为3px

    .el-button {
      margin-left: 0; // 移除默认的margin
    }
  }

}
</style>
