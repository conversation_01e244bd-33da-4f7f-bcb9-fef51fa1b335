import os
import logging
from logging.handlers import TimedRotatingFileHandler
import datetime

class LazyFileHandler(TimedRotatingFileHandler):
    """延迟创建文件的日志处理器，只有在实际写入日志时才创建文件"""

    def __init__(self, *args, **kwargs):
        self._file_created = False
        super().__init__(*args, **kwargs)
        # 关闭初始化时创建的文件
        if self.stream:
            self.stream.close()
            self.stream = None

    def emit(self, record):
        """只有在实际写入日志时才创建文件"""
        if not self._file_created:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.baseFilename), exist_ok=True)
            self._file_created = True
        super().emit(record)

def setup_logging():
    """仅执行一次的日志初始化"""
    if hasattr(setup_logging, '_executed'):
        return
    setup_logging._executed = True

    # 创建日志目录 - 适配项目结构
    log_dir = os.path.join(os.path.dirname(__file__), "..", "..", "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 获取当前日期
    current_date = datetime.datetime.now().strftime("%Y%m%d")

    # 创建日志处理器 - 每日动态生成文件名，使用延迟创建
    def create_handler(log_type: str):
        return LazyFileHandler(
            filename=f"{log_dir}/{log_type}_{current_date}.log",
            when='midnight',  # 每天午夜自动滚动
            backupCount=30,   # 保留30个历史文件
            encoding="utf-8"
        )
    
    # 创建6个独立的日志处理器
    handlers = {
        # 异常记录日志（由异常处理系统调用）
        'biz': create_handler('biz'),      # 业务异常记录
        'tech': create_handler('tech'),    # 技术异常记录

        # 调试日志（4个级别完全独立）
        'debug': create_handler('debug'),
        'info': create_handler('info'),
        'warning': create_handler('warning'),
        'error': create_handler('error'),
    }
    
    # 创建日志格式器 - 多行格式，每条日志间有分隔线
    class MultiLineFormatter(logging.Formatter):
        def format(self, record):
            # 格式化时间
            timestamp = self.formatTime(record, self.datefmt)

            # 创建日志头部
            header = f"[{record.levelname}] {timestamp}"

            # 创建位置信息
            location = f"位置: {record.filename}:{record.lineno} -> {record.funcName}()"

            # 获取消息内容
            message = record.getMessage()

            # 组装完整的日志条目，每条日志后添加分隔线
            lines = [
                header,
                location,
                f"消息: {message}",
                "─" * 50  # 分隔线
            ]

            return '\n'.join(lines)

    formatter = MultiLineFormatter(datefmt="%Y-%m-%d %H:%M:%S")
    
    # 创建6个完全独立的记录器
    loggers = {}

    # 清除根记录器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.setLevel(logging.DEBUG)

    for name, handler in handlers.items():
        handler.setFormatter(formatter)

        # 为每种日志类型创建完全独立的记录器
        logger_name = f"mapi.{name}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
        logger.handlers.clear()
        logger.addHandler(handler)
        logger.propagate = False  # 完全独立，不传播到其他记录器

        # 设置处理器的最低级别（但记录器会控制具体记录什么）
        handler.setLevel(logging.DEBUG)

        loggers[name] = logger

    # 存储记录器引用供LogUtil使用
    setup_logging._loggers = loggers
