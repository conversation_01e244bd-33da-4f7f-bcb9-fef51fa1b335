<!-- 
  PaginationComponent - 通用分页组件
  功能: 提供标准的分页控制，支持页码切换、每页条数调整，并通过事件与父组件通信
  Props:
    currentPage - 当前页码
    pageSize - 每页条数
    total - 总记录数
    pageSizes - 可选的每页条数列表
    layout - 分页布局配置
    background - 是否显示背景
    small - 是否使用小型分页样式
  Events:
    update:currentPage - 页码变更事件
    update:pageSize - 每页条数变更事件
    sizeChange - 每页条数改变时触发
    currentChange - 页码改变时触发
-->
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="currentPageModel"
      v-model:page-size="pageSizeModel"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  currentPage: number;
  pageSize: number;
  total: number;
  pageSizes?: number[];
  layout?: string;
  background?: boolean;
  small?: boolean;
}

interface Emits {
  (e: 'update:currentPage', value: number): void;
  (e: 'update:pageSize', value: number): void;
  (e: 'sizeChange', size: number): void;
  (e: 'currentChange', page: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  small: false
});

const emit = defineEmits<Emits>();

// 双向绑定
const currentPageModel = computed({
  get: () => props.currentPage,
  set: (value: number) => emit('update:currentPage', value)
});

const pageSizeModel = computed({
  get: () => props.pageSize,
  set: (value: number) => emit('update:pageSize', value)
});

// 事件处理
const handleSizeChange = (size: number) => {
  emit('sizeChange', size);
};

const handleCurrentChange = (page: number) => {
  emit('currentChange', page);
};
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
