"""
接口管理模块开发模板 - API Management Module Template
用于接口管理相关功能开发的标准模板

模块结构：
- Controller层：处理HTTP请求，参数验证，响应格式化
- Service层：业务逻辑处理，异常处理
- Repository层：数据访问，数据库操作
- Model层：数据模型定义

开发规范：
1. 严格遵循异常处理规范
2. 使用LogUtil记录日志
3. 测试文件放在tests目录下
4. 保持代码整洁，及时清理临时文件

前后端对接规范：
1. 后端严格使用下划线命名，前端使用驼峰命名
2. 前端通过common-utils工具进行命名转换，后端不添加alias
3. 后端返回业务响应格式：{success, message, error_code, detail}
4. Router层根据error_code设置HTTP状态码(409/404/500等)
5. 业务错误必须记录到业务日志(biz_*.log)
6. 前端使用相对路径(/api/v1/xxx)，通过Vite代理转发
7. 修改vite.config.ts代理配置后必须重启前端服务器

前端页面组件规范：
1. 所有列表页面必须添加EmptyState空状态组件
2. 支持搜索和无数据两种状态
3. 在el-table中添加template #empty插槽
4. 导入EmptyState组件并实现clearSearch方法
5. 这是强制性的用户体验规范，不允许遗漏

作者：系统生成
日期：2025-07-21
"""

from typing import Optional, List, Dict, Any
from app.shared.core.exception_handler import BusinessException, TechnicalException
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil


class ApiManagementService:
    """
    接口管理服务类模板
    用于管理API接口的增删改查等操作
    """
    
    def __init__(self, repository):
        """
        初始化服务
        
        Args:
            repository: 数据访问层实例
        """
        self.repository = repository
        LogUtil.debug("接口管理服务初始化", service="ApiManagementService")
    
    def get_api_list(self, page: int = 1, size: int = 10, 
                     category: Optional[str] = None, 
                     status: Optional[str] = None,
                     search: Optional[str] = None) -> Dict[str, Any]:
        """
        获取接口列表
        
        Args:
            page: 页码
            size: 每页大小
            category: 接口分类
            status: 接口状态
            search: 搜索关键词
            
        Returns:
            接口列表响应数据
        """
        try:
            LogUtil.debug("开始获取接口列表", 
                         operation="get_api_list",
                         page=page, 
                         size=size, 
                         category=category,
                         status=status,
                         search=search)
            
            # 参数验证
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )
            
            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )
            
            # 获取数据
            apis, total = self.repository.get_api_list(page, size, category, status, search)
            
            LogUtil.info("接口列表获取成功", 
                        operation="get_api_list",
                        total_count=total,
                        returned_count=len(apis),
                        page=page,
                        size=size)
            
            return {
                "apis": apis,
                "total": total,
                "page": page,
                "size": size,
                "filters": {
                    "category": category,
                    "status": status,
                    "search": search
                }
            }
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_api_list",
                    "error": str(e),
                    "params": {
                        "page": page, 
                        "size": size, 
                        "category": category,
                        "status": status,
                        "search": search
                    }
                }
            )
    
    def create_api(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新接口
        
        Args:
            api_data: 接口数据
            
        Returns:
            创建的接口数据
        """
        try:
            LogUtil.debug("开始创建接口", 
                         operation="create_api",
                         api_name=api_data.get("name"),
                         api_path=api_data.get("path"))
            
            # 必填字段验证
            required_fields = ["name", "path", "method", "category"]
            for field in required_fields:
                if not api_data.get(field):
                    raise BusinessException(
                        user_message=f"{field}不能为空",
                        user_detail={"field": field, "value": api_data.get(field)},
                        error_type=ErrorType.验证错误
                    )
            
            # 检查接口路径是否重复
            if self.repository.check_api_path_exists(api_data["path"], api_data["method"]):
                raise BusinessException(
                    user_message="接口路径已存在",
                    user_detail={
                        "path": api_data["path"],
                        "method": api_data["method"],
                        "suggestion": "请使用不同的路径或方法"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 创建接口
            new_api = self.repository.create_api(api_data)
            
            LogUtil.info("接口创建成功", 
                        operation="create_api",
                        api_id=new_api.get("id"),
                        api_name=new_api.get("name"),
                        api_path=new_api.get("path"))
            
            return new_api
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "create_api",
                    "error": str(e),
                    "api_data": api_data
                }
            )


# ==================== 接口管理模块开发指南 ====================
"""
接口管理模块开发指南：

1. 模块功能：
   - 接口注册和管理
   - 接口分类和标签
   - 接口状态管理（启用/禁用）
   - 接口文档管理
   - 接口权限控制
   - 接口调用统计

2. 数据模型设计：
   - api_id: 接口唯一标识
   - name: 接口名称
   - path: 接口路径
   - method: HTTP方法（GET/POST/PUT/DELETE）
   - category: 接口分类
   - description: 接口描述
   - status: 接口状态（active/inactive）
   - created_at: 创建时间
   - updated_at: 更新时间

3. 业务规则：
   - 接口路径+方法组合必须唯一
   - 接口名称在同一分类下必须唯一
   - 删除接口前需要检查是否有依赖关系
   - 接口状态变更需要记录操作日志

4. 异常处理：
   - 参数验证失败 -> BusinessException
   - 资源不存在 -> BusinessException
   - 资源冲突 -> BusinessException
   - 数据库错误 -> TechnicalException
   - 网络错误 -> TechnicalException

5. 日志记录：
   - 关键操作使用info级别
   - 调试信息使用debug级别
   - 异常情况使用warning/error级别
   - 业务异常自动记录到biz日志
   - 技术异常自动记录到tech日志
"""
