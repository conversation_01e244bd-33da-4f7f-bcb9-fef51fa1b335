"""
数据源路由层
定义API路由和端点
"""

from fastapi import APIRouter, Depends, Query, Path
from typing import Optional
from app.config.datasource.controllers.data_source_controller import DataSourceController
from app.config.datasource.schemas.data_source_schema import (
    DataSourceCreate,  # 数据源创建请求模型
    DataSourceUpdate,  # 数据源更新请求模型
    DataSourceResponse,  # 单个数据源响应模型
    DataSourceListResponse,  # 数据源列表响应模型
    ConnectionTestResponse  # 连接测试响应模型
)

# 创建路由器
router = APIRouter()

@router.get("/", response_model=DataSourceListResponse, summary="获取数据源列表")
async def get_data_sources(
    page: int = Query(1, ge=1, description="页码"),  # 页码，默认1，最小值1
    size: int = Query(10, ge=1, le=100, description="每页大小"),  # 每页大小，默认10，最小值1，最大值100
    search: Optional[str] = Query(None, description="搜索关键词（名称、描述、主机地址）"),  # 搜索关键词，可选
    status: Optional[str] = Query(None, description="状态过滤（active/inactive/error）"),  # 状态过滤，可选
    db_type: Optional[str] = Query(None, description="数据库类型过滤"),  # 数据库类型过滤，可选
    controller: DataSourceController = Depends()  # 依赖注入数据源控制器    
):
    """
    获取数据源列表
    
    支持分页、搜索和过滤功能：
    - **page**: 页码，从1开始
    - **size**: 每页大小，1-100之间
    - **search**: 搜索关键词，匹配名称、描述、主机地址
    - **status**: 状态过滤，可选值：active、inactive、error
    - **db_type**: 数据库类型过滤，可选值：mysql、postgresql、sqlserver、oracle、sqlite
    """
    return await controller.get_data_sources(page, size, search, status, db_type)

@router.post("/", summary="创建数据源")
async def create_data_source(
    data_source_data: DataSourceCreate,
    controller: DataSourceController = Depends()
):
    """
    创建新的数据源
    
    创建数据源时需要提供：
    - **name**: 数据源名称（唯一）
    - **db_type**: 数据库类型
    - **host**: 主机地址
    - **port**: 端口号
    - **database**: 数据库名
    - **username**: 用户名
    - **password**: 密码（将自动加密存储）
    - **max_connections**: 最大连接数（可选，默认10）
    - **connection_timeout**: 连接超时时间（可选，默认60秒）
    - **refresh_time**: 定时刷新时间（可选，格式：HH:MM）
    - **description**: 描述信息（可选）
    """
    return await controller.create_data_source(data_source_data)

@router.get("/{data_source_id}", response_model=DataSourceResponse, summary="获取单个数据源")
async def get_data_source(
    data_source_id: int = Path(..., description="数据源ID"),
    controller: DataSourceController = Depends()
):
    """
    根据ID获取单个数据源的详细信息
    
    - **data_source_id**: 数据源的唯一标识符
    """
    return await controller.get_data_source(data_source_id)

@router.put("/{data_source_id}", response_model=DataSourceResponse, summary="更新数据源")
async def update_data_source(
    data_source_id: int = Path(..., description="数据源ID"),
    data_source_data: DataSourceUpdate = ...,
    controller: DataSourceController = Depends()
):
    """
    更新指定ID的数据源
    
    可以部分更新，只需要提供需要修改的字段：
    - **name**: 数据源名称
    - **description**: 描述信息
    - **db_type**: 数据库类型
    - **host**: 主机地址
    - **port**: 端口号
    - **database**: 数据库名
    - **username**: 用户名
    - **password**: 密码
    - **max_connections**: 最大连接数
    - **connection_timeout**: 连接超时时间
    - **refresh_time**: 定时刷新时间
    - **status**: 状态
    """
    return await controller.update_data_source(data_source_id, data_source_data)

@router.delete("/{data_source_id}", summary="删除数据源")
async def delete_data_source(
    data_source_id: int = Path(..., description="数据源ID"),
    controller: DataSourceController = Depends()
):
    """
    删除指定ID的数据源

    注意：删除前会检查是否有接口正在使用此数据源

    - **data_source_id**: 要删除的数据源ID
    """
    result = await controller.delete_data_source(data_source_id)

    # 根据业务结果设置HTTP状态码
    if isinstance(result, dict) and result.get("success") is False:
        from fastapi import HTTPException, status
        from fastapi.responses import JSONResponse

        if result.get("error_code") == "RESOURCE_CONFLICT":
            # 资源冲突，返回409状态码
            return JSONResponse(
                status_code=status.HTTP_409_CONFLICT,
                content=result
            )
        elif result.get("error_code") == "RESOURCE_NOT_FOUND":
            # 资源不存在，返回404状态码
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content=result
            )

    return result


@router.get("/{data_source_id}/test-connection", response_model=ConnectionTestResponse, summary="测试已保存数据源连接")
async def test_saved_data_source_connection(
    data_source_id: int,
    controller: DataSourceController = Depends()
):
    """
    测试已保存数据源的连接是否正常

    根据数据源ID从数据库获取连接参数，解密密码后测试连接：
    - **data_source_id**: 数据源ID

    返回连接测试结果，包括是否成功、响应时间和错误详情
    """
    return await controller.test_saved_data_source_connection(data_source_id)


@router.post("/{data_source_id}/validate-table", summary="校验表/视图/存储过程名称")
async def validate_table_name(
    data_source_id: int = Path(..., description="数据源ID"),
    table_name: str = Query(..., description="表/视图/存储过程名称"),
    table_type: str = Query("table", description="类型：table/view/procedure"),
    controller: DataSourceController = Depends()
):
    """
    校验指定数据源中的表/视图/存储过程名称是否存在

    路径参数：
    - **data_source_id**: 数据源ID

    查询参数：
    - **table_name**: 要校验的名称
    - **table_type**: 类型，可选值：table（表）、view（视图）、procedure（存储过程）

    返回校验结果，包含是否存在、类型信息等
    """
    return await controller.validate_table_name(data_source_id, table_name, table_type)


@router.get("/{data_source_id}/table-structure", summary="获取表结构信息")
async def get_table_structure(
    data_source_id: int = Path(..., description="数据源ID"),
    table_name: str = Query(..., description="表名称"),
    table_type: str = Query("table", description="类型：table/view"),
    controller: DataSourceController = Depends()
):
    """
    获取指定数据源中的表结构信息

    路径参数：
    - **data_source_id**: 数据源ID

    查询参数：
    - **table_name**: 表名称
    - **table_type**: 类型，可选值：table（表）、view（视图）

    返回表结构信息，包含字段列表、类型、注释等
    """
    return await controller.get_table_structure(data_source_id, table_name, table_type)


