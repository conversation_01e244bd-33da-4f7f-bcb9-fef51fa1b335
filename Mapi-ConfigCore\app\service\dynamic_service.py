"""
动态服务层
处理核心业务逻辑，ORM解析，数据库操作
"""

import json
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app.shared.core.log_util import LogUtil
from app.service.dynamic_model import DynamicModel
from app.service.dynamic_schema import DynamicSchema
from app.config.interface.repositories.interface_config_repository import InterfaceConfigRepository

class DynamicService:
    """动态服务 - 处理核心业务逻辑"""
    
    def __init__(self, db: Session):
        self.db = db
        self.interface_repo = InterfaceConfigRepository(db)
        self.dynamic_model = DynamicModel()
        self.dynamic_schema = DynamicSchema()
    
    async def query_data(self, interface_path: str, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询数据
        
        Args:
            interface_path: 接口路径
            query_params: 查询参数
            
        Returns:
            查询结果
        """
        try:
            LogUtil.info("开始查询数据", interface_path=interface_path, query_params=query_params)
            
            # 1. 根据路径解析接口配置ID
            interface_config_id = self._parse_interface_config_id(interface_path)
            if not interface_config_id:
                return self._error_response("无效的接口路径")
            
            # 2. 获取接口配置
            interface_config = self.interface_repo.get_by_id(interface_config_id)
            if not interface_config:
                return self._error_response("接口配置不存在")
            
            # 3. 解析ORM配置
            LogUtil.info("开始解析ORM配置", interface_config_id=interface_config_id,
                        has_orm_config=bool(interface_config.orm_model_config))
            orm_config = self._parse_orm_config(interface_config.orm_model_config)
            if not orm_config:
                LogUtil.error("ORM配置解析失败", interface_config_id=interface_config_id,
                             orm_model_config=interface_config.orm_model_config)
                return self._error_response("ORM配置解析失败")
            
            # 4. 验证查询参数
            validated_params = self.dynamic_schema.validate_query_params(orm_config, query_params)
            
            # 5. 构建并执行查询
            LogUtil.info("开始执行查询", interface_config_id=interface_config_id,
                        datasource_id=interface_config.datasource_id,
                        table_name=orm_config.get("table_name"),
                        validated_params=validated_params)
            result = await self.dynamic_model.execute_query(
                interface_config.datasource_id,
                orm_config,
                validated_params
            )
            LogUtil.info("查询执行完成", interface_config_id=interface_config_id,
                        result_success=result.get("success") if result else False)
            
            # 6. 格式化响应数据
            formatted_result = self.dynamic_schema.format_response(orm_config, result)

            # 安全地获取记录数量
            data = formatted_result.get("data", []) if formatted_result else []
            record_count = len(data) if data else 0

            LogUtil.info("查询数据完成", interface_config_id=interface_config_id,
                        record_count=record_count)

            return formatted_result
            
        except Exception as e:
            LogUtil.error("查询数据失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"查询失败: {str(e)}")
    
    async def create_data(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建数据
        
        Args:
            interface_path: 接口路径
            data: 创建数据
            query_params: 查询参数
            
        Returns:
            创建结果
        """
        try:
            LogUtil.info("开始创建数据", interface_path=interface_path, data_keys=list(data.keys()))
            
            # 1. 解析接口配置
            interface_config_id = self._parse_interface_config_id(interface_path)
            if not interface_config_id:
                return self._error_response("无效的接口路径")
            
            interface_config = self.interface_repo.get_by_id(interface_config_id)
            if not interface_config:
                return self._error_response("接口配置不存在")
            
            # 2. 解析ORM配置
            orm_config = self._parse_orm_config(interface_config.orm_model_config)
            if not orm_config:
                return self._error_response("ORM配置解析失败")
            
            # 3. 验证创建数据
            validated_data = self.dynamic_schema.validate_create_data(orm_config, data)
            
            # 4. 执行创建操作
            result = await self.dynamic_model.execute_insert(
                interface_config.datasource_id,
                orm_config,
                validated_data
            )
            
            LogUtil.info("创建数据完成", interface_config_id=interface_config_id)
            return result
            
        except Exception as e:
            LogUtil.error("创建数据失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"创建失败: {str(e)}")
    
    async def update_data(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新数据
        
        Args:
            interface_path: 接口路径
            data: 更新数据
            query_params: 查询参数（更新条件）
            
        Returns:
            更新结果
        """
        try:
            LogUtil.info("开始更新数据", interface_path=interface_path, data_keys=list(data.keys()))
            
            # 1. 解析接口配置
            interface_config_id = self._parse_interface_config_id(interface_path)
            if not interface_config_id:
                return self._error_response("无效的接口路径")
            
            interface_config = self.interface_repo.get_by_id(interface_config_id)
            if not interface_config:
                return self._error_response("接口配置不存在")
            
            # 2. 解析ORM配置
            orm_config = self._parse_orm_config(interface_config.orm_model_config)
            if not orm_config:
                return self._error_response("ORM配置解析失败")
            
            # 3. 验证更新数据和条件
            validated_data = self.dynamic_schema.validate_update_data(orm_config, data)
            validated_conditions = self.dynamic_schema.validate_update_conditions(orm_config, query_params)
            
            # 4. 执行更新操作
            result = await self.dynamic_model.execute_update(
                interface_config.datasource_id,
                orm_config,
                validated_data,
                validated_conditions
            )
            
            LogUtil.info("更新数据完成", interface_config_id=interface_config_id)
            return result
            
        except Exception as e:
            LogUtil.error("更新数据失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"更新失败: {str(e)}")
    
    async def delete_data(self, interface_path: str, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除数据
        
        Args:
            interface_path: 接口路径
            query_params: 查询参数（删除条件）
            
        Returns:
            删除结果
        """
        try:
            LogUtil.info("开始删除数据", interface_path=interface_path, query_params=query_params)
            
            # 1. 解析接口配置
            interface_config_id = self._parse_interface_config_id(interface_path)
            if not interface_config_id:
                return self._error_response("无效的接口路径")
            
            interface_config = self.interface_repo.get_by_id(interface_config_id)
            if not interface_config:
                return self._error_response("接口配置不存在")
            
            # 2. 解析ORM配置
            orm_config = self._parse_orm_config(interface_config.orm_model_config)
            if not orm_config:
                return self._error_response("ORM配置解析失败")
            
            # 3. 验证删除条件
            validated_conditions = self.dynamic_schema.validate_delete_conditions(orm_config, query_params)
            
            # 4. 执行删除操作
            result = await self.dynamic_model.execute_delete(
                interface_config.datasource_id,
                orm_config,
                validated_conditions
            )
            
            LogUtil.info("删除数据完成", interface_config_id=interface_config_id)
            return result
            
        except Exception as e:
            LogUtil.error("删除数据失败", error=str(e), interface_path=interface_path)
            return self._error_response(f"删除失败: {str(e)}")
    
    async def patch_data(self, interface_path: str, data: Dict[str, Any], query_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        部分更新数据（PATCH）
        
        Args:
            interface_path: 接口路径
            data: 部分更新数据
            query_params: 查询参数（更新条件）
            
        Returns:
            更新结果
        """
        # PATCH与PUT的区别：PATCH只更新提供的字段，PUT是全量更新
        return await self.update_data(interface_path, data, query_params)
    
    async def test_interface(self, interface_config_id: int, method: str, path: str, params: Dict[str, Any], body: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试接口
        
        Args:
            interface_config_id: 接口配置ID
            method: HTTP方法
            path: 接口路径
            params: 查询参数
            body: 请求体
            
        Returns:
            测试结果
        """
        try:
            LogUtil.info("开始接口测试", interface_config_id=interface_config_id, method=method)
            
            # 根据HTTP方法调用相应的处理方法
            if method == 'GET':
                return await self.query_data(path, params)
            elif method == 'POST':
                return await self.create_data(path, body, params)
            elif method == 'PUT':
                return await self.update_data(path, body, params)
            elif method == 'DELETE':
                return await self.delete_data(path, params)
            elif method == 'PATCH':
                return await self.patch_data(path, body, params)
            else:
                return self._error_response(f"不支持的HTTP方法: {method}")
                
        except Exception as e:
            LogUtil.error("接口测试失败", error=str(e), interface_config_id=interface_config_id)
            return self._error_response(f"测试失败: {str(e)}")
    
    async def get_interface_schema(self, interface_config_id: int) -> Dict[str, Any]:
        """
        获取接口数据结构
        
        Args:
            interface_config_id: 接口配置ID
            
        Returns:
            接口结构信息
        """
        try:
            LogUtil.info("获取接口结构", interface_config_id=interface_config_id)
            
            # 获取接口配置
            interface_config = self.interface_repo.get_by_id(interface_config_id)
            if not interface_config:
                return self._error_response("接口配置不存在")
            
            # 解析ORM配置
            orm_config = self._parse_orm_config(interface_config.orm_model_config)
            if not orm_config:
                return self._error_response("ORM配置解析失败")
            
            # 生成接口结构信息
            schema_info = self.dynamic_schema.generate_schema_info(orm_config)
            
            return {
                "success": True,
                "message": "获取接口结构成功",
                "data": {
                    "interface_config_id": interface_config_id,
                    "table_name": orm_config.get("table_name"),
                    "supported_methods": orm_config.get("http_methods", ["GET"]),
                    "fields": schema_info.get("fields", []),
                    "query_capabilities": schema_info.get("query_capabilities", {}),
                    "validation_rules": schema_info.get("validation_rules", {})
                }
            }
            
        except Exception as e:
            LogUtil.error("获取接口结构失败", error=str(e), interface_config_id=interface_config_id)
            return self._error_response(f"获取结构失败: {str(e)}")
    
    def _parse_interface_config_id(self, interface_path: str) -> Optional[int]:
        """
        根据接口路径查找接口配置ID

        Args:
            interface_path: 接口路径，如 "/api/users" 或 "/api/reports/sales"

        Returns:
            接口配置ID
        """
        try:
            LogUtil.info("根据路径查找接口配置", interface_path=interface_path)

            # 根据路径查找接口配置
            interface_config = self.interface_repo.get_by_path(interface_path)
            if interface_config:
                LogUtil.info("找到接口配置", config_id=interface_config.id, path=interface_path)
                return interface_config.id

            LogUtil.warning("未找到匹配的接口配置", interface_path=interface_path)
            return None
        except Exception as e:
            LogUtil.error("查找接口配置失败", error=str(e), interface_path=interface_path)
            return None
    
    def _parse_orm_config(self, orm_model_config: str) -> Optional[Dict[str, Any]]:
        """
        解析ORM配置
        
        Args:
            orm_model_config: ORM配置JSON字符串
            
        Returns:
            解析后的ORM配置字典
        """
        try:
            if not orm_model_config:
                return None
            return json.loads(orm_model_config)
        except json.JSONDecodeError as e:
            LogUtil.error("ORM配置JSON解析失败", error=str(e))
            return None
    
    def _error_response(self, message: str) -> Dict[str, Any]:
        """生成错误响应"""
        return {
            "success": False,
            "message": message,
            "data": None
        }
