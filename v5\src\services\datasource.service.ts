/**
 * 数据源服务 - 已禁用Mock，强制使用API
 *
 * 注意：数据源服务已被接管，不再使用Mock数据
 * 即使环境变量配置错误，也会强制使用真实API服务
 */
import type { DataSource, DataSourceRequest, ConnectionTestResult, DataSourceListResponse } from '../types/datasource';
// import { mockDataSources, mockTestConnection } from '../mock/datasource.mock'; // 仅供Mock类使用，实际不会调用
import { apiClient } from '@/utils/http-client';

/**
 * 数据源服务接口
 */
interface IDataSourceService {
  getDataSources(page?: number, size?: number): Promise<DataSourceListResponse>;
  getDataSourceById(id: number): Promise<DataSource | undefined>;
  createDataSource(data: DataSourceRequest): Promise<DataSource>;
  updateDataSource(id: number, data: DataSourceRequest): Promise<DataSource>;
  deleteDataSource(id: number): Promise<boolean>;
  testConnection(data: DataSourceRequest): Promise<ConnectionTestResult>;
  testSavedDataSourceConnection(id: number): Promise<ConnectionTestResult>;
  validateTableName(datasourceId: number, tableName: string, tableType: string): Promise<any>;
  getTableStructure(datasourceId: number, tableName: string, tableType: string): Promise<any>;
}

/**
 * Mock数据源服务实现 - 已禁用
 * 数据源服务已被接管，不再使用Mock数据
 * 保留代码仅供参考，实际不会被调用
 */
/* 已禁用Mock服务
class MockDataSourceService implements IDataSourceService {
  private dataSources: DataSource[] = [...mockDataSources];
  
  async getDataSources(page: number = 1, size: number = 10): Promise<DataSourceListResponse> {
    const total = this.dataSources.length;
    const pages = Math.ceil(total / size);
    const start = (page - 1) * size;
    const items = this.dataSources.slice(start, start + size);

    return Promise.resolve({
      items,
      total,
      page,
      size,
      pages
    });
  }
  
  async getDataSourceById(id: number): Promise<DataSource | undefined> {
    const dataSource = this.dataSources.find(ds => ds.id === id);
    return Promise.resolve(dataSource ? { ...dataSource } : undefined);
  }


  
  async createDataSource(data: DataSourceRequest): Promise<DataSource> {
    const newId = Math.max(...this.dataSources.map(ds => ds.id), 0) + 1;
    const now = new Date().toLocaleString();
    
    const newDataSource: DataSource = {
      id: newId,
      ...data,
      status: 'active',
      createdAt: now,
      updatedAt: now
    };
    
    this.dataSources.push(newDataSource);
    return Promise.resolve({ ...newDataSource });
  }
  
  async updateDataSource(id: number, data: DataSourceRequest): Promise<DataSource> {
    const index = this.dataSources.findIndex(ds => ds.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`数据源ID ${id} 不存在`));
    }
    
    const now = new Date().toLocaleString();
    const updatedDataSource: DataSource = {
      ...this.dataSources[index],
      ...data,
      updatedAt: now
    };
    
    this.dataSources[index] = updatedDataSource;
    return Promise.resolve({ ...updatedDataSource });
  }
  
  async deleteDataSource(id: number): Promise<boolean> {
    const index = this.dataSources.findIndex(ds => ds.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }
    
    this.dataSources.splice(index, 1);
    return Promise.resolve(true);
  }
  
  async testConnection(data: DataSourceRequest): Promise<ConnectionTestResult> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    return Promise.resolve(mockTestConnection(data.dbType));
  }

  async testSavedDataSourceConnection(id: number): Promise<ConnectionTestResult> {
    // 查找数据源
    const dataSource = this.dataSources.find(ds => ds.id === id);
    if (!dataSource) {
      return Promise.resolve({
        success: false,
        message: '数据源不存在',
        errorDetail: `未找到ID为${id}的数据源`
      });
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    return Promise.resolve(mockTestConnection(dataSource.dbType));
  }
}
*/ // Mock服务注释结束

/**
 * API数据源服务实现 - 使用统一HTTP客户端
 */
class ApiDataSourceService implements IDataSourceService {

  async getDataSources(page: number = 1, size: number = 10): Promise<DataSourceListResponse> {
    return apiClient.get<DataSourceListResponse>('/datasource', { page, size });
  }

  async getDataSourceById(id: number): Promise<DataSource | undefined> {
    try {
      return await apiClient.get<DataSource>(`/datasource/${id}`);
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return undefined;
      }
      throw error;
    }
  }



  async createDataSource(data: DataSourceRequest): Promise<DataSource> {
    return apiClient.post<DataSource>('/datasource', data);
  }

  async updateDataSource(id: number, data: DataSourceRequest): Promise<DataSource> {
    return apiClient.put<DataSource>(`/datasource/${id}`, data);
  }

  async deleteDataSource(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`/datasource/${id}`);
      return true;
    } catch (error) {
      // 重新抛出错误，保持原有的错误处理逻辑
      throw error;
    }
  }

  async testConnection(data: DataSourceRequest): Promise<ConnectionTestResult> {
    return apiClient.post<ConnectionTestResult>('/datasource/test-connection', data);
  }

  async testSavedDataSourceConnection(id: number): Promise<ConnectionTestResult> {
    return apiClient.get<ConnectionTestResult>(`/datasource/${id}/test-connection`);
  }

  async validateTableName(datasourceId: number, tableName: string, tableType: string): Promise<any> {
    return apiClient.post<any>(`/datasource/${datasourceId}/validate-table`, null, {
      params: {
        table_name: tableName,
        table_type: tableType
      }
    });
  }

  async getTableStructure(datasourceId: number, tableName: string, tableType: string): Promise<any> {
    return apiClient.get<any>(`/datasource/${datasourceId}/table-structure`, {
      table_name: tableName,
      table_type: tableType
    });
  }
}

// 数据源服务已被接管，强制使用API服务，不再使用Mock
// const useMock = import.meta.env.VITE_USE_MOCK === 'true';
const dataSourceService: IDataSourceService = new ApiDataSourceService();
// 注释掉Mock服务选择逻辑，确保始终使用真实API
// const dataSourceService: IDataSourceService = useMock
//   ? new MockDataSourceService()
//   : new ApiDataSourceService();

export default dataSourceService;