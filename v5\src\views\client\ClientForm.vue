<template>
  <div class="drawer-form-container">
    <div class="drawer-form-content custom-scrollbar">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
        class="client-form"
      >
        <el-form-item label="客户端名称" prop="clientName">
          <el-input
            v-model="formData.clientName"
            placeholder="请输入客户端名称"
            :disabled="loading"
            maxlength="50"
            show-word-limit
          />
          <div class="form-tip">客户端的显示名称，用于标识不同的应用系统</div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入客户端描述"
            :disabled="loading"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
          <div class="form-tip">详细描述客户端的用途和功能</div>
        </el-form-item>

        <el-form-item label="Token有效期" prop="tokenExpiresIn">
          <el-select
            v-model="formData.tokenExpiresIn"
            placeholder="请选择Token有效期"
            :disabled="loading"
            style="width: 100%"
          >
            <el-option label="1天" :value="86400" />
            <el-option label="7天" :value="604800" />
            <el-option label="30天" :value="2592000" />
            <el-option label="90天" :value="7776000" />
            <el-option label="永不过期" :value="0" />
          </el-select>
          <div class="form-tip important-tip">⚠️ Token的有效期，过期后需要重新获取</div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status" :disabled="loading">
            <el-radio value="enabled">启用</el-radio>
            <el-radio value="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 状态说明 -->
        <el-alert
          title="状态说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="status-description">
              <p><strong>启用：</strong>客户端可以正常访问所有已授权的接口</p>
              <p><strong>禁用：</strong>客户端将无法访问任何接口，所有API请求都会被拒绝</p>
            </div>
          </template>
        </el-alert>


      </el-form>
    </div>

    <!-- 底部按钮 -->
    <DrawerFooter />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore'
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger'
import DrawerFooter from '@/components/common/DrawerFooter.vue'

// 全局状态
const globalDrawerStore = useGlobalDrawerStore()
const drawerMessenger = useGlobalDrawerMessenger()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  clientName: '',
  description: '',
  tokenExpiresIn: 2592000, // 默认30天
  status: 'enabled',
  clientId: '',
  clientSecret: '',
  createdAt: '',
  updatedAt: ''
})

// 计算属性
const isEdit = computed(() => globalDrawerStore.props.isEdit)

// 表单验证规则
const formRules = {
  clientName: [
    { required: true, message: '请输入客户端名称', trigger: 'blur' },
    { min: 2, max: 50, message: '客户端名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' }
  ],
  tokenExpiresIn: [
    { required: true, message: '请选择Token有效期', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    clientName: '',
    description: '',
    tokenExpiresIn: 2592000,
    status: 'enabled',
    clientId: '',
    clientSecret: '',
    createdAt: '',
    updatedAt: ''
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听抽屉属性变化
watch(() => globalDrawerStore.props, (newProps) => {
  if (newProps && newProps.editData) {
    // 编辑模式，填充表单数据
    Object.assign(formData, newProps.editData)
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}, { immediate: true, deep: true })

// 生成客户端ID
const generateClientId = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let result = ''
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成安全密钥
const generateClientSecret = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}



// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // 新增模式需要生成客户端ID和密钥
    if (!isEdit.value) {
      // TODO: 检查客户端ID是否已存在，如果存在则重新生成
      formData.clientId = generateClientId()
      formData.clientSecret = generateClientSecret()
    }

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isEdit.value ? '保存成功' : '创建成功')
    drawerMessenger.hideDrawer()
    
    // 刷新父页面数据
    // TODO: 触发父页面数据刷新
    
  } catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? '保存失败' : '创建失败')
      console.error('提交表单失败:', error)
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer()
}

// 设置抽屉底部按钮
const updateDrawerButtons = () => {
  globalDrawerStore.rightButtons = [
    {
      text: '取消',
      handler: handleCancel
    },
    {
      text: isEdit.value ? '保存' : '创建',
      type: 'primary',
      handler: handleSubmit,
      loading: loading.value,
      loadingText: '提交中...'
    }
  ]
}

// 监听loading状态，更新按钮状态
watch(loading, () => {
  updateDrawerButtons()
})

// 监听编辑状态，更新按钮文字
watch(isEdit, () => {
  updateDrawerButtons()
})

// 初始化按钮
updateDrawerButtons()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 表单标签对齐修复 */
.client-form {
  :deep(.el-form-item__label) {
    text-align: left !important;
    justify-content: flex-start !important;
    padding-left: 0 !important;
  }

  :deep(.el-form-item__label::before) {
    margin-right: 4px !important;
  }
}

/* 状态说明样式 */
.status-description {
  p {
    margin: 4px 0;
    line-height: 1.5;

    strong {
      color: #409eff;
    }
  }
}

/* 重要提示样式 */
.important-tip {
  color: #e6a23c !important;
  font-weight: 500 !important;
  background: #fdf6ec;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
  margin-top: 8px;
}


</style>
