import type { 
  InterfaceTag, 
  InterfaceTagRequest, 
  InterfaceTagListResponse,
  TAG_COLOR_PRESETS
} from '@/types/interface-tag';

/**
 * 接口标签管理 MOCK 数据
 * 用于：InterfaceTag.vue 接口标签管理页面
 */

/**
 * 模拟接口标签列表数据
 */
export const mockInterfaceTags: InterfaceTag[] = [
  {
    id: 1,
    name: '查询类',
    color: '#3FC8DD',
    description: '用于数据查询的接口，如列表查询、详情查询等',
    isEnabled: true,
    interfaceCount: 15,
    createdAt: '2025-07-01 10:30:00',
    updatedAt: '2025-07-10 14:20:00'
  },
  {
    id: 2,
    name: '更新类',
    color: '#67C23A',
    description: '用于数据更新的接口，如新增、修改、删除等',
    isEnabled: true,
    interfaceCount: 8,
    createdAt: '2025-07-02 11:15:00',
    updatedAt: '2025-07-11 16:45:00'
  },
  {
    id: 3,
    name: '审批流程',
    color: '#E6A23C',
    description: '涉及审批流程的接口，如提交审批、审批通过、审批拒绝等',
    isEnabled: false,
    interfaceCount: 12,
    createdAt: '2025-07-03 09:20:00',
    updatedAt: '2025-07-09 10:30:00'
  },
  {
    id: 4,
    name: '报表统计',
    color: '#F56C6C',
    description: '用于报表生成和数据统计的接口',
    isEnabled: true,
    interfaceCount: 6,
    createdAt: '2025-07-04 14:30:00',
    updatedAt: '2025-07-12 11:15:00'
  },
  {
    id: 5,
    name: '文件操作',
    color: '#9C27B0',
    description: '文件上传、下载、预览等相关接口',
    isEnabled: true,
    interfaceCount: 4,
    createdAt: '2025-07-05 16:00:00',
    updatedAt: '2025-07-08 10:00:00'
  },
  {
    id: 6,
    name: '系统配置',
    color: '#17A2B8',
    description: '系统参数配置、字典管理等接口',
    isEnabled: false,
    interfaceCount: 3,
    createdAt: '2025-07-06 10:45:00',
    updatedAt: '2025-07-13 15:30:00'
  },
  {
    id: 7,
    name: '消息通知',
    color: '#909399',
    description: '消息推送、通知发送等相关接口',
    isEnabled: true,
    interfaceCount: 5,
    createdAt: '2025-07-07 13:20:00',
    updatedAt: '2025-07-14 09:45:00'
  },
  {
    id: 8,
    name: '权限验证',
    color: '#409EFF',
    description: '用户权限验证、角色检查等安全相关接口',
    isEnabled: true,
    interfaceCount: 7,
    createdAt: '2025-07-08 15:10:00',
    updatedAt: '2025-07-15 12:25:00'
  }
];

/**
 * 颜色自动分配函数
 */
const getAutoColor = (tagName: string): string => {
  const colors = [
    '#3FC8DD', '#67C23A', '#E6A23C', '#F56C6C', 
    '#9C27B0', '#17A2B8', '#909399', '#409EFF'
  ];
  
  // 基于标签名称生成hash，确保相同名称总是得到相同颜色
  const hash = tagName.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  return colors[Math.abs(hash) % colors.length];
};

/**
 * 模拟获取接口标签列表
 */
export const mockGetInterfaceTags = async (
  page: number = 1,
  pageSize: number = 10,
  search?: string
): Promise<InterfaceTagListResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  let filteredTags = [...mockInterfaceTags];
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    filteredTags = filteredTags.filter(tag => 
      tag.name.toLowerCase().includes(searchLower) ||
      (tag.description && tag.description.toLowerCase().includes(searchLower))
    );
  }
  
  // 分页处理
  const total = filteredTags.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filteredTags.slice(start, end);
  
  return {
    items: data,
    total,
    page,
    size: pageSize,
    pages: Math.ceil(total / pageSize)
  };
};

/**
 * 模拟创建接口标签
 */
export const mockCreateInterfaceTag = async (
  tagData: InterfaceTagRequest
): Promise<InterfaceTag> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 检查标签名称是否重复
  const existingTag = mockInterfaceTags.find(
    tag => tag.name === tagData.name
  );
  if (existingTag) {
    throw new Error(`标签名称 "${tagData.name}" 已存在`);
  }
  
  const newId = Math.max(...mockInterfaceTags.map(t => t.id), 0) + 1;
  const now = new Date().toLocaleString();
  
  const newTag: InterfaceTag = {
    id: newId,
    name: tagData.name,
    color: tagData.color || getAutoColor(tagData.name),
    description: tagData.description,
    isEnabled: tagData.isEnabled !== undefined ? tagData.isEnabled : true,
    interfaceCount: 0,
    createdAt: now,
    updatedAt: now
  };
  
  mockInterfaceTags.push(newTag);
  return { ...newTag };
};

/**
 * 模拟更新接口标签
 */
export const mockUpdateInterfaceTag = async (
  id: number,
  tagData: InterfaceTagRequest
): Promise<InterfaceTag> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const index = mockInterfaceTags.findIndex(tag => tag.id === id);
  if (index === -1) {
    throw new Error(`标签ID ${id} 不存在`);
  }
  
  // 检查标签名称是否重复（排除自己）
  const existingTag = mockInterfaceTags.find(
    tag => tag.name === tagData.name && tag.id !== id
  );
  if (existingTag) {
    throw new Error(`标签名称 "${tagData.name}" 已存在`);
  }
  
  const updatedTag: InterfaceTag = {
    ...mockInterfaceTags[index],
    name: tagData.name,
    color: tagData.color || mockInterfaceTags[index].color,
    description: tagData.description,
    isEnabled: tagData.isEnabled !== undefined ? tagData.isEnabled : mockInterfaceTags[index].isEnabled,
    updatedAt: new Date().toLocaleString()
  };
  
  mockInterfaceTags[index] = updatedTag;
  return { ...updatedTag };
};

/**
 * 模拟删除接口标签
 */
export const mockDeleteInterfaceTag = async (id: number): Promise<boolean> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const tag = mockInterfaceTags.find(t => t.id === id);
  if (!tag) {
    throw new Error(`标签ID ${id} 不存在`);
  }
  
  // 注意：按照需求，删除标签时不检查关联，直接删除
  const index = mockInterfaceTags.findIndex(t => t.id === id);
  mockInterfaceTags.splice(index, 1);
  
  return true;
};
