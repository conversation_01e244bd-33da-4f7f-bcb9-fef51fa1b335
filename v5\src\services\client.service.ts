import type { 
  Client, 
  ClientFormD<PERSON>, 
  ClientQuery, 
  ClientListResponse,
  ClientPermission,
  ClientPermissionFormData
} from '../types/client';
import { mockClients, mockClientPermissions, mockInterfaceTree } from '../mock/client.mock';

/**
 * 客户端服务接口
 */
interface IClientService {
  getClients(params: ClientQuery): Promise<ClientListResponse>;
  getClientById(id: number): Promise<Client | undefined>;
  createClient(data: ClientFormData): Promise<Client>;
  updateClient(id: number, data: ClientFormData): Promise<Client>;
  deleteClient(id: number): Promise<boolean>;
  toggleClientStatus(id: number, status: 'enabled' | 'disabled'): Promise<Client>;
  regenerateClientSecret(id: number): Promise<{ clientSecret: string }>;
  getClientPermissions(clientId: string): Promise<ClientPermission[]>;
  updateClientPermissions(clientId: string, data: ClientPermissionFormData): Promise<void>;
  getInterfaceTree(): Promise<any[]>;
}

/**
 * Mock客户端服务实现
 */
class MockClientService implements IClientService {
  private clients: Client[] = [...mockClients];
  private permissions: ClientPermission[] = [...mockClientPermissions];
  
  async getClients(params: ClientQuery): Promise<ClientListResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredData = [...this.clients];

    // 搜索过滤
    if (params.clientName) {
      const keyword = params.clientName.toLowerCase();
      filteredData = filteredData.filter(item =>
        item.clientName.toLowerCase().includes(keyword) ||
        item.clientId.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      );
    }

    // 状态过滤
    if (params.status) {
      filteredData = filteredData.filter(item => item.status === params.status);
    }

    // 分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 20;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);

    return {
      data: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    };
  }
  
  async getClientById(id: number): Promise<Client | undefined> {
    const client = this.clients.find(item => item.id === id);
    return Promise.resolve(client ? { ...client } : undefined);
  }
  
  async createClient(data: ClientFormData): Promise<Client> {
    const newId = Math.max(...this.clients.map(c => c.id), 0) + 1;
    const now = new Date().toLocaleString('zh-CN');
    
    const newClient: Client = {
      id: newId,
      clientId: data.clientId || this.generateClientId(),
      clientSecret: data.clientSecret || this.generateClientSecret(),
      clientName: data.clientName,
      description: data.description || '',
      tokenExpiresIn: data.tokenExpiresIn,
      status: data.status,
      createdAt: now,
      updatedAt: now
    };
    
    this.clients.unshift(newClient);
    return Promise.resolve({ ...newClient });
  }
  
  async updateClient(id: number, data: ClientFormData): Promise<Client> {
    const index = this.clients.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`客户端ID ${id} 不存在`));
    }
    
    const now = new Date().toLocaleString('zh-CN');
    const updatedClient: Client = {
      ...this.clients[index],
      clientName: data.clientName,
      description: data.description || '',
      tokenExpiresIn: data.tokenExpiresIn,
      status: data.status,
      updatedAt: now
    };
    
    this.clients[index] = updatedClient;
    return Promise.resolve({ ...updatedClient });
  }
  
  async deleteClient(id: number): Promise<boolean> {
    const index = this.clients.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }
    
    this.clients.splice(index, 1);
    return Promise.resolve(true);
  }
  
  async toggleClientStatus(id: number, status: 'enabled' | 'disabled'): Promise<Client> {
    const index = this.clients.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`客户端ID ${id} 不存在`));
    }
    
    this.clients[index].status = status;
    this.clients[index].updatedAt = new Date().toLocaleString('zh-CN');
    
    return Promise.resolve({ ...this.clients[index] });
  }
  
  async regenerateClientSecret(id: number): Promise<{ clientSecret: string }> {
    const index = this.clients.findIndex(item => item.id === id);
    if (index === -1) {
      return Promise.reject(new Error(`客户端ID ${id} 不存在`));
    }
    
    const newSecret = this.generateClientSecret();
    this.clients[index].clientSecret = newSecret;
    this.clients[index].updatedAt = new Date().toLocaleString('zh-CN');
    
    return Promise.resolve({ clientSecret: newSecret });
  }
  
  async getClientPermissions(clientId: string): Promise<ClientPermission[]> {
    return Promise.resolve(this.permissions.filter(p => p.clientId === clientId));
  }
  
  async updateClientPermissions(clientId: string, data: ClientPermissionFormData): Promise<void> {
    // 删除旧权限
    this.permissions = this.permissions.filter(p => p.clientId !== clientId);
    
    // 添加新权限
    data.permissions.forEach((permission, index) => {
      this.permissions.push({
        id: Date.now() + index,
        clientId,
        resourceType: permission.resourceType,
        resourceId: permission.resourceId,
        createdAt: new Date().toLocaleString('zh-CN')
      });
    });
    
    return Promise.resolve();
  }
  
  async getInterfaceTree(): Promise<any[]> {
    return Promise.resolve([...mockInterfaceTree]);
  }
  
  private generateClientId(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < 16; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  private generateClientSecret(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

/**
 * API客户端服务实现
 */
class ApiClientService implements IClientService {
  private baseUrl = '/api/clients';

  async getClients(params: ClientQuery): Promise<ClientListResponse> {
    const response = await fetch(`${this.baseUrl}?${new URLSearchParams(params as any)}`);
    if (!response.ok) throw new Error('获取客户端列表失败');
    return response.json();
  }

  async getClientById(id: number): Promise<Client | undefined> {
    const response = await fetch(`${this.baseUrl}/${id}`);
    if (response.status === 404) return undefined;
    if (!response.ok) throw new Error(`获取客户端ID ${id} 失败`);
    return response.json();
  }

  async createClient(data: ClientFormData): Promise<Client> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('创建客户端失败');
    return response.json();
  }

  async updateClient(id: number, data: ClientFormData): Promise<Client> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error(`更新客户端ID ${id} 失败`);
    return response.json();
  }

  async deleteClient(id: number): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE'
    });
    return response.ok;
  }

  async toggleClientStatus(id: number, status: 'enabled' | 'disabled'): Promise<Client> {
    const response = await fetch(`${this.baseUrl}/${id}/status`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status })
    });
    if (!response.ok) throw new Error(`切换客户端状态失败`);
    return response.json();
  }

  async regenerateClientSecret(id: number): Promise<{ clientSecret: string }> {
    const response = await fetch(`${this.baseUrl}/${id}/regenerate-secret`, {
      method: 'POST'
    });
    if (!response.ok) throw new Error('重新生成密钥失败');
    return response.json();
  }

  async getClientPermissions(clientId: string): Promise<ClientPermission[]> {
    const response = await fetch(`${this.baseUrl}/${clientId}/permissions`);
    if (!response.ok) throw new Error('获取客户端权限失败');
    return response.json();
  }

  async updateClientPermissions(clientId: string, data: ClientPermissionFormData): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${clientId}/permissions`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('更新客户端权限失败');
  }

  async getInterfaceTree(): Promise<any[]> {
    const response = await fetch('/api/interfaces/tree');
    if (!response.ok) throw new Error('获取接口树失败');
    return response.json();
  }
}

// 根据环境变量选择服务实现
// 临时修复：客户端服务暂时使用Mock模式，等后端客户端API完成后再切换
const useMockForClient = true; // 暂时使用Mock
const clientService: IClientService = useMockForClient
  ? new MockClientService()
  : new ApiClientService();

export default clientService;
