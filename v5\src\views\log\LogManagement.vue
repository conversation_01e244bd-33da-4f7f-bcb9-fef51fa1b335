<template>
  <div class="page-container">
    <h2 class="page-title">
      <el-icon><Document /></el-icon>
      日志管理
    </h2>
    
    <div class="content-container">
      <!-- 日志查询 -->
      <div class="section-title">
        <el-icon><Search /></el-icon>
        日志查询
      </div>
      
      <div class="query-card">
        <el-form :model="queryForm" label-width="100px" inline>
          <el-form-item label="日志类型">
            <el-select v-model="queryForm.type" placeholder="选择日志类型">
              <el-option label="全部" value=""></el-option>
              <el-option label="访问日志" value="access"></el-option>
              <el-option label="错误日志" value="error"></el-option>
              <el-option label="系统日志" value="system"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="日志级别">
            <el-select v-model="queryForm.level" placeholder="选择日志级别">
              <el-option label="全部" value=""></el-option>
              <el-option label="INFO" value="info"></el-option>
              <el-option label="WARN" value="warn"></el-option>
              <el-option label="ERROR" value="error"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input v-model="queryForm.keyword" placeholder="搜索关键词"></el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="searchLogs" icon="Search">查询</el-button>
            <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            <el-button type="danger" @click="clearLogs" icon="Delete">清空日志</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 日志列表 -->
      <div class="section-title">
        <el-icon><List /></el-icon>
        日志列表
      </div>
      
      <div class="log-card">
        <el-table :data="logs" style="width: 100%; min-width: 900px;" v-loading="loading" :scroll-x="true">
          <!-- 空状态 -->
          <template #empty>
            <el-empty
              description="暂无日志数据"
              :image-size="120"
            >
              <template #description>
                <p>当前时间范围内没有日志记录</p>
                <p>请调整查询条件或等待系统产生新的日志</p>
              </template>
              <el-button type="primary" @click="resetQuery">
                <el-icon><Refresh /></el-icon>
                重置查询条件
              </el-button>
            </el-empty>
          </template>
          <el-table-column prop="timestamp" label="时间" width="180">
            <template #default="scope">
              {{ formatTime(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="100">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)">{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="80">
            <template #default="scope">
              <el-tag :type="getLevelColor(scope.row.level)" size="small">{{ scope.row.level }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="消息" min-width="300"></el-table-column>
          <el-table-column prop="source" label="来源" width="150"></el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 20px; text-align: right;">
        </el-pagination>
      </div>
      
      <!-- 日志详情对话框 -->
      <el-dialog v-model="detailVisible" title="日志详情" width="800px">
        <div v-if="currentLog">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="时间">{{ formatTime(currentLog.timestamp) }}</el-descriptions-item>
            <el-descriptions-item label="类型">
              <el-tag :type="getTypeColor(currentLog.type)">{{ currentLog.type }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="级别">
              <el-tag :type="getLevelColor(currentLog.level)">{{ currentLog.level }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="来源">{{ currentLog.source }}</el-descriptions-item>
            <el-descriptions-item label="消息" span="2">{{ currentLog.message }}</el-descriptions-item>
            <el-descriptions-item label="详细信息" span="2">
              <pre class="log-detail">{{ currentLog.details }}</pre>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Document, Search, List, Refresh, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 加载状态
const loading = ref(false)

// 详情对话框
const detailVisible = ref(false)
const currentLog = ref<any>(null)

// 查询表单
const queryForm = ref({
  type: '',
  level: '',
  dateRange: [],
  keyword: ''
})

// 分页
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

// 日志列表
const logs = ref([
  {
    id: 1,
    timestamp: '2023-12-15 14:30:25',
    type: 'access',
    level: 'info',
    message: 'GET /api/user/info - 200 OK',
    source: 'API Gateway',
    details: 'User: admin, IP: *************, Response Time: 156ms'
  },
  {
    id: 2,
    timestamp: '2023-12-15 14:28:15',
    type: 'error',
    level: 'error',
    message: 'Database connection failed',
    source: 'Database Service',
    details: 'Connection timeout after 30 seconds\nHost: *************:3306\nDatabase: production'
  },
  {
    id: 3,
    timestamp: '2023-12-15 14:25:10',
    type: 'system',
    level: 'warn',
    message: 'High memory usage detected',
    source: 'System Monitor',
    details: 'Memory usage: 85%\nAvailable: 2.1GB\nTotal: 16GB'
  }
])

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    access: 'success',
    error: 'danger',
    system: 'warning'
  }
  return colors[type] || 'info'
}

// 获取级别颜色
const getLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    info: 'success',
    warn: 'warning',
    error: 'danger'
  }
  return colors[level] || 'info'
}

// 搜索日志
const searchLogs = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 1000)
}

// 重置查询
const resetQuery = () => {
  queryForm.value = {
    type: '',
    level: '',
    dateRange: [],
    keyword: ''
  }
  searchLogs()
}

// 清空日志
const clearLogs = () => {
  ElMessageBox.confirm('确定要清空所有日志吗？此操作不可恢复！', '确认清空', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    logs.value = []
    ElMessage.success('日志已清空')
  })
}

// 查看详情
const viewDetail = (log: any) => {
  currentLog.value = log
  detailVisible.value = true
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  searchLogs()
}

const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  searchLogs()
}

// 初始化
onMounted(() => {
  pagination.value.total = logs.value.length
})
</script>

<style scoped>
.page-container {
  max-width: 99%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
}

.page-title {
  font-size: 18px;
  color: var(--primary);
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.content-container {
  padding: 20px 30px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--primary);
  display: flex;
  align-items: center;
  border-left: 3px solid var(--primary);
  padding-left: 12px;
}

.query-card, .log-card {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 25px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.log-detail {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
}
</style>
